-- Создание расширений
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Таблица пользователей (расширение ejabberd users)
CREATE TABLE IF NOT EXISTS users_extended (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    server VARCHAR(255) NOT NULL DEFAULT 'localhost',
    display_name VARCHAR(255),
    avatar_url TEXT,
    phone VARCHAR(20),
    email VARCHAR(255),
    status VARCHAR(50) DEFAULT 'offline',
    last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(username, server)
);

-- Таблица файлов
CREATE TABLE IF NOT EXISTS files (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    filename VARCHAR(255) NOT NULL,
    original_name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    content_type VARCHAR(100),
    size_bytes BIGINT,
    s3_key VARCHAR(500) NOT NULL,
    s3_bucket VARCHAR(100) DEFAULT 'messenger-files',
    uploaded_by VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Таблица групповых чатов (дополнительная информация к MUC)
CREATE TABLE IF NOT EXISTS group_chats (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    jid VARCHAR(255) NOT NULL UNIQUE, -- <EMAIL>
    name VARCHAR(255) NOT NULL,
    description TEXT,
    avatar_url TEXT,
    created_by VARCHAR(255) NOT NULL,
    max_members INTEGER DEFAULT 500,
    is_public BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Таблица участников групп
CREATE TABLE IF NOT EXISTS group_members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    group_id UUID REFERENCES group_chats(id) ON DELETE CASCADE,
    user_jid VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'member', -- admin, moderator, member
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(group_id, user_jid)
);

-- Таблица звонков
CREATE TABLE IF NOT EXISTS calls (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    livekit_room_id VARCHAR(255) NOT NULL,
    call_type VARCHAR(20) NOT NULL, -- audio, video
    initiator_jid VARCHAR(255) NOT NULL,
    participants JSONB DEFAULT '[]', -- список JID участников
    status VARCHAR(20) DEFAULT 'initiated', -- initiated, ongoing, ended, declined
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ended_at TIMESTAMP WITH TIME ZONE,
    duration_seconds INTEGER DEFAULT 0
);

-- Таблица уведомлений
CREATE TABLE IF NOT EXISTS notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_jid VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL, -- message, call, group_invite
    title VARCHAR(255),
    body TEXT,
    data JSONB DEFAULT '{}',
    is_read BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    INDEX(user_jid, is_read)
);

-- Индексы для оптимизации
CREATE INDEX IF NOT EXISTS idx_users_extended_username ON users_extended(username);
CREATE INDEX IF NOT EXISTS idx_users_extended_status ON users_extended(status);
CREATE INDEX IF NOT EXISTS idx_files_uploaded_by ON files(uploaded_by);
CREATE INDEX IF NOT EXISTS idx_group_members_group_id ON group_members(group_id);
CREATE INDEX IF NOT EXISTS idx_group_members_user_jid ON group_members(user_jid);
CREATE INDEX IF NOT EXISTS idx_calls_livekit_room ON calls(livekit_room_id);
CREATE INDEX IF NOT EXISTS idx_calls_initiator ON calls(initiator_jid);
CREATE INDEX IF NOT EXISTS idx_notifications_user ON notifications(user_jid, is_read);

-- Триггер для обновления updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_extended_updated_at BEFORE UPDATE ON users_extended
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_group_chats_updated_at BEFORE UPDATE ON group_chats
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();