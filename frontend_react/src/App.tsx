import Box from "@mui/material/Box";
import CssBaseline from "@mui/material/CssBaseline";
import { createTheme, ThemeProvider } from "@mui/material/styles";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import type React from "react";
import LoginScreen from "./components/auth/LoginScreen";
import MainLayout from "./components/layout/MainLayout";
import { CallProvider } from "./contexts/CallProvider";
import { XMPPProvider } from "./contexts/XMPPProvider";
import { useAuthStore } from "./stores/authStore";

// WhatsApp-подобная цветовая схема
const theme = createTheme({
	palette: {
		mode: "light",
		primary: {
			main: "#00a884", // WhatsApp зеленый
			light: "#26d366",
			dark: "#008069",
		},
		secondary: {
			main: "#128c7e",
		},
		background: {
			default: "#f0f2f5",
			paper: "#ffffff",
		},
		text: {
			primary: "#111b21",
			secondary: "#667781",
		},
	},
	typography: {
		fontFamily: '"Segoe UI", "Helvetica Neue", Arial, sans-serif',
		h6: {
			fontWeight: 600,
		},
	},
	components: {
		MuiCssBaseline: {
			styleOverrides: {
				body: {
					margin: 0,
					padding: 0,
					height: "100vh",
					overflow: "hidden",
				},
				"#root": {
					height: "100vh",
					display: "flex",
					flexDirection: "column",
				},
			},
		},
	},
});

// Create a client
const queryClient = new QueryClient({
	defaultOptions: {
		queries: {
			retry: 1,
			refetchOnWindowFocus: false,
		},
	},
});

const AppContent: React.FC = () => {
	const { isAuthenticated } = useAuthStore();

	return (
		<Box height="100vh" bgcolor="background.default">
			{isAuthenticated ? <MainLayout /> : <LoginScreen />}
		</Box>
	);
};

const App: React.FC = () => {
	return (
		<QueryClientProvider client={queryClient}>
			<ThemeProvider theme={theme}>
				<CssBaseline />
				<XMPPProvider>
					<CallProvider>
						<AppContent />
					</CallProvider>
				</XMPPProvider>
			</ThemeProvider>
			<ReactQueryDevtools initialIsOpen={false} />
		</QueryClientProvider>
	);
};

export default App;
