import {
	ArrowBack,
	AttachFile,
	Circle,
	EmojiEmotions,
	MoreV<PERSON>,
	Send,
} from "@mui/icons-material";
import {
	Avatar,
	Box,
	Chip,
	IconButton,
	InputAdornment,
	List,
	ListItem,
	Paper,
	TextField,
	Typography,
	useTheme,
} from "@mui/material";
import { formatDistanceToNow } from "date-fns";
import { ru } from "date-fns/locale";
import React, { useEffect, useRef, useState } from "react";
import { useAuthStore } from "../../stores/authStore";
import type { Contact, Message } from "../../stores/chatStore";
import { useChatStore } from "../../stores/chatStore";
import { useXMPPStore } from "../../stores/xmppStore";

interface ChatAreaProps {
	selectedContact: Contact | null;
	onBackToContacts?: () => void;
}

const ChatArea: React.FC<ChatAreaProps> = ({
	selectedContact,
	onBackToContacts,
}) => {
	const theme = useTheme();
	const { user } = useAuthStore();
	const { messages, currentChatJid, setCurrentChat, markMessagesAsRead } = useChatStore();
	const { isConnected, fetchHistory, sendMessage } = useXMPPStore();

	const [newMessage, setNewMessage] = useState("");
	const [isTyping, setIsTyping] = useState(false);
	const messagesEndRef = useRef<HTMLDivElement>(null);

	// Set current chat JID when a contact is selected
	useEffect(() => {
		if (selectedContact) {
			setCurrentChat(selectedContact.userJid);
		} else {
			setCurrentChat(null);
		}
	}, [selectedContact, setCurrentChat]);

	// Fetch message history when a chat is selected and the client is connected
	useEffect(() => {
		if (currentChatJid && isConnected) {
			fetchHistory(currentChatJid);
			markMessagesAsRead(currentChatJid);
		}
	}, [currentChatJid, isConnected, fetchHistory, markMessagesAsRead]);

	// Auto-scroll to bottom when messages change
	useEffect(() => {
		scrollToBottom();
	}, [messages]);

	const currentChatMessages = useChatStore((state) => state.messages);

	const scrollToBottom = () => {
		messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
	};

	const handleSendMessage = async () => {
		if (!newMessage.trim() || !selectedContact || !user) return;

		try {
			sendMessage(selectedContact.userJid, newMessage.trim());
			setNewMessage("");
		} catch (error) {
			console.error("Error sending message:", error);
		}
	};

	const handleKeyPress = (e: React.KeyboardEvent) => {
		if (e.key === "Enter" && !e.shiftKey) {
			e.preventDefault();
			handleSendMessage();
		}
	};

	const formatMessageTime = (timestamp: Date) => {
		const now = new Date();
		const messageDate = new Date(timestamp);

		if (now.toDateString() === messageDate.toDateString()) {
			return messageDate.toLocaleTimeString("ru-RU", {
				hour: "2-digit",
				minute: "2-digit",
			});
		} else {
			return messageDate.toLocaleDateString("ru-RU", {
				day: "2-digit",
				month: "2-digit",
				hour: "2-digit",
				minute: "2-digit",
			});
		}
	};

	const getContactStatus = () => {
		if (!selectedContact) return "";

		if (selectedContact.isOnline) {
			return "в сети";
		}

		if (selectedContact.lastSeen) {
			try {
				return `был(а) ${formatDistanceToNow(selectedContact.lastSeen, {
					addSuffix: true,
					locale: ru,
				})}`;
			} catch {
				return "был(а) давно";
			}
		}

		return "был(а) давно";
	};

	if (!selectedContact) {
		return (
			<Box
				display="flex"
				flexDirection="column"
				alignItems="center"
				justifyContent="center"
				height="100%"
				bgcolor="background.default"
				p={3}
				textAlign="center"
			>
				<Box
					component="img"
					src="/whatsapp-web-bg.svg"
					alt="WhatsApp Web"
					sx={{
						width: 320,
						height: 200,
						opacity: 0.1,
						mb: 3,
					}}
				/>
				<Typography variant="h5" color="text.secondary" gutterBottom>
					WhatsApp Web
				</Typography>
				<Typography variant="body1" color="text.secondary" maxWidth={400}>
					Отправляйте и получайте сообщения без необходимости держать телефон
					подключенным к интернету.
				</Typography>
			</Box>
		);
	}

	return (
		<Box display="flex" flexDirection="column" height="100%">
			{/* Chat Header */}
			<Paper
				elevation={1}
				sx={{
					display: "flex",
					alignItems: "center",
					p: 2,
					bgcolor: "background.paper",
					borderRadius: 0,
				}}
			>
				{onBackToContacts && (
					<IconButton onClick={onBackToContacts} sx={{ mr: 1 }}>
						<ArrowBack />
					</IconButton>
				)}

				<Avatar
					src={selectedContact.avatar}
					sx={{ width: 40, height: 40, mr: 2 }}
				>
					{selectedContact.displayName.charAt(0).toUpperCase()}
				</Avatar>

				<Box flex={1}>
					<Typography variant="h6" noWrap>
						{selectedContact.displayName}
					</Typography>
					<Box display="flex" alignItems="center" gap={1}>
						{selectedContact.isOnline && (
							<Circle sx={{ fontSize: 8, color: "#4caf50" }} />
						)}
						<Typography variant="caption" color="text.secondary">
							{isTyping ? "печатает..." : getContactStatus()}
						</Typography>
					</Box>
				</Box>

				<IconButton>
					<MoreVert />
				</IconButton>
			</Paper>

			{/* Messages Area */}
			<Box
				flex={1}
				overflow="auto"
				sx={{
					backgroundImage: "url(/chat-bg.png)",
					backgroundRepeat: "repeat",
					backgroundSize: "412px 749px",
				}}
			>
				{currentChatMessages.length === 0 ? (
					<Box
						display="flex"
						justifyContent="center"
						alignItems="center"
						height="100%"
						p={3}
					>
						<Chip
							label="Начните разговор"
							variant="outlined"
							sx={{ bgcolor: "background.paper" }}
						/>
					</Box>
				) : (
					<List sx={{ p: 1 }}>
						{currentChatMessages.map((message, index) => (
							<ListItem
								key={message.messageId || index}
								sx={{
									display: "flex",
									justifyContent: message.isOwn ? "flex-end" : "flex-start",
									mb: 1,
								}}
							>
								<Paper
									elevation={1}
									sx={{
										maxWidth: "70%",
										p: 1.5,
										bgcolor: message.isOwn ? "#dcf8c6" : "background.paper",
										borderRadius: 2,
										borderTopRightRadius: message.isOwn ? 0 : 2,
										borderTopLeftRadius: message.isOwn ? 2 : 0,
									}}
								>
									<Typography variant="body1" sx={{ mb: 0.5 }}>
										{message.body}
									</Typography>
									<Typography
										variant="caption"
										color="text.secondary"
										sx={{ display: "block", textAlign: "right" }}
									>
										{formatMessageTime(message.timestamp)}
									</Typography>
								</Paper>
							</ListItem>
						))}
						<div ref={messagesEndRef} />
					</List>
				)}
			</Box>

			{/* Message Input */}
			<Paper
				elevation={1}
				sx={{
					display: "flex",
					alignItems: "center",
					p: 1,
					bgcolor: "background.paper",
					borderRadius: 0,
				}}
			>
				<IconButton>
					<EmojiEmotions />
				</IconButton>

				<IconButton>
					<AttachFile />
				</IconButton>

				<TextField
					fullWidth
					multiline
					maxRows={4}
					placeholder="Введите сообщение"
					variant="outlined"
					size="small"
					value={newMessage}
					onChange={(e) => setNewMessage(e.target.value)}
					onKeyPress={handleKeyPress}
					sx={{
						mx: 1,
						"& .MuiOutlinedInput-root": {
							borderRadius: 3,
						},
					}}
				/>

				<IconButton
					color="primary"
					onClick={handleSendMessage}
					disabled={!newMessage.trim()}
				>
					<Send />
				</IconButton>
			</Paper>
		</Box>
	);
};

export default ChatArea;
