import { Box, useMediaQuery, useTheme } from "@mui/material";
import type React from "react";
import { useState } from "react";
import type { Contact } from "../../stores/chatStore";
import CallModal from "../call/CallModal";
import ChatArea from "./ChatArea";
import Sidebar from "./Sidebar";

const MainLayout: React.FC = () => {
	const theme = useTheme();
	const isMobile = useMediaQuery(theme.breakpoints.down("md"));

	const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
	const [showChatArea, setShowChatArea] = useState(false);

	const handleContactSelect = (contact: Contact) => {
		setSelectedContact(contact);
		if (isMobile) {
			setShowChatArea(true);
		}
	};

	const handleBackToContacts = () => {
		setShowChatArea(false);
		setSelectedContact(null);
	};

	return (
		<Box
			display="flex"
			height="100vh"
			bgcolor="background.default"
			overflow="hidden"
		>
			{/* Sidebar - список контактов */}
			<Box
				sx={{
					width: isMobile ? "100%" : 400,
					minWidth: isMobile ? "auto" : 320,
					maxWidth: isMobile ? "100%" : 500,
					display: isMobile && showChatArea ? "none" : "flex",
					flexDirection: "column",
					borderRight: isMobile ? "none" : "1px solid",
					borderColor: "divider",
					bgcolor: "background.paper",
				}}
			>
				<Sidebar
					selectedContact={selectedContact}
					onContactSelect={handleContactSelect}
				/>
			</Box>

			{/* Chat Area - область чата */}
			<Box
				sx={{
					flex: 1,
					display: isMobile && !showChatArea ? "none" : "flex",
					flexDirection: "column",
					bgcolor: "background.default",
				}}
			>
				<ChatArea
					selectedContact={selectedContact}
					onBackToContacts={isMobile ? handleBackToContacts : undefined}
				/>
			</Box>

			{/* Call Modal */}
			<CallModal />
		</Box>
	);
};

export default MainLayout;
