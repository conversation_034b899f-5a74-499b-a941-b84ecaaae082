import { Button, Box, Typography, Paper } from "@mui/material";
import React from "react";
import { useChatStore } from "../../stores/chatStore";
import { useXMPPStore } from "../../stores/xmppStore";

const XMPPTest: React.FC = () => {
	const { isConnected, sendMessage, sendPresence } = useXMPPStore();
	const { messages } = useChatStore();

	const handleTestMessage = () => {
		sendMessage("admin@localhost", "Тестовое сообщение через XMPP");
	};

	const handleTestPresence = () => {
		sendPresence("chat", "Доступен для общения");
	};

	return (
		<Paper sx={{ p: 2, m: 2 }}>
			<Typography variant="h6" gutterBottom>
				XMPP Тест
			</Typography>
			<Box display="flex" flexDirection="column" gap={1}>
				<Typography variant="body2" color={isConnected ? "success.main" : "error.main"}>
					Статус: {isConnected ? "✅ Подключен" : "❌ Отключен"}
				</Typography>
				<Typography variant="body2" color="text.secondary">
					Сообщений получено (в текущем чате): {messages.length}
				</Typography>
				<Button
					variant="outlined"
					onClick={handleTestMessage}
					disabled={!isConnected}
					size="small"
				>
					Отправить тестовое сообщение
				</Button>
				<Button
					variant="outlined"
					onClick={handleTestPresence}
					disabled={!isConnected}
					size="small"
				>
					Обновить статус присутствия
				</Button>
			</Box>
		</Paper>
	);
};

export default XMPPTest;