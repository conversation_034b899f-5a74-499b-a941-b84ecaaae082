import {
	Add,
	Circle,
	ExitToApp,
	Message,
	MoreVert,
	PersonAdd,
	Phone,
	Search,
	Settings,
	VideoCall,
} from "@mui/icons-material";
import {
	Avatar,
	Badge,
	Box,
	Button,
	Chip,
	CircularProgress,
	Dialog,
	DialogActions,
	DialogContent,
	DialogTitle,
	Divider,
	IconButton,
	InputAdornment,
	List,
	ListItem,
	ListItemAvatar,
	ListItemButton,
	ListItemText,
	Menu,
	MenuItem,
	TextField,
	Typography,
} from "@mui/material";
import { formatDistanceToNow } from "date-fns";
import { ru } from "date-fns/locale";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { useCall } from "../../contexts/CallProvider";
import { useXMPP } from "../../contexts/XMPPProvider";
import {
	useAddContact,
	useChats,
	useContacts,
	useSearchUsers,
} from "../../hooks/useApi";
import { useAuthStore } from "../../stores/authStore";
import type { Contact } from "../../stores/chatStore";
import { useChatStore } from "../../stores/chatStore";
import TestContacts from "./TestContacts";
import XMPPTest from "./XMPPTest";

interface SidebarProps {
	selectedContact: Contact | null;
	onContactSelect: (contact: Contact) => void;
}

const Sidebar: React.FC<SidebarProps> = ({
	selectedContact,
	onContactSelect,
}) => {
	const { user, clearAuth } = useAuthStore();
	const { addContact: addContactToStore } = useChatStore();
	const { isConnected } = useXMPP();
	const { initiateCall } = useCall();

	const addContactMutation = useAddContact();
	const { data: serverContacts = [], refetch: refetchContacts } = useContacts();
	const { data: serverChats = [], refetch: refetchChats } = useChats();

	// Convert server chats to local format (prioritize chats over contacts)
	const contacts = React.useMemo(() => {
		// First, get contacts from chats
		const chatContacts = serverChats.map((chat) => ({
			userJid: chat.chat_jid,
			username: chat.chat_jid.split("@")[0],
			displayName: chat.chat_name,
			isOnline: false, // Will be updated by WebSocket
			unreadCount: chat.unread_count,
			lastMessage: chat.last_message_body,
			lastMessageTime: chat.last_message_at
				? new Date(chat.last_message_at)
				: undefined,
			isPinned: chat.is_pinned,
		}));

		// Then add contacts that don't have chats yet
		const chatJids = new Set(chatContacts.map((c) => c.userJid));
		const additionalContacts = serverContacts
			.filter((contact) => !chatJids.has(contact.contact_jid))
			.map((contact) => ({
				userJid: contact.contact_jid,
				username: contact.contact_username,
				displayName: contact.contact_display_name,
				isOnline: false,
				unreadCount: contact.unread_count,
				lastMessage: contact.last_message_preview,
				lastMessageTime: contact.last_message_at
					? new Date(contact.last_message_at)
					: undefined,
				isPinned: contact.is_pinned,
			}));

		// Combine and sort by last message time
		const allContacts = [...chatContacts, ...additionalContacts];
		return allContacts.sort((a, b) => {
			if (a.isPinned && !b.isPinned) return -1;
			if (!a.isPinned && b.isPinned) return 1;

			const aTime = a.lastMessageTime?.getTime() || 0;
			const bTime = b.lastMessageTime?.getTime() || 0;
			return bTime - aTime;
		});
	}, [serverChats, serverContacts]);

	const [searchQuery, setSearchQuery] = useState("");
	const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);
	const [filteredContacts, setFilteredContacts] = useState<Contact[]>([]);
	const [showSearchDialog, setShowSearchDialog] = useState(false);

	// Use React Query for search
	const { data: searchResults = [], isLoading: isSearching } = useSearchUsers(
		searchQuery,
		searchQuery.length >= 2,
	);

	// Filter contacts based on search query
	useEffect(() => {
		if (!searchQuery.trim()) {
			setFilteredContacts(contacts);
		} else {
			const filtered = contacts.filter(
				(contact) =>
					contact.displayName
						.toLowerCase()
						.includes(searchQuery.toLowerCase()) ||
					contact.username.toLowerCase().includes(searchQuery.toLowerCase()),
			);
			setFilteredContacts(filtered);
		}
	}, [contacts, searchQuery]);

	// Filter search results to exclude existing contacts
	const filteredSearchResults = React.useMemo(() => {
		if (!searchResults) return [];
		const existingJids = new Set(contacts.map((c) => c.userJid));
		return searchResults
			.filter((user) => !existingJids.has(`${user.username}@localhost`))
			.map((user) => ({
				userJid: `${user.username}@localhost`,
				username: user.username,
				displayName: user.display_name,
				avatar: user.avatar_url,
			}));
	}, [searchResults, contacts]);

	const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
		setMenuAnchor(event.currentTarget);
	};

	const handleMenuClose = () => {
		setMenuAnchor(null);
	};

	const handleLogout = () => {
		handleMenuClose();
		clearAuth();
	};

	const handleAddContact = async (user: Contact) => {
		try {
			await addContactMutation.mutateAsync(user.username);

			// Refetch both contacts and chats from server
			refetchContacts();
			refetchChats();

			// Add to local store for immediate UI update
			addContactToStore({
				userJid: user.userJid,
				username: user.username,
				displayName: user.displayName,
				avatar: user.avatar,
				isOnline: false,
			});
		} catch (error) {
			console.error("Error adding contact:", error);
		}
	};

	const handleCall = async (contact: Contact, callType: "audio" | "video") => {
		try {
			await initiateCall([contact.userJid], callType);
		} catch (error) {
			console.error("Error initiating call:", error);
		}
	};

	const openSearchDialog = () => {
		setShowSearchDialog(true);
	};

	const closeSearchDialog = () => {
		setShowSearchDialog(false);
		setSearchQuery("");
	};

	const formatLastSeen = (lastSeen?: Date) => {
		if (!lastSeen) return "давно";

		try {
			return formatDistanceToNow(lastSeen, {
				addSuffix: true,
				locale: ru,
			});
		} catch {
			return "давно";
		}
	};

	const getContactStatus = (contact: Contact) => {
		if (contact.isOnline) {
			return "в сети";
		}
		return `был(а) ${formatLastSeen(contact.lastSeen)}`;
	};

	return (
		<Box display="flex" flexDirection="column" height="100%">
			{/* Header */}
			<Box
				display="flex"
				alignItems="center"
				justifyContent="space-between"
				p={2}
				bgcolor="primary.main"
				color="white"
			>
				<Box display="flex" alignItems="center" gap={2}>
					<Avatar src={user?.avatar} sx={{ width: 40, height: 40 }}>
						{user?.displayName?.charAt(0).toUpperCase()}
					</Avatar>
					<Box>
						<Typography variant="h6" noWrap>
							{user?.displayName}
						</Typography>
						<Box display="flex" alignItems="center" gap={1}>
							<Circle
								sx={{
									fontSize: 8,
									color: isConnected ? "#4caf50" : "#f44336",
								}}
							/>
							<Typography variant="caption">
								{isConnected ? "В сети" : "Не в сети"}
							</Typography>
						</Box>
					</Box>
				</Box>

				<IconButton color="inherit" onClick={handleMenuOpen}>
					<MoreVert />
				</IconButton>
			</Box>

			{/* Search */}
			<Box p={2} bgcolor="background.paper">
				<Box display="flex" gap={1}>
					<TextField
						fullWidth
						placeholder="Поиск контактов..."
						variant="outlined"
						size="small"
						value={searchQuery}
						onChange={(e) => setSearchQuery(e.target.value)}
						InputProps={{
							startAdornment: (
								<InputAdornment position="start">
									<Search color="action" />
								</InputAdornment>
							),
							endAdornment: isSearching && (
								<InputAdornment position="end">
									<CircularProgress size={20} />
								</InputAdornment>
							),
						}}
					/>
					<IconButton
						color="primary"
						onClick={openSearchDialog}
						title="Найти новых пользователей"
					>
						<PersonAdd />
					</IconButton>
				</Box>

				{/* Search Results */}
				{filteredSearchResults.length > 0 && (
					<Box mt={1}>
						<Typography variant="caption" color="text.secondary">
							Найденные пользователи:
						</Typography>
						<List dense>
							{filteredSearchResults.map((user) => (
								<ListItem key={user.userJid} disablePadding>
									<ListItemButton onClick={() => handleAddContact(user)}>
										<ListItemAvatar>
											<Avatar src={user.avatar}>
												{user.displayName.charAt(0).toUpperCase()}
											</Avatar>
										</ListItemAvatar>
										<ListItemText
											primary={user.displayName}
											secondary={`@${user.username}`}
										/>
										<IconButton size="small" color="primary">
											<Add />
										</IconButton>
									</ListItemButton>
								</ListItem>
							))}
						</List>
					</Box>
				)}
			</Box>

			{/* Contacts List */}
			<Box flex={1} overflow="auto">
				{filteredContacts.length === 0 ? (
					<Box
						display="flex"
						flexDirection="column"
						alignItems="center"
						justifyContent="center"
						height="200px"
						p={3}
						textAlign="center"
					>
						<Message sx={{ fontSize: 48, color: "text.secondary", mb: 2 }} />
						<Typography variant="h6" color="text.secondary" gutterBottom>
							Нет контактов
						</Typography>
						<Typography variant="body2" color="text.secondary" mb={2}>
							Добавьте контакты, чтобы начать общение
						</Typography>
						<Box display="flex" flexDirection="column" gap={1}>
							<Button
								variant="outlined"
								startIcon={<PersonAdd />}
								onClick={openSearchDialog}
							>
								Найти пользователей
							</Button>
							<TestContacts />
						</Box>
						<XMPPTest />
					</Box>
				) : (
					<List disablePadding>
						{filteredContacts.map((contact) => (
							<ListItem key={contact.userJid} disablePadding>
								<ListItemButton
									selected={selectedContact?.userJid === contact.userJid}
									onClick={() => onContactSelect(contact)}
									sx={{
										py: 1.5,
										"&.Mui-selected": {
											bgcolor: "action.selected",
										},
									}}
								>
									<ListItemAvatar>
										<Badge
											overlap="circular"
											anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
											badgeContent={
												contact.isOnline ? (
													<Circle
														sx={{
															fontSize: 12,
															color: "#4caf50",
															bgcolor: "white",
															borderRadius: "50%",
														}}
													/>
												) : null
											}
										>
											<Avatar src={contact.avatar}>
												{contact.displayName.charAt(0).toUpperCase()}
											</Avatar>
										</Badge>
									</ListItemAvatar>

									<ListItemText
										primary={
											<Box
												display="flex"
												justifyContent="space-between"
												alignItems="center"
											>
												<Typography variant="subtitle1" noWrap>
													{contact.displayName}
												</Typography>
												<Box display="flex" alignItems="center" gap={0.5}>
													<IconButton
														size="small"
														onClick={(e) => {
															e.stopPropagation();
															handleCall(contact, "audio");
														}}
														title="Аудиозвонок"
													>
														<Phone fontSize="small" />
													</IconButton>
													<IconButton
														size="small"
														onClick={(e) => {
															e.stopPropagation();
															handleCall(contact, "video");
														}}
														title="Видеозвонок"
													>
														<VideoCall fontSize="small" />
													</IconButton>
													{contact.lastMessageTime && (
														<Typography
															variant="caption"
															color="text.secondary"
														>
															{formatLastSeen(contact.lastMessageTime)}
														</Typography>
													)}
												</Box>
											</Box>
										}
										secondary={
											<Box
												display="flex"
												justifyContent="space-between"
												alignItems="center"
											>
												<Typography
													variant="body2"
													color="text.secondary"
													noWrap
													sx={{ flex: 1, mr: 1 }}
												>
													{contact.lastMessage || getContactStatus(contact)}
												</Typography>
												{contact.unreadCount && contact.unreadCount > 0 && (
													<Badge
														badgeContent={contact.unreadCount}
														color="primary"
														sx={{
															"& .MuiBadge-badge": {
																fontSize: "0.75rem",
																minWidth: 20,
																height: 20,
															},
														}}
													/>
												)}
											</Box>
										}
										primaryTypographyProps={{ component: "div" }}
										secondaryTypographyProps={{ component: "div" }}
									/>
								</ListItemButton>
							</ListItem>
						))}
					</List>
				)}
			</Box>

			{/* Menu */}
			<Menu
				anchorEl={menuAnchor}
				open={Boolean(menuAnchor)}
				onClose={handleMenuClose}
				anchorOrigin={{
					vertical: "bottom",
					horizontal: "right",
				}}
				transformOrigin={{
					vertical: "top",
					horizontal: "right",
				}}
			>
				<MenuItem onClick={openSearchDialog}>
					<PersonAdd sx={{ mr: 2 }} />
					Найти пользователей
				</MenuItem>
				<MenuItem onClick={handleMenuClose}>
					<Settings sx={{ mr: 2 }} />
					Настройки
				</MenuItem>
				<Divider />
				<MenuItem onClick={handleLogout}>
					<ExitToApp sx={{ mr: 2 }} />
					Выйти
				</MenuItem>
			</Menu>

			{/* Search Dialog */}
			<Dialog
				open={showSearchDialog}
				onClose={closeSearchDialog}
				maxWidth="sm"
				fullWidth
			>
				<DialogTitle>Поиск пользователей</DialogTitle>
				<DialogContent>
					<TextField
						autoFocus
						fullWidth
						placeholder="Введите имя пользователя..."
						variant="outlined"
						value={searchQuery}
						onChange={(e) => setSearchQuery(e.target.value)}
						sx={{ mb: 2 }}
						InputProps={{
							startAdornment: (
								<InputAdornment position="start">
									<Search />
								</InputAdornment>
							),
							endAdornment: isSearching && (
								<InputAdornment position="end">
									<CircularProgress size={20} />
								</InputAdornment>
							),
						}}
					/>

					{filteredSearchResults.length > 0 && (
						<List>
							{filteredSearchResults.map((user) => (
								<ListItem key={user.userJid} disablePadding>
									<ListItemButton onClick={() => handleAddContact(user)}>
										<ListItemAvatar>
											<Avatar src={user.avatar}>
												{user.displayName.charAt(0).toUpperCase()}
											</Avatar>
										</ListItemAvatar>
										<ListItemText
											primary={user.displayName}
											secondary={`@${user.username}`}
										/>
										<IconButton size="small" color="primary">
											<Add />
										</IconButton>
									</ListItemButton>
								</ListItem>
							))}
						</List>
					)}

					{searchQuery &&
						!isSearching &&
						filteredSearchResults.length === 0 && (
							<Typography color="text.secondary" textAlign="center" py={2}>
								Пользователи не найдены
							</Typography>
						)}
				</DialogContent>
				<DialogActions>
					<Button onClick={closeSearchDialog}>Закрыть</Button>
				</DialogActions>
			</Dialog>
		</Box>
	);
};

export default Sidebar;
