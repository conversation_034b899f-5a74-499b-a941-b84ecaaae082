import { PersonAdd } from "@mui/icons-material";
import { Box, Button } from "@mui/material";
import type React from "react";
import { useChatStore } from "../../stores/chatStore";

const TestContacts: React.FC = () => {
	const { addContact, contacts } = useChatStore();

	const addTestContacts = () => {
		const testContacts = [
			{
				userJid: "test_user_1@localhost",
				username: "test_user_1",
				displayName: "Тестовый пользователь 1",
				status: "Доступен",
				isOnline: true,
			},
			{
				userJid: "test_user_2@localhost",
				username: "test_user_2",
				displayName: "Тестовый пользователь 2",
				status: "Занят",
				isOnline: false,
				lastSeen: new Date(Date.now() - 30 * 60 * 1000), // 30 минут назад
			},
			{
				userJid: "alice@localhost",
				username: "alice",
				displayName: "Алиса",
				status: "В сети",
				isOnline: true,
			},
			{
				userJid: "bob@localhost",
				username: "bob",
				displayName: "Боб",
				status: "Отошел",
				isOnline: false,
				lastSeen: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 часа назад
			},
		];

		testContacts.forEach((contact) => {
			const exists = contacts.some((c) => c.userJid === contact.userJid);
			if (!exists) {
				addContact(contact);
			}
		});
	};

	return (
		<Box textAlign="center" p={2}>
			<Button
				variant="outlined"
				startIcon={<PersonAdd />}
				onClick={addTestContacts}
				color="primary"
			>
				Добавить тестовые контакты
			</Button>
		</Box>
	);
};

export default TestContacts;
