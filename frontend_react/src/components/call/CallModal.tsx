import {
	Call,
	CallEnd,
	Mic,
	MicOff,
	Videocam,
	VideocamOff,
	VolumeOff,
	VolumeUp,
} from "@mui/icons-material";
import {
	Avatar,
	Box,
	Chip,
	Dialog,
	DialogContent,
	Grid,
	IconButton,
	Paper,
	Typography,
} from "@mui/material";
import { formatDistanceToNow } from "date-fns";
import { ru } from "date-fns/locale";
import type React from "react";
import { useEffect, useState } from "react";
import { useCall } from "../../contexts/CallProvider";
import { useCallStore } from "../../stores/callStore";

const CallModal: React.FC = () => {
	const {
		answerCall,
		rejectCall,
		endCall,
		toggleAudio,
		toggleVideo,
		closeCallModal,
	} = useCall();

	const { currentCall, isCallModalOpen, localAudioEnabled, localVideoEnabled } =
		useCallStore();

	const [callDuration, setCallDuration] = useState(0);
	const [isSpeakerEnabled, setIsSpeakerEnabled] = useState(false);

	// Таймер для отображения длительности звонка
	useEffect(() => {
		let interval: NodeJS.Timeout | null = null;

		if (currentCall?.status === "connected" && currentCall.startedAt) {
			interval = setInterval(() => {
				const now = new Date();
				const duration = Math.floor(
					(now.getTime() - currentCall.startedAt!.getTime()) / 1000,
				);
				setCallDuration(duration);
			}, 1000);
		}

		return () => {
			if (interval) {
				clearInterval(interval);
			}
		};
	}, [currentCall?.status, currentCall?.startedAt]);

	const formatCallDuration = (seconds: number) => {
		const mins = Math.floor(seconds / 60);
		const secs = seconds % 60;
		return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
	};

	const handleAnswerCall = () => {
		if (currentCall) {
			answerCall(currentCall.id);
		}
	};

	const handleRejectCall = () => {
		if (currentCall) {
			rejectCall(currentCall.id);
		}
	};

	const handleEndCall = () => {
		endCall();
	};

	const handleToggleAudio = () => {
		toggleAudio();
	};

	const handleToggleVideo = () => {
		toggleVideo();
	};

	const handleToggleSpeaker = () => {
		setIsSpeakerEnabled((prev) => !prev);
		// В реальной реализации здесь бы переключался динамик
	};

	if (!currentCall) return null;

	const isIncoming = currentCall.status === "ringing";
	const isConnected = currentCall.status === "connected";
	const isConnecting = currentCall.status === "connecting";

	const getStatusText = () => {
		switch (currentCall.status) {
			case "ringing":
				return "Входящий звонок...";
			case "connecting":
				return "Соединение...";
			case "connected":
				return formatCallDuration(callDuration);
			default:
				return "";
		}
	};

	const getCallTypeIcon = () => {
		return currentCall.callType === "video" ? <Videocam /> : <Call />;
	};

	return (
		<Dialog
			open={isCallModalOpen}
			onClose={isIncoming ? handleRejectCall : handleEndCall}
			maxWidth="sm"
			fullWidth
			PaperProps={{
				sx: {
					bgcolor:
						currentCall.callType === "video" ? "black" : "background.paper",
					color: currentCall.callType === "video" ? "white" : "text.primary",
					minHeight: 400,
				},
			}}
		>
			<DialogContent sx={{ p: 3, textAlign: "center" }}>
				{/* Call Status */}
				<Box mb={2}>
					<Chip
						icon={getCallTypeIcon()}
						label={
							currentCall.callType === "video" ? "Видеозвонок" : "Аудиозвонок"
						}
						color="primary"
						variant="outlined"
					/>
				</Box>

				{/* Participant Info */}
				<Box mb={3}>
					{currentCall.participants.length > 0 && (
						<>
							<Avatar
								sx={{
									width: 120,
									height: 120,
									mx: "auto",
									mb: 2,
									fontSize: "3rem",
								}}
							>
								{currentCall.participants[0].displayName
									.charAt(0)
									.toUpperCase()}
							</Avatar>
							<Typography variant="h5" gutterBottom>
								{currentCall.participants[0].displayName}
							</Typography>
						</>
					)}
					<Typography variant="body1" color="text.secondary">
						{getStatusText()}
					</Typography>
				</Box>

				{/* Video Area */}
				{currentCall.callType === "video" && isConnected && (
					<Paper
						sx={{
							height: 200,
							mb: 3,
							bgcolor: "grey.900",
							position: "relative",
							overflow: "hidden",
						}}
					>
						{/* Remote Video */}
						<video
							id="remote-video"
							autoPlay
							playsInline
							style={{
								width: "100%",
								height: "100%",
								objectFit: "cover",
							}}
						/>

						{/* Local Video (Picture-in-Picture) */}
						<video
							id="local-video"
							autoPlay
							playsInline
							muted
							style={{
								position: "absolute",
								top: 10,
								right: 10,
								width: 80,
								height: 60,
								objectFit: "cover",
								borderRadius: 8,
								border: "2px solid white",
							}}
						/>

						{/* Audio element for remote audio */}
						<audio id="remote-audio" autoPlay />
					</Paper>
				)}

				{/* Call Controls */}
				<Box>
					{isIncoming ? (
						// Incoming call controls
						<Box display="flex" justifyContent="center" gap={3}>
							<IconButton
								size="large"
								onClick={handleRejectCall}
								sx={{
									bgcolor: "error.main",
									color: "white",
									"&:hover": { bgcolor: "error.dark" },
									width: 64,
									height: 64,
								}}
							>
								<CallEnd />
							</IconButton>
							<IconButton
								size="large"
								onClick={handleAnswerCall}
								sx={{
									bgcolor: "success.main",
									color: "white",
									"&:hover": { bgcolor: "success.dark" },
									width: 64,
									height: 64,
								}}
							>
								<Call />
							</IconButton>
						</Box>
					) : (
						// Active call controls
						<Grid container spacing={2} justifyContent="center">
							<Grid item>
								<IconButton
									onClick={handleToggleAudio}
									sx={{
										bgcolor: localAudioEnabled
											? "action.selected"
											: "error.main",
										color: localAudioEnabled ? "text.primary" : "white",
										"&:hover": {
											bgcolor: localAudioEnabled
												? "action.hover"
												: "error.dark",
										},
									}}
								>
									{localAudioEnabled ? <Mic /> : <MicOff />}
								</IconButton>
							</Grid>

							{currentCall.callType === "video" && (
								<Grid item>
									<IconButton
										onClick={handleToggleVideo}
										sx={{
											bgcolor: localVideoEnabled
												? "action.selected"
												: "error.main",
											color: localVideoEnabled ? "text.primary" : "white",
											"&:hover": {
												bgcolor: localVideoEnabled
													? "action.hover"
													: "error.dark",
											},
										}}
									>
										{localVideoEnabled ? <Videocam /> : <VideocamOff />}
									</IconButton>
								</Grid>
							)}

							<Grid item>
								<IconButton
									onClick={handleToggleSpeaker}
									sx={{
										bgcolor: isSpeakerEnabled
											? "primary.main"
											: "action.selected",
										color: isSpeakerEnabled ? "white" : "text.primary",
										"&:hover": {
											bgcolor: isSpeakerEnabled
												? "primary.dark"
												: "action.hover",
										},
									}}
								>
									{isSpeakerEnabled ? <VolumeUp /> : <VolumeOff />}
								</IconButton>
							</Grid>

							<Grid item>
								<IconButton
									onClick={handleEndCall}
									sx={{
										bgcolor: "error.main",
										color: "white",
										"&:hover": { bgcolor: "error.dark" },
									}}
								>
									<CallEnd />
								</IconButton>
							</Grid>
						</Grid>
					)}
				</Box>

				{/* Connection Status */}
				{isConnecting && (
					<Box mt={2}>
						<Typography variant="body2" color="text.secondary">
							Подключение к серверу...
						</Typography>
					</Box>
				)}
			</DialogContent>
		</Dialog>
	);
};

export default CallModal;
