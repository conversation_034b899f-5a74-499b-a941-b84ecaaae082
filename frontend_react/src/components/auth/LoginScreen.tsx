import { Message, Visibility, VisibilityOff } from "@mui/icons-material";
import {
	Alert,
	Box,
	Button,
	CircularProgress,
	IconButton,
	InputAdornment,
	Paper,
	Tab,
	Tabs,
	TextField,
	Typography,
} from "@mui/material";
import type React from "react";
import { useState } from "react";
import { useLogin, useRegister } from "../../hooks/useApi";

interface TabPanelProps {
	children?: React.ReactNode;
	index: number;
	value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
	return (
		<div hidden={value !== index}>
			{value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
		</div>
	);
};

const LoginScreen: React.FC = () => {
	const loginMutation = useLogin();
	const registerMutation = useRegister();
	const [tabValue, setTabValue] = useState(0);
	const [showPassword, setShowPassword] = useState(false);
	const [error, setError] = useState<string | null>(null);

	// Login form state
	const [loginData, setLoginData] = useState({
		username: "",
		password: "",
	});

	// Register form state
	const [registerData, setRegisterData] = useState({
		username: "",
		password: "",
		confirmPassword: "",
		displayName: "",
	});

	const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
		setTabValue(newValue);
		setError(null);
	};

	const handleLogin = async (e: React.FormEvent) => {
		e.preventDefault();
		setError(null);

		if (!loginData.username || !loginData.password) {
			setError("Пожалуйста, заполните все поля");
			return;
		}

		try {
			await loginMutation.mutateAsync({
				username: loginData.username,
				password: loginData.password,
			});
		} catch (error) {
			setError("Неверное имя пользователя или пароль");
		}
	};

	const handleRegister = async (e: React.FormEvent) => {
		e.preventDefault();
		setError(null);

		if (
			!registerData.username ||
			!registerData.password ||
			!registerData.displayName
		) {
			setError("Пожалуйста, заполните все поля");
			return;
		}

		if (registerData.password !== registerData.confirmPassword) {
			setError("Пароли не совпадают");
			return;
		}

		if (registerData.password.length < 6) {
			setError("Пароль должен содержать минимум 6 символов");
			return;
		}

		try {
			await registerMutation.mutateAsync({
				username: registerData.username,
				password: registerData.password,
				displayName: registerData.displayName,
			});

			// После успешной регистрации автоматически логинимся
			await loginMutation.mutateAsync({
				username: registerData.username,
				password: registerData.password,
			});
		} catch (error) {
			setError("Ошибка регистрации. Возможно, пользователь уже существует");
		}
	};

	return (
		<Box
			display="flex"
			justifyContent="center"
			alignItems="center"
			minHeight="100vh"
			bgcolor="background.default"
			px={2}
		>
			<Paper
				elevation={8}
				sx={{
					width: "100%",
					maxWidth: 400,
					p: 4,
					borderRadius: 3,
				}}
			>
				{/* Header */}
				<Box textAlign="center" mb={3}>
					<Message
						sx={{
							fontSize: 48,
							color: "primary.main",
							mb: 1,
						}}
					/>
					<Typography variant="h4" fontWeight="bold" color="primary.main">
						WhatsApp
					</Typography>
					<Typography variant="body2" color="text.secondary" mt={1}>
						Добро пожаловать в мессенджер
					</Typography>
				</Box>

				{/* Tabs */}
				<Tabs
					value={tabValue}
					onChange={handleTabChange}
					variant="fullWidth"
					sx={{ mb: 2 }}
				>
					<Tab label="Вход" />
					<Tab label="Регистрация" />
				</Tabs>

				{/* Error Alert */}
				{error && (
					<Alert severity="error" sx={{ mb: 2 }}>
						{error}
					</Alert>
				)}

				{/* Login Tab */}
				<TabPanel value={tabValue} index={0}>
					<Box component="form" onSubmit={handleLogin}>
						<TextField
							fullWidth
							label="Имя пользователя"
							variant="outlined"
							margin="normal"
							value={loginData.username}
							onChange={(e) =>
								setLoginData({ ...loginData, username: e.target.value })
							}
							disabled={loginMutation.isPending}
						/>
						<TextField
							fullWidth
							label="Пароль"
							type={showPassword ? "text" : "password"}
							variant="outlined"
							margin="normal"
							value={loginData.password}
							onChange={(e) =>
								setLoginData({ ...loginData, password: e.target.value })
							}
							disabled={loginMutation.isPending}
							InputProps={{
								endAdornment: (
									<InputAdornment position="end">
										<IconButton
											onClick={() => setShowPassword(!showPassword)}
											edge="end"
										>
											{showPassword ? <VisibilityOff /> : <Visibility />}
										</IconButton>
									</InputAdornment>
								),
							}}
						/>
						<Button
							type="submit"
							fullWidth
							variant="contained"
							size="large"
							disabled={loginMutation.isPending}
							sx={{ mt: 3, mb: 2, py: 1.5 }}
						>
							{loginMutation.isPending ? (
								<CircularProgress size={24} color="inherit" />
							) : (
								"Войти"
							)}
						</Button>
					</Box>
				</TabPanel>

				{/* Register Tab */}
				<TabPanel value={tabValue} index={1}>
					<Box component="form" onSubmit={handleRegister}>
						<TextField
							fullWidth
							label="Имя пользователя"
							variant="outlined"
							margin="normal"
							value={registerData.username}
							onChange={(e) =>
								setRegisterData({ ...registerData, username: e.target.value })
							}
							disabled={registerMutation.isPending || loginMutation.isPending}
						/>
						<TextField
							fullWidth
							label="Отображаемое имя"
							variant="outlined"
							margin="normal"
							value={registerData.displayName}
							onChange={(e) =>
								setRegisterData({
									...registerData,
									displayName: e.target.value,
								})
							}
							disabled={registerMutation.isPending || loginMutation.isPending}
						/>
						<TextField
							fullWidth
							label="Пароль"
							type={showPassword ? "text" : "password"}
							variant="outlined"
							margin="normal"
							value={registerData.password}
							onChange={(e) =>
								setRegisterData({ ...registerData, password: e.target.value })
							}
							disabled={registerMutation.isPending || loginMutation.isPending}
							InputProps={{
								endAdornment: (
									<InputAdornment position="end">
										<IconButton
											onClick={() => setShowPassword(!showPassword)}
											edge="end"
										>
											{showPassword ? <VisibilityOff /> : <Visibility />}
										</IconButton>
									</InputAdornment>
								),
							}}
						/>
						<TextField
							fullWidth
							label="Подтвердите пароль"
							type={showPassword ? "text" : "password"}
							variant="outlined"
							margin="normal"
							value={registerData.confirmPassword}
							onChange={(e) =>
								setRegisterData({
									...registerData,
									confirmPassword: e.target.value,
								})
							}
							disabled={registerMutation.isPending || loginMutation.isPending}
						/>
						<Button
							type="submit"
							fullWidth
							variant="contained"
							size="large"
							disabled={registerMutation.isPending || loginMutation.isPending}
							sx={{ mt: 3, mb: 2, py: 1.5 }}
						>
							{registerMutation.isPending || loginMutation.isPending ? (
								<CircularProgress size={24} color="inherit" />
							) : (
								"Зарегистрироваться"
							)}
						</Button>
					</Box>
				</TabPanel>
			</Paper>
		</Box>
	);
};

export default LoginScreen;
