import { type RemoteParticipant, Room, Track } from "livekit-client";
import type React from "react";
import { createContext, type ReactNode, useContext, useRef } from "react";
import {
	useAnswerCall,
	useCallToken,
	useCreateCall,
	useEndCall,
	useRejectCall,
} from "../hooks/useApi";
import { useAuthStore } from "../stores/authStore";
import { useCallStore } from "../stores/callStore";
import { useXMPP } from "./XMPPProvider";

export interface CallContextType {
	// Call management
	initiateCall: (
		participantJids: string[],
		callType: "audio" | "video",
	) => Promise<boolean>;
	answerCall: (callId: string) => Promise<boolean>;
	rejectCall: (callId: string) => Promise<boolean>;
	endCall: () => Promise<boolean>;

	// Media controls
	toggleAudio: () => void;
	toggleVideo: () => void;

	// <PERSON><PERSON> controls
	openCallModal: () => void;
	closeCallModal: () => void;
}

const CallContext = createContext<CallContextType | undefined>(undefined);

interface CallProviderProps {
	children: ReactNode;
}

export const CallProvider: React.FC<CallProviderProps> = ({ children }) => {
	const { user } = useAuthStore();
	const {
		currentCall,
		localAudioEnabled,
		localVideoEnabled,
		setCurrentCall,
		updateCall,
		setCallModalOpen,
		setLocalAudioEnabled,
		setLocalVideoEnabled,
	} = useCallStore();
	const { sendMessage } = useXMPP();

	// LiveKit room reference
	const roomRef = useRef<Room | null>(null);

	// API hooks
	const createCallMutation = useCreateCall();
	const answerCallMutation = useAnswerCall();
	const rejectCallMutation = useRejectCall();
	const endCallMutation = useEndCall();
	const { data: callTokenData } = useCallToken(currentCall?.id || null);

	const initiateCall = async (
		participantJids: string[],
		callType: "audio" | "video",
	): Promise<boolean> => {
		if (!user) return false;

		try {
			const callData = await createCallMutation.mutateAsync({
				participants: participantJids,
				callType,
			});

			const call = {
				id: callData.call_id,
				roomName: callData.room_name,
				callType,
				initiatorJid: user.userJid,
				participants: participantJids.map((jid) => ({
					userJid: jid,
					username: jid.split("@")[0],
					displayName: jid.split("@")[0],
					isAudioEnabled: true,
					isVideoEnabled: callType === "video",
					isSpeaking: false,
				})),
				status: "connecting" as const,
				startedAt: new Date(),
			};

			setCurrentCall(call);
			setCallModalOpen(true);

			// Connect to LiveKit room
			const token = callData.tokens[user.userJid];
			await connectToLiveKitRoom(callData.livekit_url, token);

			// Уведомления о звонке уже отправлены с бекенда
			// Здесь ничего дополнительно делать не нужно

			return true;
		} catch (error) {
			console.error("Failed to initiate call:", error);
			return false;
		}
	};

	const answerCall = async (callId: string): Promise<boolean> => {
		if (!currentCall || !user || !callTokenData) return false;

		try {
			// Answer call on backend
			await answerCallMutation.mutateAsync(callId);

			// Connect to LiveKit room
			await connectToLiveKitRoom(
				callTokenData.livekit_url,
				callTokenData.token,
			);

			updateCall({
				status: "connected",
				startedAt: new Date(),
			});

			return true;
		} catch (error) {
			console.error("Failed to answer call:", error);
			return false;
		}
	};

	const rejectCall = async (callId: string): Promise<boolean> => {
		if (!currentCall || !user) return false;

		try {
			// Reject call on backend
			await rejectCallMutation.mutateAsync(callId);

			setCurrentCall(null);
			setCallModalOpen(false);

			return true;
		} catch (error) {
			console.error("Failed to reject call:", error);
			return false;
		}
	};

	const endCall = async (): Promise<boolean> => {
		if (!currentCall) return false;

		try {
			// Disconnect from LiveKit room
			if (roomRef.current) {
				await roomRef.current.disconnect();
				roomRef.current = null;
			}

			// End call on server
			await endCallMutation.mutateAsync(currentCall.id);

			// Уведомление о завершении звонка отправляется через бекенд

			setCurrentCall(null);
			setCallModalOpen(false);
			setLocalAudioEnabled(true);
			setLocalVideoEnabled(true);

			return true;
		} catch (error) {
			console.error("Failed to end call:", error);
			return false;
		}
	};

	const connectToLiveKitRoom = async (livekitUrl: string, token: string) => {
		try {
			console.log("Connecting to LiveKit room:", { livekitUrl, token });

			// Create room instance
			const room = new Room();
			roomRef.current = room;

			// Set up event listeners
			room.on("participantConnected", (participant: RemoteParticipant) => {
				console.log("Participant connected:", participant.identity);
			});

			room.on("participantDisconnected", (participant: RemoteParticipant) => {
				console.log("Participant disconnected:", participant.identity);
			});

			room.on(
				"trackSubscribed",
				(track: Track, publication: any, participant: RemoteParticipant) => {
					console.log("Track subscribed:", track.kind, participant.identity);

					if (track.kind === Track.Kind.Video) {
						// Handle video track
						const videoElement = document.getElementById(
							"remote-video",
						) as HTMLVideoElement;
						if (videoElement) {
							track.attach(videoElement);
						}
					} else if (track.kind === Track.Kind.Audio) {
						// Handle audio track
						const audioElement = document.getElementById(
							"remote-audio",
						) as HTMLAudioElement;
						if (audioElement) {
							track.attach(audioElement);
						}
					}
				},
			);

			room.on(
				"trackUnsubscribed",
				(track: Track, publication: any, participant: RemoteParticipant) => {
					console.log("Track unsubscribed:", track.kind, participant.identity);
					track.detach();
				},
			);

			// Connect to room
			await room.connect(livekitUrl, token);

			// Request permissions and enable local tracks
			try {
				if (currentCall?.callType === "video") {
					await room.localParticipant.enableCameraAndMicrophone();
				} else {
					await room.localParticipant.setMicrophoneEnabled(true);
				}
				console.log("Local media enabled successfully");
			} catch (mediaError) {
				console.error("Failed to enable local media:", mediaError);
				// Continue without local media - user can enable later
			}

			// Attach local video if available
			if (currentCall?.callType === "video") {
				const localVideoElement = document.getElementById(
					"local-video",
				) as HTMLVideoElement;
				if (
					localVideoElement &&
					room.localParticipant.videoTrackPublications.size > 0
				) {
					const videoTrack = Array.from(
						room.localParticipant.videoTrackPublications.values(),
					)[0].track;
					if (videoTrack) {
						videoTrack.attach(localVideoElement);
					}
				}
			}

			updateCall({ status: "connected" });
		} catch (error) {
			console.error("Failed to connect to LiveKit room:", error);
			throw error;
		}
	};

	const toggleAudio = () => {
		const newState = !localAudioEnabled;
		setLocalAudioEnabled(newState);

		// Toggle local audio track
		if (roomRef.current) {
			roomRef.current.localParticipant.setMicrophoneEnabled(newState);
		}
	};

	const toggleVideo = () => {
		const newState = !localVideoEnabled;
		setLocalVideoEnabled(newState);

		// Toggle local video track
		if (roomRef.current) {
			roomRef.current.localParticipant.setCameraEnabled(newState);
		}
	};

	const openCallModal = () => setCallModalOpen(true);

	const closeCallModal = () => {
		if (currentCall?.status === "ringing") {
			rejectCall(currentCall.id);
		} else {
			setCallModalOpen(false);
		}
	};

	const value: CallContextType = {
		initiateCall,
		answerCall,
		rejectCall,
		endCall,
		toggleAudio,
		toggleVideo,
		openCallModal,
		closeCallModal,
	};

	return <CallContext.Provider value={value}>{children}</CallContext.Provider>;
};

export const useCall = (): CallContextType => {
	const context = useContext(CallContext);
	if (context === undefined) {
		throw new Error("useCall must be used within a CallProvider");
	}
	return context;
};
