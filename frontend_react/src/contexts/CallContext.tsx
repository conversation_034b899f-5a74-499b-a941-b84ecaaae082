import {
	LocalParticipant,
	type RemoteParticipant,
	Room,
	Track,
} from "livekit-client";
import React, {
	createContext,
	type ReactNode,
	useContext,
	useRef,
	useState,
} from "react";
import { useAuth } from "./AuthContext";
import { useWebSocket } from "./WebSocketContext";

export interface CallParticipant {
	userJid: string;
	username: string;
	displayName: string;
	avatar?: string;
	isAudioEnabled: boolean;
	isVideoEnabled: boolean;
	isSpeaking: boolean;
}

export interface Call {
	id: string;
	roomName: string;
	callType: "audio" | "video";
	initiatorJid: string;
	participants: CallParticipant[];
	status: "ringing" | "connecting" | "connected" | "ended";
	startedAt?: Date;
	duration?: number;
}

export interface CallContextType {
	currentCall: Call | null;
	isInCall: boolean;
	isCallModalOpen: boolean;

	// Call management
	initiateCall: (
		participantJids: string[],
		callType: "audio" | "video",
	) => Promise<boolean>;
	answerCall: (callId: string) => Promise<boolean>;
	rejectCall: (callId: string) => Promise<boolean>;
	endCall: () => Promise<boolean>;

	// Media controls
	toggleAudio: () => void;
	toggleVideo: () => void;

	// UI controls
	openCallModal: () => void;
	closeCallModal: () => void;
}

const CallContext = createContext<CallContextType | undefined>(undefined);

interface CallProviderProps {
	children: ReactNode;
}

const API_BASE_URL = "http://localhost:8000";

export const CallProvider: React.FC<CallProviderProps> = ({ children }) => {
	const { user, token } = useAuth();
	const { sendMessage, onMessage } = useWebSocket();

	const [currentCall, setCurrentCall] = useState<Call | null>(null);
	const [isCallModalOpen, setIsCallModalOpen] = useState(false);
	const [localAudioEnabled, setLocalAudioEnabled] = useState(true);
	const [localVideoEnabled, setLocalVideoEnabled] = useState(true);

	// LiveKit room and track references
	const roomRef = useRef<Room | null>(null);
	const localVideoTrackRef = useRef<Track | null>(null);
	const localAudioTrackRef = useRef<Track | null>(null);

	// Listen for incoming call notifications via WebSocket
	React.useEffect(() => {
		const unsubscribe = onMessage((message) => {
			if (message.type === "incoming_call") {
				handleIncomingCall(message);
			} else if (message.type === "call_ended") {
				handleCallEnded(message);
			} else if (message.type === "call_answered") {
				handleCallAnswered(message);
			} else if (message.type === "call_rejected") {
				handleCallRejected(message);
			}
		});

		return unsubscribe;
	}, [onMessage]);

	const handleIncomingCall = (message: any) => {
		const call: Call = {
			id: message.call_id,
			roomName: message.room_name,
			callType: message.call_type,
			initiatorJid: message.from_jid,
			participants: message.participants || [],
			status: "ringing",
		};

		setCurrentCall(call);
		setIsCallModalOpen(true);
	};

	const handleCallAnswered = (message: any) => {
		if (currentCall && currentCall.id === message.call_id) {
			setCurrentCall((prev) =>
				prev ? { ...prev, status: "connecting" } : null,
			);
		}
	};

	const handleCallRejected = (message: any) => {
		if (currentCall && currentCall.id === message.call_id) {
			setCurrentCall(null);
			setIsCallModalOpen(false);
		}
	};

	const handleCallEnded = (message: any) => {
		if (currentCall && currentCall.id === message.call_id) {
			endCall();
		}
	};

	const initiateCall = async (
		participantJids: string[],
		callType: "audio" | "video",
	): Promise<boolean> => {
		if (!user || !token) return false;

		try {
			const response = await fetch(`${API_BASE_URL}/api/calls/create`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
					Authorization: `Bearer ${token}`,
				},
				body: JSON.stringify({
					participants: participantJids,
					call_type: callType,
				}),
			});

			if (!response.ok) {
				throw new Error("Failed to create call");
			}

			const callData = await response.json();

			const call: Call = {
				id: callData.call_id,
				roomName: callData.room_name,
				callType,
				initiatorJid: user.userJid,
				participants: participantJids.map((jid) => ({
					userJid: jid,
					username: jid.split("@")[0],
					displayName: jid.split("@")[0],
					isAudioEnabled: true,
					isVideoEnabled: callType === "video",
					isSpeaking: false,
				})),
				status: "connecting",
				startedAt: new Date(),
			};

			setCurrentCall(call);
			setIsCallModalOpen(true);

			// Connect to LiveKit room
			await connectToLiveKitRoom(
				callData.livekit_url,
				callData.tokens[user.userJid],
			);

			// Notify participants via WebSocket
			participantJids.forEach((participantJid) => {
				sendMessage({
					type: "call_invite",
					to_jid: participantJid,
					call_id: callData.call_id,
					call_type: callType,
					room_name: callData.room_name,
				});
			});

			return true;
		} catch (error) {
			console.error("Failed to initiate call:", error);
			return false;
		}
	};

	const answerCall = async (callId: string): Promise<boolean> => {
		if (!currentCall || !user || !token) return false;

		try {
			// Get call info and LiveKit token
			const response = await fetch(`${API_BASE_URL}/api/calls/${callId}`, {
				method: "GET",
				headers: {
					Authorization: `Bearer ${token}`,
				},
			});

			if (!response.ok) {
				throw new Error("Failed to get call info");
			}

			const callData = await response.json();

			// Connect to LiveKit room
			await connectToLiveKitRoom(callData.livekit_url, callData.token);

			setCurrentCall((prev) =>
				prev ? { ...prev, status: "connected" } : null,
			);

			// Notify other participants
			sendMessage({
				type: "call_answer",
				call_id: callId,
				from_jid: user.userJid,
			});

			return true;
		} catch (error) {
			console.error("Failed to answer call:", error);
			return false;
		}
	};

	const rejectCall = async (callId: string): Promise<boolean> => {
		if (!currentCall || !user) return false;

		try {
			// Notify other participants
			sendMessage({
				type: "call_reject",
				call_id: callId,
				from_jid: user.userJid,
			});

			setCurrentCall(null);
			setIsCallModalOpen(false);

			return true;
		} catch (error) {
			console.error("Failed to reject call:", error);
			return false;
		}
	};

	const endCall = async (): Promise<boolean> => {
		if (!currentCall || !token) return false;

		try {
			// Disconnect from LiveKit room
			if (roomRef.current) {
				await roomRef.current.disconnect();
				roomRef.current = null;
			}

			// End call on server
			await fetch(`${API_BASE_URL}/api/calls/${currentCall.id}/end`, {
				method: "POST",
				headers: {
					Authorization: `Bearer ${token}`,
				},
			});

			// Notify other participants
			sendMessage({
				type: "call_end",
				call_id: currentCall.id,
			});

			setCurrentCall(null);
			setIsCallModalOpen(false);
			setLocalAudioEnabled(true);
			setLocalVideoEnabled(true);

			return true;
		} catch (error) {
			console.error("Failed to end call:", error);
			return false;
		}
	};

	const connectToLiveKitRoom = async (livekitUrl: string, token: string) => {
		try {
			console.log("Connecting to LiveKit room:", { livekitUrl, token });

			// Create room instance
			const room = new Room();
			roomRef.current = room;

			// Set up event listeners
			room.on("participantConnected", (participant: RemoteParticipant) => {
				console.log("Participant connected:", participant.identity);
			});

			room.on("participantDisconnected", (participant: RemoteParticipant) => {
				console.log("Participant disconnected:", participant.identity);
			});

			room.on(
				"trackSubscribed",
				(track: Track, publication: any, participant: RemoteParticipant) => {
					console.log("Track subscribed:", track.kind, participant.identity);

					if (track.kind === Track.Kind.Video) {
						// Handle video track
						const videoElement = document.getElementById(
							"remote-video",
						) as HTMLVideoElement;
						if (videoElement) {
							track.attach(videoElement);
						}
					} else if (track.kind === Track.Kind.Audio) {
						// Handle audio track
						const audioElement = document.getElementById(
							"remote-audio",
						) as HTMLAudioElement;
						if (audioElement) {
							track.attach(audioElement);
						}
					}
				},
			);

			room.on(
				"trackUnsubscribed",
				(track: Track, publication: any, participant: RemoteParticipant) => {
					console.log("Track unsubscribed:", track.kind, participant.identity);
					track.detach();
				},
			);

			// Connect to room
			await room.connect(livekitUrl, token);

			// Enable local tracks
			await room.localParticipant.enableCameraAndMicrophone();

			setCurrentCall((prev) =>
				prev ? { ...prev, status: "connected" } : null,
			);
		} catch (error) {
			console.error("Failed to connect to LiveKit room:", error);
			throw error;
		}
	};

	const toggleAudio = () => {
		setLocalAudioEnabled((prev) => {
			const newState = !prev;

			// Toggle local audio track
			if (roomRef.current) {
				roomRef.current.localParticipant.setMicrophoneEnabled(newState);
			}

			return newState;
		});
	};

	const toggleVideo = () => {
		setLocalVideoEnabled((prev) => {
			const newState = !prev;

			// Toggle local video track
			if (roomRef.current) {
				roomRef.current.localParticipant.setCameraEnabled(newState);
			}

			return newState;
		});
	};

	const openCallModal = () => setIsCallModalOpen(true);
	const closeCallModal = () => {
		if (currentCall?.status === "ringing") {
			rejectCall(currentCall.id);
		} else {
			setIsCallModalOpen(false);
		}
	};

	const value: CallContextType = {
		currentCall,
		isInCall: !!currentCall && currentCall.status !== "ended",
		isCallModalOpen,
		initiateCall,
		answerCall,
		rejectCall,
		endCall,
		toggleAudio,
		toggleVideo,
		openCallModal,
		closeCallModal,
	};

	return <CallContext.Provider value={value}>{children}</CallContext.Provider>;
};

export const useCall = (): CallContextType => {
	const context = useContext(CallContext);
	if (context === undefined) {
		throw new Error("useCall must be used within a CallProvider");
	}
	return context;
};
