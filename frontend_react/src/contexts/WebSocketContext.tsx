import type React from "react";
import {
	createContext,
	type ReactNode,
	useContext,
	useEffect,
	useRef,
	useState,
} from "react";
import { useAuth } from "./AuthContext";

export interface WebSocketMessage {
	type: string;
	[key: string]: any;
}

export interface WebSocketContextType {
	isConnected: boolean;
	sendMessage: (message: WebSocketMessage) => void;
	onMessage: (callback: (message: WebSocketMessage) => void) => () => void;
	reconnect: () => void;
}

const WebSocketContext = createContext<WebSocketContextType | undefined>(
	undefined,
);

interface WebSocketProviderProps {
	children: ReactNode;
}

const WS_BASE_URL = "ws://localhost:8000";

export const WebSocketProvider: React.FC<WebSocketProviderProps> = ({
	children,
}) => {
	const { token, isAuthenticated } = useAuth();
	const [isConnected, setIsConnected] = useState(false);
	const wsRef = useRef<WebSocket | null>(null);
	const messageCallbacksRef = useRef<Set<(message: WebSocketMessage) => void>>(
		new Set(),
	);
	const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
	const reconnectAttemptsRef = useRef(0);
	const maxReconnectAttempts = 5;

	const connect = () => {
		if (!isAuthenticated || !token) {
			return;
		}

		try {
			const wsUrl = `${WS_BASE_URL}/api/websocket/ws?token=${token}`;
			const ws = new WebSocket(wsUrl);

			ws.onopen = () => {
				console.log("WebSocket connected");
				setIsConnected(true);
				reconnectAttemptsRef.current = 0;

				// Отправляем ping для поддержания соединения
				const pingInterval = setInterval(() => {
					if (ws.readyState === WebSocket.OPEN) {
						ws.send(JSON.stringify({ type: "ping" }));
					} else {
						clearInterval(pingInterval);
					}
				}, 30000); // каждые 30 секунд
			};

			ws.onmessage = (event) => {
				try {
					const message: WebSocketMessage = JSON.parse(event.data);

					// Обрабатываем pong сообщения
					if (message.type === "pong") {
						return;
					}

					// Уведомляем всех подписчиков
					messageCallbacksRef.current.forEach((callback) => {
						try {
							callback(message);
						} catch (error) {
							console.error("Error in message callback:", error);
						}
					});
				} catch (error) {
					console.error("Error parsing WebSocket message:", error);
				}
			};

			ws.onclose = (event) => {
				console.log("WebSocket disconnected:", event.code, event.reason);
				setIsConnected(false);
				wsRef.current = null;

				// Автоматическое переподключение
				if (
					isAuthenticated &&
					reconnectAttemptsRef.current < maxReconnectAttempts
				) {
					const delay = Math.min(
						1000 * 2 ** reconnectAttemptsRef.current,
						30000,
					);
					console.log(
						`Reconnecting in ${delay}ms (attempt ${reconnectAttemptsRef.current + 1})`,
					);

					reconnectTimeoutRef.current = setTimeout(() => {
						reconnectAttemptsRef.current++;
						connect();
					}, delay);
				}
			};

			ws.onerror = (error) => {
				console.error("WebSocket error:", error);
			};

			wsRef.current = ws;
		} catch (error) {
			console.error("Error creating WebSocket connection:", error);
		}
	};

	const disconnect = () => {
		if (reconnectTimeoutRef.current) {
			clearTimeout(reconnectTimeoutRef.current);
			reconnectTimeoutRef.current = null;
		}

		if (wsRef.current) {
			wsRef.current.close();
			wsRef.current = null;
		}

		setIsConnected(false);
		reconnectAttemptsRef.current = 0;
	};

	const sendMessage = (message: WebSocketMessage) => {
		if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
			try {
				wsRef.current.send(JSON.stringify(message));
			} catch (error) {
				console.error("Error sending WebSocket message:", error);
			}
		} else {
			console.warn("WebSocket is not connected. Message not sent:", message);
		}
	};

	const onMessage = (callback: (message: WebSocketMessage) => void) => {
		messageCallbacksRef.current.add(callback);

		// Возвращаем функцию для отписки
		return () => {
			messageCallbacksRef.current.delete(callback);
		};
	};

	const reconnect = () => {
		disconnect();
		reconnectAttemptsRef.current = 0;
		if (isAuthenticated) {
			connect();
		}
	};

	useEffect(() => {
		if (isAuthenticated && token) {
			connect();
		} else {
			disconnect();
		}

		return () => {
			disconnect();
		};
	}, [isAuthenticated, token]);

	const value: WebSocketContextType = {
		isConnected,
		sendMessage,
		onMessage,
		reconnect,
	};

	return (
		<WebSocketContext.Provider value={value}>
			{children}
		</WebSocketContext.Provider>
	);
};

export const useWebSocket = (): WebSocketContextType => {
	const context = useContext(WebSocketContext);
	if (context === undefined) {
		throw new Error("useWebSocket must be used within a WebSocketProvider");
	}
	return context;
};
