// This comment is added to force a recompile and clear potential caching issues.
import <PERSON><PERSON> from "dexie";
import React, {
	createContext,
	type ReactNode,
	useContext,
	useEffect,
	useState,
} from "react";

export interface Contact {
	id?: number;
	userJid: string;
	username: string;
	displayName: string;
	avatar?: string;
	status?: string;
	lastSeen?: Date;
	isOnline?: boolean;
	unreadCount?: number;
	lastMessage?: string;
	lastMessageTime?: Date;
	isPinned?: boolean;
}

export interface Message {
	id?: number;
	messageId: string;
	fromJid: string;
	toJid: string;
	body: string;
	messageType: "chat" | "groupchat";
	timestamp: Date;
	isRead: boolean;
	isSent: boolean;
	isDelivered: boolean;
	isOwn: boolean;
	replyTo?: string;
	attachments?: MessageAttachment[];
}

export interface MessageAttachment {
	id: string;
	type: "image" | "video" | "audio" | "document";
	url: string;
	filename: string;
	size: number;
	mimeType: string;
}

export interface Chat {
	id?: number;
	chatJid: string;
	chatType: "direct" | "group";
	name: string;
	avatar?: string;
	lastMessage?: string;
	lastMessageTime?: Date;
	unreadCount: number;
	isPinned: boolean;
	isMuted: boolean;
	participants?: string[];
}

class MessengerDatabase extends Dexie {
	contacts!: Dexie.Table<Contact>;
	messages!: Dexie.Table<Message>;
	chats!: Dexie.Table<Chat>;

	constructor() {
		super("MessengerDatabase");

		this.version(1).stores({
			contacts: "++id, userJid, username, displayName, lastSeen, isOnline",
			messages: "++id, messageId, fromJid, toJid, timestamp, isRead, isOwn",
			chats:
				"++id, chatJid, chatType, name, lastMessageTime, unreadCount, isPinned",
		});
	}
}

export interface DatabaseContextType {
	db: MessengerDatabase;

	// Contacts
	addContact: (contact: Omit<Contact, "id">) => Promise<number>;
	updateContact: (userJid: string, updates: Partial<Contact>) => Promise<void>;
	getContact: (userJid: string) => Promise<Contact | undefined>;
	getAllContacts: () => Promise<Contact[]>;
	deleteContact: (userJid: string) => Promise<void>;

	// Messages
	addMessage: (message: Omit<Message, "id">) => Promise<number>;
	getMessages: (
		chatJid: string,
		limit?: number,
		offset?: number,
	) => Promise<Message[]>;
	markMessageAsRead: (messageId: string) => Promise<void>;
	markMessagesAsRead: (chatJid: string) => Promise<void>;
	deleteMessage: (messageId: string) => Promise<void>;

	// Chats
	addOrUpdateChat: (chat: Omit<Chat, "id">) => Promise<number>;
	getAllChats: () => Promise<Chat[]>;
	updateChatUnreadCount: (chatJid: string, count: number) => Promise<void>;
	toggleChatPin: (chatJid: string) => Promise<void>;
	toggleChatMute: (chatJid: string) => Promise<void>;

	// Utility
	clearAllData: () => Promise<void>;
	exportData: () => Promise<any>;
	importData: (data: any) => Promise<void>;
}

const DatabaseContext = createContext<DatabaseContextType | undefined>(
	undefined,
);

interface DatabaseProviderProps {
	children: ReactNode;
}

export const DatabaseProvider: React.FC<DatabaseProviderProps> = ({
	children,
}) => {
	const [db] = useState(() => new MessengerDatabase());

	useEffect(() => {
		// Инициализация базы данных
		db.open().catch((error) => {
			console.error("Failed to open database:", error);
		});

		return () => {
			db.close();
		};
	}, [db]);

	// Contacts methods
	const addContact = async (contact: Omit<Contact, "id">): Promise<number> => {
		return await db.contacts.add(contact);
	};

	const updateContact = async (
		userJid: string,
		updates: Partial<Contact>,
	): Promise<void> => {
		await db.contacts.where("userJid").equals(userJid).modify(updates);
	};

	const getContact = async (userJid: string): Promise<Contact | undefined> => {
		return await db.contacts.where("userJid").equals(userJid).first();
	};

	const getAllContacts = async (): Promise<Contact[]> => {
		return await db.contacts.orderBy("displayName").toArray();
	};

	const deleteContact = async (userJid: string): Promise<void> => {
		await db.contacts.where("userJid").equals(userJid).delete();
	};

	// Messages methods
	const addMessage = async (message: Omit<Message, "id">): Promise<number> => {
		return await db.messages.add(message);
	};

	const getMessages = async (
		chatJid: string,
		limit: number = 50,
		offset: number = 0,
	): Promise<Message[]> => {
		return await db.messages
			.orderBy("timestamp")
			.filter(
				(message) => message.toJid === chatJid || message.fromJid === chatJid,
			)
			.reverse()
			.offset(offset)
			.limit(limit)
			.toArray();
	};

	const markMessageAsRead = async (messageId: string): Promise<void> => {
		await db.messages
			.where("messageId")
			.equals(messageId)
			.modify({ isRead: true });
	};

	const markMessagesAsRead = async (chatJid: string): Promise<void> => {
		await db.messages
			.where("toJid")
			.equals(chatJid)
			.or("fromJid")
			.equals(chatJid)
			.modify({ isRead: true });
	};

	const deleteMessage = async (messageId: string): Promise<void> => {
		await db.messages.where("messageId").equals(messageId).delete();
	};

	// Chats methods
	const addOrUpdateChat = async (chat: Omit<Chat, "id">): Promise<number> => {
		const existing = await db.chats
			.where("chatJid")
			.equals(chat.chatJid)
			.first();
		if (existing) {
			await db.chats.where("chatJid").equals(chat.chatJid).modify(chat);
			return existing.id!;
		} else {
			return await db.chats.add(chat);
		}
	};

	const getAllChats = async (): Promise<Chat[]> => {
		return await db.chats
			.orderBy("isPinned")
			.reverse()
			.toArray()
			.then((chats) =>
				chats.sort((a, b) => {
					if (a.isPinned && !b.isPinned) return -1;
					if (!a.isPinned && b.isPinned) return 1;
					return (
						(b.lastMessageTime?.getTime() || 0) -
						(a.lastMessageTime?.getTime() || 0)
					);
				}),
			);
	};

	const updateChatUnreadCount = async (
		chatJid: string,
		count: number,
	): Promise<void> => {
		await db.chats
			.where("chatJid")
			.equals(chatJid)
			.modify({ unreadCount: count });
	};

	const toggleChatPin = async (chatJid: string): Promise<void> => {
		const chat = await db.chats.where("chatJid").equals(chatJid).first();
		if (chat) {
			await db.chats
				.where("chatJid")
				.equals(chatJid)
				.modify({ isPinned: !chat.isPinned });
		}
	};

	const toggleChatMute = async (chatJid: string): Promise<void> => {
		const chat = await db.chats.where("chatJid").equals(chatJid).first();
		if (chat) {
			await db.chats
				.where("chatJid")
				.equals(chatJid)
				.modify({ isMuted: !chat.isMuted });
		}
	};

	// Utility methods
	const clearAllData = async (): Promise<void> => {
		await db.transaction("rw", db.contacts, db.messages, db.chats, async () => {
			await db.contacts.clear();
			await db.messages.clear();
			await db.chats.clear();
		});
	};

	const exportData = async (): Promise<any> => {
		const contacts = await db.contacts.toArray();
		const messages = await db.messages.toArray();
		const chats = await db.chats.toArray();

		return {
			contacts,
			messages,
			chats,
			exportDate: new Date().toISOString(),
		};
	};

	const importData = async (data: any): Promise<void> => {
		await db.transaction("rw", db.contacts, db.messages, db.chats, async () => {
			if (data.contacts) {
				await db.contacts.bulkAdd(data.contacts);
			}
			if (data.messages) {
				await db.messages.bulkAdd(data.messages);
			}
			if (data.chats) {
				await db.chats.bulkAdd(data.chats);
			}
		});
	};

	const value: DatabaseContextType = {
		db,
		addContact,
		updateContact,
		getContact,
		getAllContacts,
		deleteContact,
		addMessage,
		getMessages,
		markMessageAsRead,
		markMessagesAsRead,
		deleteMessage,
		addOrUpdateChat,
		getAllChats,
		updateChatUnreadCount,
		toggleChatPin,
		toggleChatMute,
		clearAllData,
		exportData,
		importData,
	};

	return (
		<DatabaseContext.Provider value={value}>
			{children}
		</DatabaseContext.Provider>
	);
};

export const useDatabase = (): DatabaseContextType => {
	const context = useContext(DatabaseContext);
	if (context === undefined) {
		throw new Error("useDatabase must be used within a DatabaseProvider");
	}
	return context;
};
