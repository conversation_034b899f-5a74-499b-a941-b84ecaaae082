import type React from "react";
import {
	createContext,
	type ReactNode,
	useContext,
	useEffect,
	useState,
} from "react";

export interface User {
	id: string;
	username: string;
	displayName: string;
	userJid: string;
	avatar?: string;
	status?: string;
	lastSeen?: Date;
}

export interface AuthContextType {
	user: User | null;
	token: string | null;
	isAuthenticated: boolean;
	isLoading: boolean;
	login: (username: string, password: string) => Promise<boolean>;
	register: (
		username: string,
		password: string,
		displayName: string,
	) => Promise<boolean>;
	logout: () => void;
	updateUser: (updates: Partial<User>) => void;
	searchUsers: (query: string) => Promise<User[]>;
	getUserProfile: (username: string) => Promise<User | null>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
	children: ReactNode;
}

const API_BASE_URL = "http://localhost:8000";

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
	const [user, setUser] = useState<User | null>(null);
	const [token, setToken] = useState<string | null>(null);
	const [isLoading, setIsLoading] = useState(true);

	useEffect(() => {
		// Проверяем сохраненные данные при загрузке
		const savedToken = localStorage.getItem("auth_token");
		const savedUser = localStorage.getItem("auth_user");

		if (savedToken && savedUser) {
			try {
				const parsedUser = JSON.parse(savedUser);
				setToken(savedToken);
				setUser(parsedUser);
			} catch (error) {
				console.error("Error parsing saved user data:", error);
				localStorage.removeItem("auth_token");
				localStorage.removeItem("auth_user");
			}
		}

		setIsLoading(false);
	}, []);

	const login = async (
		username: string,
		password: string,
	): Promise<boolean> => {
		try {
			setIsLoading(true);

			const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ username, password }),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.detail || "Login failed");
			}

			const data = await response.json();

			const userData: User = {
				id: data.user_id,
				username: data.username,
				displayName: data.display_name,
				userJid: data.user_jid,
				avatar: data.avatar,
				status: data.status,
			};

			setToken(data.access_token);
			setUser(userData);

			// Сохраняем в localStorage
			localStorage.setItem("auth_token", data.access_token);
			localStorage.setItem("auth_user", JSON.stringify(userData));

			return true;
		} catch (error) {
			console.error("Login error:", error);
			return false;
		} finally {
			setIsLoading(false);
		}
	};

	const register = async (
		username: string,
		password: string,
		displayName: string,
	): Promise<boolean> => {
		try {
			setIsLoading(true);

			const response = await fetch(`${API_BASE_URL}/api/auth/register`, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					username,
					password,
					display_name: displayName,
				}),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.detail || "Registration failed");
			}

			// После успешной регистрации автоматически логинимся
			return await login(username, password);
		} catch (error) {
			console.error("Registration error:", error);
			return false;
		} finally {
			setIsLoading(false);
		}
	};

	const logout = () => {
		setUser(null);
		setToken(null);
		localStorage.removeItem("auth_token");
		localStorage.removeItem("auth_user");
	};

	const updateUser = (updates: Partial<User>) => {
		if (user) {
			const updatedUser = { ...user, ...updates };
			setUser(updatedUser);
			localStorage.setItem("auth_user", JSON.stringify(updatedUser));
		}
	};

	const searchUsers = async (query: string): Promise<User[]> => {
		if (!token || !query.trim()) return [];

		try {
			const response = await fetch(
				`${API_BASE_URL}/api/users/search?q=${encodeURIComponent(query)}`,
				{
					method: "GET",
					headers: {
						Authorization: `Bearer ${token}`,
					},
				},
			);

			if (!response.ok) {
				throw new Error("Search failed");
			}

			const users = await response.json();
			return users.map((userData: any) => ({
				id: userData.username,
				username: userData.username,
				displayName: userData.display_name,
				userJid: `${userData.username}@localhost`,
				avatar: userData.avatar_url,
				status: userData.status,
			}));
		} catch (error) {
			console.error("Search users error:", error);
			return [];
		}
	};

	const getUserProfile = async (username: string): Promise<User | null> => {
		if (!token) return null;

		try {
			const response = await fetch(
				`${API_BASE_URL}/api/users/profile/${username}`,
				{
					method: "GET",
					headers: {
						Authorization: `Bearer ${token}`,
					},
				},
			);

			if (!response.ok) {
				throw new Error("Profile fetch failed");
			}

			const userData = await response.json();
			return {
				id: userData.username,
				username: userData.username,
				displayName: userData.display_name,
				userJid: `${userData.username}@localhost`,
				avatar: userData.avatar_url,
				status: userData.status,
				lastSeen: userData.last_seen ? new Date(userData.last_seen) : undefined,
			};
		} catch (error) {
			console.error("Get user profile error:", error);
			return null;
		}
	};

	const value: AuthContextType = {
		user,
		token,
		isAuthenticated: !!user && !!token,
		isLoading,
		login,
		register,
		logout,
		updateUser,
		searchUsers,
		getUserProfile,
	};

	return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
	const context = useContext(AuthContext);
	if (context === undefined) {
		throw new Error("useAuth must be used within an AuthProvider");
	}
	return context;
};
