import React, {
	createContext,
	type ReactNode,
	useContext,
	useEffect,
} from "react";
import { useAuthStore } from "../stores/authStore";
import { useXMPPStore } from "../stores/xmppStore";

export interface XMPPContextType {
	isConnected: boolean;
	sendMessage: (to: string, body: string) => void;
	sendPresence: (show?: string, status?: string) => void;
	reconnect: () => void;
}

const XMPPContext = createContext<XMPPContextType | undefined>(undefined);

interface XMPPProviderProps {
	children: ReactNode;
}

const XMPP_DOMAIN = "localhost";

export const XMPPProvider: React.FC<XMPPProviderProps> = ({ children }) => {
	const { user, password, isAuthenticated } = useAuthStore();
	const { connect, disconnect, sendMessage, sendPresence, isConnected } = useXMPPStore();

	// Подключаемся при аутентификации
	useEffect(() => {
		if (isAuthenticated && user && password) {
			const jid = `${user.username}@${XMPP_DOMAIN}`;
			console.log("🔄 Connecting to XMPP as", jid);
			connect(jid, password);
		} else {
			disconnect();
		}

		return () => {
			disconnect();
		};
	}, [isAuthenticated, user, password, connect, disconnect]);

	const reconnect = React.useCallback(() => {
		if (isAuthenticated && user && password) {
			const jid = `${user.username}@${XMPP_DOMAIN}`;
			disconnect();
			setTimeout(() => connect(jid, password), 1000);
		}
	}, [isAuthenticated, user, password, connect, disconnect]);

	const value: XMPPContextType = {
		isConnected,
		sendMessage,
		sendPresence,
		reconnect,
	};

	return <XMPPContext.Provider value={value}>{children}</XMPPContext.Provider>;
};

export const useXMPP = (): XMPPContextType => {
	const context = useContext(XMPPContext);
	if (context === undefined) {
		throw new Error("useXMPP must be used within an XMPPProvider");
	}
	return context;
};