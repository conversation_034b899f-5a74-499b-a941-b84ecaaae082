
import Dexie, { type Table } from 'dexie';
import type { Message } from './stores/chatStore';

export class MessengerDB extends <PERSON><PERSON> {
    messages!: Table<Message>;

    constructor() {
        super('messengerDB');
        this.version(1).stores({
            // Schema definition.
            messages: 'messageId, [fromJid+toJid], timestamp',
        });
    }
}

export const db = new MessengerDB();
