/* Reset and base styles */
* {
	box-sizing: border-box;
}

html,
body {
	margin: 0;
	padding: 0;
	height: 100%;
	font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

#root {
	height: 100vh;
	display: flex;
	flex-direction: column;
}

/* Custom scrollbar */
::-webkit-scrollbar {
	width: 6px;
}

::-webkit-scrollbar-track {
	background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
	background: #c1c1c1;
	border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
	background: #a8a8a8;
}

/* Chat background pattern */
.chat-background {
	background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e5ddd5' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

/* Message animations */
@keyframes messageSlideIn {
	from {
		opacity: 0;
		transform: translateY(10px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.message-enter {
	animation: messageSlideIn 0.2s ease-out;
}

/* Typing indicator animation */
@keyframes typingDots {
	0%,
	60%,
	100% {
		transform: translateY(0);
	}
	30% {
		transform: translateY(-10px);
	}
}

.typing-dot {
	animation: typingDots 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
	animation-delay: -0.32s;
}

.typing-dot:nth-child(2) {
	animation-delay: -0.16s;
}

/* Online status indicator */
.online-indicator {
	position: relative;
}

.online-indicator::after {
	content: "";
	position: absolute;
	bottom: 2px;
	right: 2px;
	width: 12px;
	height: 12px;
	background-color: #4caf50;
	border: 2px solid white;
	border-radius: 50%;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
	.chat-sidebar {
		width: 100% !important;
	}

	.chat-area {
		width: 100% !important;
	}
}

/* Custom Material-UI overrides */
.MuiListItemButton-root.Mui-selected {
	background-color: rgba(0, 168, 132, 0.08) !important;
}

.MuiListItemButton-root:hover {
	background-color: rgba(0, 0, 0, 0.04);
}

/* WhatsApp-like message bubbles */
.message-bubble-own {
	background-color: #dcf8c6 !important;
	border-top-right-radius: 0 !important;
}

.message-bubble-other {
	background-color: white !important;
	border-top-left-radius: 0 !important;
}

/* Loading spinner */
@keyframes spin {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}
