import { useAuthStore } from "../stores/authStore";

const API_BASE_URL = "http://localhost:8000";

class ApiClient {
	private baseURL: string;

	constructor(baseURL: string = API_BASE_URL) {
		this.baseURL = baseURL;
	}

	private getAuthHeaders(): HeadersInit {
		const token = useAuthStore.getState().token;
		return {
			"Content-Type": "application/json",
			...(token && { Authorization: `Bearer ${token}` }),
		};
	}

	private async request<T>(
		endpoint: string,
		options: RequestInit = {},
	): Promise<T> {
		const url = `${this.baseURL}${endpoint}`;
		const config: RequestInit = {
			headers: this.getAuthHeaders(),
			...options,
		};

		const response = await fetch(url, config);

		if (!response.ok) {
			const errorData = await response.json().catch(() => ({}));
			throw new Error(
				errorData.detail || `HTTP error! status: ${response.status}`,
			);
		}

		return response.json();
	}

	// Auth endpoints
	async login(username: string, password: string) {
		return this.request<{
			access_token: string;
			token_type: string;
			user_jid: string;
			username: string;
			display_name: string;
			user_id: string;
			avatar?: string;
			status?: string;
		}>("/api/auth/login", {
			method: "POST",
			body: JSON.stringify({ username, password }),
		});
	}

	async register(username: string, password: string, displayName: string) {
		return this.request<{ success: boolean; message: string }>(
			"/api/auth/register",
			{
				method: "POST",
				body: JSON.stringify({
					username,
					password,
					display_name: displayName,
				}),
			},
		);
	}

	// Users endpoints
	async searchUsers(query: string, limit: number = 10) {
		return this.request<
			Array<{
				username: string;
				display_name: string;
				avatar_url?: string;
			}>
		>(`/api/users/search?q=${encodeURIComponent(query)}&limit=${limit}`);
	}

	async getUserProfile(username: string) {
		return this.request<{
			username: string;
			display_name: string;
			email?: string;
			phone?: string;
			avatar_url?: string;
			status?: string;
			last_seen?: string;
			created_at: string;
		}>(`/api/users/profile/${username}`);
	}

	async addContact(username: string) {
		return this.request<{ success: boolean; message: string }>(
			"/api/contacts/add",
			{
				method: "POST",
				body: JSON.stringify({ username }),
			},
		);
	}

	async getContacts() {
		return this.request<
			Array<{
				contact_jid: string;
				contact_username: string;
				contact_display_name: string;
				added_at: string;
				last_message_at?: string;
				last_message_preview?: string;
				unread_count: number;
				is_pinned: boolean;
				is_muted: boolean;
			}>
		>("/api/contacts/list");
	}

	async getChats() {
		return this.request<
			Array<{
				chat_jid: string;
				chat_type: string;
				chat_name: string;
				chat_avatar?: string;
				last_message_body?: string;
				last_message_at?: string;
				last_message_from?: string;
				unread_count: number;
				total_messages: number;
				is_pinned: boolean;
				is_muted: boolean;
				is_archived: boolean;
				updated_at: string;
			}>
		>("/api/contacts/chats");
	}

	async markChatAsRead(chatJid: string) {
		return this.request<{ success: boolean; message: string }>(
			`/api/contacts/chats/${encodeURIComponent(chatJid)}/mark-read`,
			{
				method: "POST",
			},
		);
	}

	// Статус пользователя теперь управляется через XMPP presence
	// История сообщений теперь получается через XMPP MAM
	// Отправка сообщений теперь через XMPP

	// Calls endpoints
	async createCall(participants: string[], callType: "audio" | "video") {
		return this.request<{
			call_id: string;
			room_name: string;
			livekit_url: string;
			tokens: Record<string, string>;
		}>("/api/calls/create", {
			method: "POST",
			body: JSON.stringify({
				participants,
				call_type: callType,
			}),
		});
	}

	async getCallInfo(callId: string) {
		return this.request<{
			id: string;
			livekit_room_id: string;
			call_type: string;
			initiator_jid: string;
			participants: string[];
			status: string;
			started_at?: string;
			duration_seconds?: number;
		}>(`/api/calls/${callId}`);
	}

	async getCallToken(callId: string) {
		return this.request<{
			token: string;
			livekit_url: string;
			room_name: string;
		}>(`/api/calls/${callId}/token`);
	}

	async answerCall(callId: string) {
		return this.request<{ success: boolean; message: string }>(
			`/api/calls/${callId}/answer`,
			{
				method: "POST",
			},
		);
	}

	async rejectCall(callId: string) {
		return this.request<{ success: boolean; message: string }>(
			`/api/calls/${callId}/reject`,
			{
				method: "POST",
			},
		);
	}

	async endCall(callId: string) {
		return this.request<{ success: boolean; message: string }>(
			`/api/calls/${callId}/end`,
			{
				method: "POST",
			},
		);
	}

	// Files endpoints
	async uploadFile(file: File) {
		const formData = new FormData();
		formData.append("file", file);

		const token = useAuthStore.getState().token;
		const response = await fetch(`${this.baseURL}/api/files/upload`, {
			method: "POST",
			headers: {
				...(token && { Authorization: `Bearer ${token}` }),
			},
			body: formData,
		});

		if (!response.ok) {
			const errorData = await response.json().catch(() => ({}));
			throw new Error(
				errorData.detail || `HTTP error! status: ${response.status}`,
			);
		}

		return response.json() as Promise<{
			file_id: string;
			url: string;
		}>;
	}
}

export const apiClient = new ApiClient();
