import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "../api/client";
import { useAuthStore } from "../stores/authStore";
import { useChatStore } from "../stores/chatStore";

// Auth hooks
export const useLogin = () => {
	const setAuth = useAuthStore((state) => state.setAuth);

	return useMutation({
		mutationFn: ({
			username,
			password,
		}: {
			username: string;
			password: string;
		}) => apiClient.login(username, password).then(data => ({ data, password })),
		onSuccess: ({ data, password }) => {
			const user = {
				id: data.user_id,
				username: data.username,
				displayName: data.display_name,
				userJid: data.user_jid,
				avatar: data.avatar,
				status: data.status,
			};
			setAuth(user, data.access_token, password);
		},
	});
};

export const useRegister = () => {
	return useMutation({
		mutationFn: ({
			username,
			password,
			displayName,
		}: {
			username: string;
			password: string;
			displayName: string;
		}) => apiClient.register(username, password, displayName),
	});
};

// Users hooks
export const useSearchUsers = (query: string, enabled: boolean = true) => {
	return useQuery({
		queryKey: ["users", "search", query],
		queryFn: () => apiClient.searchUsers(query),
		enabled: enabled && query.length >= 2,
		staleTime: 30000, // 30 seconds
	});
};

export const useUserProfile = (username: string) => {
	return useQuery({
		queryKey: ["users", "profile", username],
		queryFn: () => apiClient.getUserProfile(username),
		staleTime: 60000, // 1 minute
	});
};

export const useAddContact = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (username: string) => apiClient.addContact(username),
		onSuccess: () => {
			// Invalidate and refetch contacts
			queryClient.invalidateQueries({ queryKey: ["contacts"] });
		},
	});
};

// Статус пользователя теперь управляется через XMPP presence
// Отправка сообщений теперь через XMPP
// История сообщений теперь получается через XMPP MAM

// Calls hooks
export const useCreateCall = () => {
	return useMutation({
		mutationFn: ({
			participants,
			callType,
		}: {
			participants: string[];
			callType: "audio" | "video";
		}) => apiClient.createCall(participants, callType),
	});
};

export const useCallInfo = (callId: string | null) => {
	return useQuery({
		queryKey: ["calls", callId],
		queryFn: () =>
			callId ? apiClient.getCallInfo(callId) : Promise.resolve(null),
		enabled: !!callId,
		refetchInterval: 5000, // Refetch every 5 seconds during call
	});
};

export const useCallToken = (callId: string | null) => {
	return useQuery({
		queryKey: ["calls", callId, "token"],
		queryFn: () =>
			callId ? apiClient.getCallToken(callId) : Promise.resolve(null),
		enabled: !!callId,
		staleTime: 300000, // 5 minutes
	});
};

export const useAnswerCall = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (callId: string) => apiClient.answerCall(callId),
		onSuccess: (_, callId) => {
			queryClient.invalidateQueries({ queryKey: ["calls", callId] });
		},
	});
};

export const useRejectCall = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (callId: string) => apiClient.rejectCall(callId),
		onSuccess: (_, callId) => {
			queryClient.invalidateQueries({ queryKey: ["calls", callId] });
		},
	});
};

export const useEndCall = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (callId: string) => apiClient.endCall(callId),
		onSuccess: (_, callId) => {
			queryClient.invalidateQueries({ queryKey: ["calls", callId] });
		},
	});
};

// Contacts hooks
export const useContacts = () => {
	return useQuery({
		queryKey: ["contacts"],
		queryFn: () => apiClient.getContacts(),
		staleTime: 60000, // 1 minute
	});
};

export const useChats = () => {
	return useQuery({
		queryKey: ["chats"],
		queryFn: () => apiClient.getChats(),
		staleTime: 30000, // 30 seconds
	});
};

export const useMarkChatAsRead = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: (chatJid: string) => apiClient.markChatAsRead(chatJid),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["chats"] });
		},
	});
};

// Files hooks
export const useUploadFile = () => {
	return useMutation({
		mutationFn: (file: File) => apiClient.uploadFile(file),
	});
};
