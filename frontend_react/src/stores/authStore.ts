import { create } from "zustand";
import { persist } from "zustand/middleware";

export interface User {
	id: string;
	username: string;
	displayName: string;
	userJid: string;
	password?: string; // Нужен для XMPP аутентификации
	avatar?: string;
	status?: string;
	lastSeen?: Date;
}

interface AuthState {
	user: User | null;
	token: string | null;
	password: string | null; // Храним пароль для XMPP
	isAuthenticated: boolean;
	setAuth: (user: User, token: string, password: string) => void;
	clearAuth: () => void;
	updateUser: (updates: Partial<User>) => void;
}

export const useAuthStore = create<AuthState>()(
	persist(
		(set, get) => ({
			user: null,
			token: null,
			password: null,
			isAuthenticated: false,

			setAuth: (user: User, token: string, password: string) => {
				set({
					user: { ...user, password },
					token,
					password,
					isAuthenticated: true,
				});
			},

			clearAuth: () => {
				set({
					user: null,
					token: null,
					password: null,
					isAuthenticated: false,
				});
			},

			updateUser: (updates: Partial<User>) => {
				const { user } = get();
				if (user) {
					set({
						user: { ...user, ...updates },
					});
				}
			},
		}),
		{
			name: "auth-storage",
		},
	),
);
