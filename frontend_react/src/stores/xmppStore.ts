import { create } from "zustand";
import { createClient, type Agent } from "stanza";
import type { Message } from "./chatStore";
import { useChatStore } from "./chatStore";

// --- Singleton XMPP Client Instance ---
// This client is created once and lives for the duration of the app.
// This avoids all issues with React lifecycle and component re-renders.
let client: Agent | null = null;

const XMPP_WS_URL = "ws://localhost:5280/websocket";

// --- Zustand Store Definition ---

interface XMPPState {
	isConnected: boolean;
	jid: string;
	// Actions
	connect: (jid: string, password: string) => void;
	disconnect: () => void;
	sendMessage: (to: string, body: string) => void;
	sendPresence: (show: string, status: string) => void;
	fetchMessageHistory: (
		chatJid: string,
		options?: { after?: string; before?: string; max?: number },
	) => Promise<Message[]>;
}

export const useXMPPStore = create<XMPPState>((set, get) => ({
	isConnected: false,
	jid: "",

	connect: (jid, password) => {
		if (client) {
			console.log("[XMPP] Client already exists. Skipping creation.");
			return;
		}

		console.log(`[XMPP] 🚀 Initiating connection for ${jid}...`);

		client = createClient({
			jid,
			password,
			transports: { websocket: XMPP_WS_URL },
		});

		

		client.on("*", (eventName, data) => {
			console.log(`[XMPP Event] ${eventName}`, data);
		});

		client.on("session:started", () => {
			console.log("[XMPP] ✅ Session started! Client is fully connected.");
			set({ isConnected: true, jid: client?.jid.bare });
			client?.sendPresence();
			client?.getRoster();
		});

		client.on("auth:failed", (err) => {
			console.error("[XMPP] ❌ Authentication failed:", err);
			set({ isConnected: false });
			client = null;
		});

		client.on("disconnected", () => {
			console.warn("[XMPP] 🔌 Disconnected.");
			set({ isConnected: false });
			client = null;
		});

		client.on("message", (msg) => {
			if (msg.type === "chat" && msg.body) {
				const fromJid = msg.from.toString();
				const toJid = msg.to.toString();
				const message: Message = {
					id: msg.id || Date.now().toString(),
					messageId: msg.id || Date.now().toString(),
					fromJid: fromJid,
					toJid: toJid,
					body: msg.body,
					messageType: "chat",
					timestamp: new Date(),
					isRead: false,
					isSent: true,
					isDelivered: true,
					isOwn: fromJid.startsWith(get().jid),
				};
				useChatStore.getState().handleIncomingMessage(message);
			}
		});

		client.connect();
	},

	disconnect: () => {
		if (client) {
			console.log("[XMPP] 🛑 Disconnecting...");
			client.disconnect();
		}
	},

	sendMessage: (to, body) => {
		if (!client || !get().isConnected) {
			console.error("[XMPP] Cannot send message, client not connected.");
			return;
		}
		client.sendMessage({ to, body, type: "chat" });
	},

	sendPresence: (show, status) => {
		if (!client || !get().isConnected) {
			console.error("[XMPP] Cannot send presence, client not connected.");
			return;
		}
		client.sendPresence({ show, status });
	},

	fetchMessageHistory: async (
		chatJid: string,
		options?: { after?: string; before?: string; max?: number },
	): Promise<Message[]> => {
		if (!client || !get().isConnected) {
			console.error("[XMPP] Attempted to fetch history while disconnected.");
			return [];
		}

		const paging = {
			max: options?.max || 100,
			...(options?.after && { after: options.after }),
			...(options?.before && { before: options.before }),
		};

		console.log(`[XMPP] Fetching history for ${chatJid} with options:`, paging);
		try {
			const response = await client.searchHistory({ with: chatJid, paging });
			console.log(`[XMPP] History response for ${chatJid}:`, response);

			const historyMessages: Message[] = response.results.reduce(
				(acc: Message[], item: any) => {
					const msg = item.item && item.item.message;
					if (msg) {
						const delay = item.item && item.item.delay;
						const from = msg.from.bare;
						const to = msg.to.bare;
						const isOwn = from === client?.jid.bare;

						acc.push({
							id: msg.id || item.id,
							messageId: msg.id || item.id,
							fromJid: from,
							toJid: to,
							body: msg.body,
							messageType: "chat",
							timestamp: new Date(delay ? delay.timestamp : Date.now()),
							isRead: true,
							isSent: true,
							isDelivered: true,
							isOwn: isOwn,
						});
					}
					return acc;
				},
				[],
			);

			const sortedMessages = historyMessages.sort(
				(a, b) => a.timestamp.getTime() - b.timestamp.getTime(),
			);
			console.log(`[XMPP] Parsed and returning ${sortedMessages.length} history messages.`);
			return sortedMessages;
		} catch (error) {
			console.error(`[XMPP] Error fetching history for ${chatJid}:`, error);
			return [];
		}
	},
}));
