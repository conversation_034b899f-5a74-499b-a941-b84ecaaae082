import { create } from "zustand";

export interface CallParticipant {
	userJid: string;
	username: string;
	displayName: string;
	avatar?: string;
	isAudioEnabled: boolean;
	isVideoEnabled: boolean;
	isSpeaking: boolean;
}

export interface Call {
	id: string;
	roomName: string;
	callType: "audio" | "video";
	initiatorJid: string;
	participants: CallParticipant[];
	status: "ringing" | "connecting" | "connected" | "ended";
	startedAt?: Date;
	duration?: number;
}

interface CallState {
	currentCall: Call | null;
	isCallModalOpen: boolean;
	localAudioEnabled: boolean;
	localVideoEnabled: boolean;

	// Actions
	setCurrentCall: (call: Call | null) => void;
	updateCall: (updates: Partial<Call>) => void;
	setCallModalOpen: (open: boolean) => void;
	setLocalAudioEnabled: (enabled: boolean) => void;
	setLocalVideoEnabled: (enabled: boolean) => void;

	// Computed
	isInCall: () => boolean;
}

export const useCallStore = create<CallState>((set, get) => ({
	currentCall: null,
	isCallModalOpen: false,
	localAudioEnabled: true,
	localVideoEnabled: true,

	setCurrentCall: (call: Call | null) => {
		set({ currentCall: call });
	},

	updateCall: (updates: Partial<Call>) => {
		set((state) => ({
			currentCall: state.currentCall
				? { ...state.currentCall, ...updates }
				: null,
		}));
	},

	setCallModalOpen: (open: boolean) => {
		set({ isCallModalOpen: open });
	},

	setLocalAudioEnabled: (enabled: boolean) => {
		set({ localAudioEnabled: enabled });
	},

	setLocalVideoEnabled: (enabled: boolean) => {
		set({ localVideoEnabled: enabled });
	},

	isInCall: () => {
		const { currentCall } = get();
		return !!currentCall && currentCall.status !== "ended";
	},
}));
