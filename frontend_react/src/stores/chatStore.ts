import { create } from "zustand";
import { db } from "../db";
import { useXMPPStore } from "./xmppStore";

export interface Message {
	id: string;
	messageId: string;
	fromJid: string;
	toJid: string;
	body: string;
	messageType: "chat" | "groupchat";
	timestamp: Date;
	isRead: boolean;
	isSent: boolean;
	isDelivered: boolean;
	isOwn: boolean;
	replyTo?: string;
	attachments?: MessageAttachment[];
    internalId?: number; // For Dexie
}

export interface MessageAttachment {
	id: string;
	type: "image" | "video" | "audio" | "document";
	url: string;
	filename: string;
	size: number;
	mimeType: string;
}

export interface Chat {
	id: string;
	chatJid: string;
	chatType: "direct" | "group";
	name: string;
	avatar?: string;
	lastMessage?: string;
	lastMessageTime?: Date;
	unreadCount: number;
	isPinned: boolean;
	isMuted: boolean;
	participants?: string[];
}

export interface Contact {
	userJid: string;
	username: string;
	displayName: string;
	avatar?: string;
	status?: string;
	lastSeen?: Date;
	isOnline?: boolean;
}

interface ChatState {
	currentChatJid: string | null;
	messages: Message[];
	chats: Chat[];
	contacts: Contact[];

	// Actions
	setCurrentChat: (chatJid: string | null) => void;
	addMessage: (message: Message) => void;
	updateMessage: (messageId: string, updates: Partial<Message>) => void;
	setChats: (chats: Chat[]) => void;
	addContact: (contact: Contact) => void;
	updateContact: (userJid: string, updates: Partial<Contact>) => void;
	setContacts: (contacts: Contact[]) => void;

	// Real-time updates
	handleIncomingMessage: (message: Message) => void;
	markMessagesAsRead: (chatJid: string) => void;

	// Caching
	loadChatHistory: (chatJid: string) => Promise<void>;
}

export const useChatStore = create<ChatState>((set, get) => ({
	currentChatJid: null,
	messages: [],
	chats: [],
	contacts: [],

	setCurrentChat: (chatJid: string | null) => {
		set({ currentChatJid: chatJid, messages: [] }); // Clear messages for new chat
        if (chatJid) {
            get().loadChatHistory(chatJid);
        }
	},

    loadChatHistory: async (chatJid: string) => {
        const myJid = useXMPPStore.getState().jid;
        if (!myJid) return;

        console.log(`[Cache] Loading history for ${chatJid} from IndexedDB...`);
        const cachedMessages = await db.messages
            .where('[fromJid+toJid]').equals([chatJid, myJid])
            .or('[fromJid+toJid]').equals([myJid, chatJid])
            .sortBy('timestamp');
        
        console.log(`[Cache] Found ${cachedMessages.length} messages in local DB.`);
        set({ messages: cachedMessages });

        const latestMessage = cachedMessages[cachedMessages.length - 1];
        const after = latestMessage?.messageId;

        console.log(`[XMPP] Fetching messages since: ${after || 'the beginning'}`);
        const newMessages = await useXMPPStore.getState().fetchMessageHistory(chatJid, { after });

        if (newMessages.length > 0) {
            console.log(`[Cache] Received ${newMessages.length} new messages. Updating cache.`);
            await db.messages.bulkPut(newMessages);
            // Combine and sort
            const allMessages = [...cachedMessages, ...newMessages].sort(
                (a, b) => a.timestamp.getTime() - b.timestamp.getTime()
            );
            set({ messages: allMessages });
        }
    },

	addMessage: (message: Message) => {
        // Optimistically add to state and save to DB
		set((state) => ({
			messages: [...state.messages, message],
		}));
        db.messages.put(message);
	},

	updateMessage: (messageId: string, updates: Partial<Message>) => {
		set((state) => ({
			messages: state.messages.map((msg) =>
				msg.messageId === messageId ? { ...msg, ...updates } : msg,
			),
		}));
        // Also update in DB
        db.messages.update(messageId, updates);
	},

	addChat: (chat: Chat) => {
		set((state) => {
			const existingIndex = state.chats.findIndex(
				(c) => c.chatJid === chat.chatJid,
			);
			if (existingIndex >= 0) {
				const updatedChats = [...state.chats];
				updatedChats[existingIndex] = chat;
				return { chats: updatedChats };
			} else {
				return { chats: [...state.chats, chat] };
			}
		});
	},

	updateChat: (chatJid: string, updates: Partial<Chat>) => {
		set((state) => ({
			chats: state.chats.map((chat) =>
				chat.chatJid === chatJid ? { ...chat, ...updates } : chat,
			),
		}));
	},

	setChats: (chats: Chat[]) => {
		set({ chats });
	},

	addContact: (contact: Contact) => {
		set((state) => {
			const existingIndex = state.contacts.findIndex(
				(c) => c.userJid === contact.userJid,
			);
			if (existingIndex >= 0) {
				const updatedContacts = [...state.contacts];
				updatedContacts[existingIndex] = contact;
				return { contacts: updatedContacts };
			} else {
				return { contacts: [...state.contacts, contact] };
			}
		});
	},

	updateContact: (userJid: string, updates: Partial<Contact>) => {
		set((state) => ({
			contacts: state.contacts.map((contact) =>
				contact.userJid === userJid ? { ...contact, ...updates } : contact,
			),
		}));
	},

	setContacts: (contacts: Contact[]) => {
		set({ contacts });
	},

	handleIncomingMessage: (message: Message) => {
		const { currentChatJid, chats, contacts } = get();

		const chatJid = message.isOwn ? message.toJid : message.fromJid;

        // Save to DB
        db.messages.put(message);

		// Add message to messages if it belongs to the current chat
		if (chatJid === currentChatJid) {
			set(state => ({
				messages: [...state.messages, message],
			}));
		}

		// Update or create chat
		const existingChat = chats.find((c) => c.chatJid === chatJid);

		if (existingChat) {
			// Update existing chat
			set((state) => ({
				chats: state.chats.map((chat) =>
					chat.chatJid === chatJid
						? {
								...chat,
								lastMessage: message.body,
								lastMessageTime: message.timestamp,
								unreadCount:
									chatJid === currentChatJid ? 0 : chat.unreadCount + 1,
							}
						: chat,
				),
			}));
		} else {
			// Create new chat
			const senderUsername = message.fromJid.split("@")[0];
			const senderContact = contacts.find((c) => c.userJid === message.fromJid);

			const newChat: Chat = {
				id: chatJid,
				chatJid,
				chatType: "direct",
				name: senderContact?.displayName || senderUsername,
				lastMessage: message.body,
				lastMessageTime: message.timestamp,
				unreadCount: chatJid === currentChatJid ? 0 : 1,
				isPinned: false,
				isMuted: false,
			};

			set((state) => ({
				chats: [...state.chats, newChat],
			}));

			// Also add sender as contact if not exists
			if (!senderContact && !message.isOwn) {
				const newContact: Contact = {
					userJid: message.fromJid,
					username: senderUsername,
					displayName: senderUsername,
					isOnline: true, // Assume online since they just sent a message
				};

				set((state) => ({
					contacts: [...state.contacts, newContact],
				}));
			}
		}
	},

	markMessagesAsRead: (chatJid: string) => {
		set((state) => ({
			messages: state.messages.map((msg) =>
				(msg.fromJid === chatJid || msg.toJid === chatJid) && !msg.isRead
					? { ...msg, isRead: true }
					: msg,
			),
			chats: state.chats.map((chat) =>
				chat.chatJid === chatJid ? { ...chat, unreadCount: 0 } : chat,
			),
		}));
	},
}));
