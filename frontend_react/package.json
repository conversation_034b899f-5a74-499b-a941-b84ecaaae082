{"name": "frontend_react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@livekit/components-react": "^2.9.14", "@mui/icons-material": "^7.3.2", "@mui/lab": "^7.0.0-beta.17", "@mui/material": "^7.3.2", "@tanstack/react-query": "^5.87.1", "@tanstack/react-query-devtools": "^5.87.3", "date-fns": "^4.1.0", "dexie": "^4.2.0", "livekit-client": "^2.15.6", "lucide-react": "^0.542.0", "node-fetch": "^2.7.0", "react": "^19.1.1", "react-dom": "^19.1.1", "stanza": "^12.21.0", "ws": "^8.18.3", "zustand": "^5.0.8"}, "devDependencies": {"@biomejs/biome": "^2.2.3", "@eslint/js": "^9.33.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^4.7.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^5.4.20"}}