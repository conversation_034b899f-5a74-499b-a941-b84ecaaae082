#!/usr/bin/env node

/**
 * 🧪 Frontend-Backend Integration Test
 *
 * Тестирует интеграцию React фронтенда с FastAPI бекендом:
 * - Аутентификация через API
 * - WebSocket соединение
 * - Отправка и получение сообщений
 */

const WebSocket = require('ws');
const fetch = require('node-fetch');

const API_BASE = 'http://localhost:8000';
const WS_BASE = 'ws://localhost:8000';

class IntegrationTest {
    constructor() {
        this.testResults = [];
    }

    log(message, type = 'info') {
        const timestamp = new Date().toISOString();
        const emoji = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';
        console.log(`${timestamp} ${emoji} ${message}`);

        this.testResults.push({
            timestamp,
            type,
            message
        });
    }

    async testAPI(endpoint, method = 'GET', body = null, headers = {}) {
        try {
            const response = await fetch(`${API_BASE}${endpoint}`, {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    ...headers
                },
                body: body ? JSON.stringify(body) : null
            });

            const data = await response.json();
            return { status: response.status, data };
        } catch (error) {
            throw new Error(`API request failed: ${error.message}`);
        }
    }

    async testAuthentication() {
        this.log('🔐 Testing authentication...');

        try {
            // Тест логина
            const loginResult = await this.testAPI('/api/auth/login', 'POST', {
                username: 'test_user_1',
                password: 'password1'
            });

            if (loginResult.status === 200 && loginResult.data.access_token) {
                this.log('Login successful', 'success');
                return loginResult.data.access_token;
            } else {
                throw new Error('Login failed');
            }
        } catch (error) {
            this.log(`Authentication failed: ${error.message}`, 'error');
            return null;
        }
    }

    async testWebSocketConnection(token) {
        this.log('🔌 Testing WebSocket connection...');

        return new Promise((resolve, reject) => {
            try {
                const ws = new WebSocket(`${WS_BASE}/api/websocket/ws?token=${token}`);
                let connected = false;

                ws.on('open', () => {
                    connected = true;
                    this.log('WebSocket connected', 'success');

                    // Тест ping
                    ws.send(JSON.stringify({ type: 'ping' }));
                });

                ws.on('message', (data) => {
                    try {
                        const message = JSON.parse(data);

                        if (message.type === 'pong') {
                            this.log('Ping/Pong test passed', 'success');
                            ws.close();
                            resolve(true);
                        }
                    } catch (error) {
                        this.log(`WebSocket message error: ${error.message}`, 'error');
                    }
                });

                ws.on('error', (error) => {
                    this.log(`WebSocket error: ${error.message}`, 'error');
                    reject(error);
                });

                ws.on('close', () => {
                    if (!connected) {
                        this.log('WebSocket connection failed', 'error');
                        reject(new Error('Connection failed'));
                    }
                });

                // Timeout после 10 секунд
                setTimeout(() => {
                    if (!connected) {
                        ws.close();
                        reject(new Error('Connection timeout'));
                    }
                }, 10000);

            } catch (error) {
                this.log(`WebSocket test failed: ${error.message}`, 'error');
                reject(error);
            }
        });
    }

    async testMessageSending(token) {
        this.log('💬 Testing message sending...');

        try {
            const result = await this.testAPI('/api/messages/send', 'POST', {
                to_jid: 'test_user_2@localhost',
                body: 'Test message from integration test',
                message_type: 'chat'
            }, {
                'Authorization': `Bearer ${token}`
            });

            if (result.status === 200) {
                this.log('Message sent successfully', 'success');
                return true;
            } else {
                throw new Error('Message sending failed');
            }
        } catch (error) {
            this.log(`Message sending failed: ${error.message}`, 'error');
            return false;
        }
    }

    async testContactsAPI(token) {
        this.log('👥 Testing contacts API...');

        try {
            const result = await this.testAPI('/api/contacts', 'GET', null, {
                'Authorization': `Bearer ${token}`
            });

            if (result.status === 200) {
                this.log(`Contacts loaded: ${result.data.length} contacts`, 'success');
                return true;
            } else {
                throw new Error('Contacts loading failed');
            }
        } catch (error) {
            this.log(`Contacts API failed: ${error.message}`, 'error');
            return false;
        }
    }

    async runAllTests() {
        this.log('🚀 Starting Frontend-Backend Integration Tests');
        this.log('=' .repeat(60));

        const startTime = Date.now();
        let passedTests = 0;
        let totalTests = 4;

        try {
            // 1. Тест аутентификации
            const token = await this.testAuthentication();
            if (token) {
                passedTests++;

                // 2. Тест WebSocket соединения
                try {
                    await this.testWebSocketConnection(token);
                    passedTests++;
                } catch (error) {
                    this.log(`WebSocket test failed: ${error.message}`, 'error');
                }

                // 3. Тест отправки сообщений
                if (await this.testMessageSending(token)) {
                    passedTests++;
                }

                // 4. Тест API контактов
                if (await this.testContactsAPI(token)) {
                    passedTests++;
                }
            }

        } catch (error) {
            this.log(`Test suite failed: ${error.message}`, 'error');
        }

        const duration = Date.now() - startTime;

        this.log('=' .repeat(60));
        this.log(`📊 Test Results: ${passedTests}/${totalTests} passed`);
        this.log(`⏱️  Duration: ${duration}ms`);

        if (passedTests === totalTests) {
            this.log('🎉 ALL TESTS PASSED! Frontend-Backend integration is working!', 'success');
        } else {
            this.log(`❌ ${totalTests - passedTests} tests failed`, 'error');
        }

        return passedTests === totalTests;
    }
}

// Запуск тестов
if (require.main === module) {
    const test = new IntegrationTest();
    test.runAllTests()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('Test suite crashed:', error);
            process.exit(1);
        });
}

module.exports = IntegrationTest;
