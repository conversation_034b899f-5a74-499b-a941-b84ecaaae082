# 🚀 Быстрый старт с XMPP архитектурой

## Что изменилось

Система переведена на стандартную XMPP архитектуру:
- ❌ Убраны кастомные WebSocket эндпоинты
- ✅ Добавлен прямой XMPP клиент (stanza.js)
- ✅ Real-time функции теперь через ejabberd
- ✅ FastAPI только для бизнес-логики

## Запуск системы

### 1. Запустите все сервисы
```bash
docker-compose up -d
```

### 2. Проверьте статус
```bash
docker-compose ps
```

Должны быть запущены:
- ✅ postg<PERSON> (база данных)
- ✅ redis (кэш)
- ✅ minio (файлы)
- ✅ ejabberd (XMPP сервер)
- ✅ livekit (звонки)
- ✅ fastapi_backend (API)

### 3. Проверьте подключения

**FastAPI:**
```bash
curl http://localhost:8000/api/health
```

**ejabberd XMPP:**
```bash
curl http://localhost:5280/api/status
```

## Тестирование

### 1. Запустите фронтенд
```bash
cd frontend_react
npm install
npm run dev
```

### 2. Откройте http://localhost:5173

### 3. Зарегистрируйтесь
- Создайте нового пользователя
- Войдите в систему
- Проверьте статус XMPP соединения в компоненте "XMPP Тест"

### 4. Создайте второго пользователя
- Откройте инкогнито окно
- Зарегистрируйте второго пользователя
- Добавьте первого пользователя в контакты
- Отправьте сообщение

## Архитектура соединений

```
Клиент (React)
    │
    ├─── HTTP ────► FastAPI:8000 (бизнес-логика)
    │
    └─── XMPP ────► ejabberd:5280/websocket (real-time)
```

## Порты и сервисы

- **5173** - React фронтенд
- **8000** - FastAPI backend
- **5280** - ejabberd WebSocket + Admin
- **5222** - ejabberd XMPP (для внешних клиентов)
- **7880** - LiveKit медиа сервер
- **5432** - PostgreSQL
- **6379** - Redis
- **9000/9001** - MinIO S3

## Отладка

### Проверка XMPP соединения
```bash
# Логи ejabberd
docker-compose logs -f ejabberd

# Проверка WebSocket эндпоинта
curl -i -N -H "Connection: Upgrade" \
     -H "Upgrade: websocket" \
     -H "Sec-WebSocket-Version: 13" \
     -H "Sec-WebSocket-Key: test" \
     http://localhost:5280/websocket
```

### Проверка базы данных
```bash
# Подключение к PostgreSQL
docker-compose exec postgres psql -U postgres -d messenger

# Проверка пользователей ejabberd
\dt
SELECT * FROM users;
```

### Проверка API
```bash
# Регистрация через API
curl -X POST http://localhost:8000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username": "test", "password": "test123", "display_name": "Test User"}'

# Логин
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "test", "password": "test123"}'
```

## Возможные проблемы

### 1. XMPP не подключается
- Проверьте, что ejabberd запущен: `docker-compose ps ejabberd`
- Проверьте логи: `docker-compose logs ejabberd`
- Убедитесь, что порт 5280 доступен
- Проверьте WebSocket: `curl -I http://localhost:5280/websocket`

### 2. Ошибка "No endpoints found for the requested transports"
- Это означает, что stanza.js пытается использовать автоматическое обнаружение
- Убедитесь, что в XMPPProvider используется `transports: { websocket: URL }`
- Добавьте `allowInsecureConnection: true` для локальной разработки

### 3. Сообщения не отправляются
- Проверьте XMPP соединение в браузере (F12 → Network → WS)
- Убедитесь, что пользователь зарегистрирован в ejabberd: `docker-compose exec ejabberd ejabberdctl registered_users localhost`
- Проверьте логи ejabberd на ошибки аутентификации

### 4. Звонки не работают
- Проверьте статус LiveKit: `curl http://localhost:7880`
- Убедитесь, что токены генерируются через FastAPI
- Проверьте настройки WebRTC в браузере

### 5. Файлы не загружаются
- Проверьте MinIO: http://localhost:9001
- Убедитесь, что bucket создан
- Проверьте настройки CORS

## Следующие шаги

1. **Изучите XMPP Test компонент** - он показывает статус соединения
2. **Добавьте контакты** - используйте поиск пользователей
3. **Протестируйте звонки** - кнопки в списке контактов
4. **Загрузите файлы** - через интерфейс чата
5. **Создайте группы** - через ejabberd admin панель

## Документация

- [XMPP Migration Guide](docs/XMPP_MIGRATION.md) - подробности миграции
- [ejabberd Configuration](ejabberd/ejabberd.yml) - настройки XMPP сервера
- [API Documentation](http://localhost:8000/docs) - Swagger UI
- [Admin Panel](http://localhost:5280/admin) - ejabberd управление