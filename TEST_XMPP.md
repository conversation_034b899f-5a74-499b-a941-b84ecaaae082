# 🧪 Тестирование XMPP интеграции

## Статус миграции

✅ **Завершено:**
- Удалены кастомные WebSocket эндпоинты из FastAPI
- Создан XMPPProvider на основе stanza.js
- Создан XMPPStore на Zustand
- Интегрирован с существующими компонентами

✅ **Архитектура:**
- Клиент подключается к ejabberd:5280/websocket для real-time
- Клиент подключается к FastAPI:8000 для бизнес-логики
- Сообщения отправляются через XMPP стансы
- История сообщений через MAM (в разработке)

## Быстрый тест

### 1. Запустите систему
```bash
docker-compose up -d
```

### 2. Проверьте сервисы
```bash
# Все сервисы должны быть UP
docker-compose ps

# ejabberd WebSocket должен отвечать
curl -I http://localhost:5280/websocket

# FastAPI должен работать
curl http://localhost:8000/api/health
```

### 3. Запустите фронтенд
```bash
cd frontend_react
npm run dev
```

### 4. Тестирование в браузере

1. **Откройте** http://localhost:5173
2. **Зарегистрируйтесь** или войдите как существующий пользователь
3. **Проверьте XMPP статус** в компоненте "XMPP Тест"
4. **Отправьте тестовое сообщение** через кнопку в XMPP Тест

### 5. Проверьте логи

**Фронтенд (F12 → Console):**
```
✅ XMPP session started successfully
📤 Sending XMPP message to admin@localhost : Тестовое сообщение через XMPP
```

**ejabberd:**
```bash
docker-compose logs -f ejabberd | grep "Accepted c2s"
```

## Детальное тестирование

### Тест 1: XMPP подключение
```javascript
// В консоли браузера
console.log("XMPP connected:", window.xmppStore?.getState().isConnected);
```

### Тест 2: Отправка сообщения
```javascript
// В консоли браузера
window.xmppStore?.getState().sendMessage('admin@localhost', 'Test from console');
```

### Тест 3: Проверка пользователей в ejabberd
```bash
# Список зарегистрированных пользователей
docker-compose exec ejabberd ejabberdctl registered_users localhost

# Онлайн пользователи
docker-compose exec ejabberd ejabberdctl connected_users_info
```

### Тест 4: Двусторонний чат
1. Откройте два браузера/вкладки
2. Войдите как разные пользователи (например, alice и bob)
3. Добавьте друг друга в контакты
4. Отправьте сообщения в обе стороны

## Отладка

### Проблема: "No endpoints found"
**Причина:** stanza.js пытается использовать service discovery
**Решение:** Убедитесь, что используется прямой WebSocket URL:
```javascript
transports: {
    websocket: 'ws://localhost:5280/websocket'
}
```

### Проблема: Аутентификация не работает
**Проверьте:**
1. Пользователь зарегистрирован в ejabberd
2. Пароль правильный
3. Логи ejabberd показывают попытки подключения

### Проблема: Сообщения не доходят
**Проверьте:**
1. Оба пользователя онлайн
2. XMPP соединение активно
3. Логи ejabberd показывают доставку сообщений

## Следующие шаги

### В разработке:
- [ ] История сообщений через MAM (XEP-0313)
- [ ] Индикаторы "печатает" (XEP-0085)
- [ ] Статусы доставки (XEP-0184)
- [ ] Групповые чаты через MUC
- [ ] Файловые вложения

### Готово к использованию:
- [x] Базовые сообщения 1-на-1
- [x] Статусы присутствия
- [x] Автоматическое переподключение
- [x] Интеграция с существующим UI

## Полезные команды

```bash
# Перезапуск ejabberd
docker-compose restart ejabberd

# Очистка логов
docker-compose logs --tail=0 -f ejabberd

# Создание тестового пользователя
docker-compose exec ejabberd ejabberdctl register testuser localhost testpass

# Проверка статуса ejabberd
docker-compose exec ejabberd ejabberdctl status
```