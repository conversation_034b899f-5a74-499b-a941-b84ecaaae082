# Миграция на XMPP архитектуру

## Обзор изменений

Система была переведена с кастомного WebSocket API на стандартную XMPP архитектуру с ejabberd сервером.

## Архитектура

### Разделение ответственности

**FastAPI Backend** - отвечает за:
- Регистрацию и аутентификацию пользователей
- Бизнес-логику (создание звонков, управление файлами)
- Синхронизацию контактов
- Управление профилями пользователей
- Генерацию токенов для LiveKit

**ejabberd XMPP Server** - отвечает за:
- Real-time сообщения (chat, groupchat)
- Управление присутствием (online/offline статусы)
- Хранение истории сообщений (mod_mam)
- Управление списками контактов (mod_roster)
- Уведомления о звонках

### Подключения клиента

Клиент устанавливает **два независимых соединения**:

1. **XMPP WebSocket** к ejabberd (порт 5280/5443)
   - Для real-time сообщений
   - Для статусов присутствия
   - Для приглашений на звонки

2. **HTTP API** к FastAPI (порт 8000)
   - Для бизнес-операций
   - Для получения токенов звонков
   - Для управления профилем

## Удаленные компоненты

### Backend
- `backend/api/messages.py` - сообщения теперь через ejabberd
- `backend/api/groups.py` - групповые чаты через mod_muc
- `backend/api/notifications.py` - уведомления через XMPP
- `backend/api/message_reactions.py` - реакции через XMPP extensions
- `backend/api/channels.py` - каналы через mod_muc
- `backend/api/websocket.py` - WebSocket через ejabberd

### Frontend
- `WebSocketProvider.tsx` - заменен на `XMPPProvider.tsx`
- `WebSocketTest.tsx` - заменен на `XMPPTest.tsx`

## Новые компоненты

### Frontend
- `XMPPProvider.tsx` - XMPP клиент на основе stanza.js
- `XMPPTest.tsx` - компонент для тестирования XMPP

## Конфигурация

### ejabberd
Конфигурация в `ejabberd/ejabberd.yml`:
- WebSocket эндпоинт: `/websocket` на портах 5280/5443
- Модули: mod_mam, mod_roster, mod_last, mod_offline
- База данных: PostgreSQL (общая с FastAPI)

### Frontend
- XMPP домен: `localhost`
- WebSocket URL: `ws://localhost:5280/websocket`
- Аутентификация: username/password (сохраняется в auth store)

## Процесс отправки сообщений

### Старый способ (WebSocket API)
1. Клиент → FastAPI WebSocket
2. FastAPI → База данных
3. FastAPI → Другие клиенты через WebSocket

### Новый способ (XMPP)
1. Клиент → ejabberd XMPP
2. ejabberd → База данных (mod_mam)
3. ejabberd → Получатель напрямую

## Процесс звонков

1. Клиент → FastAPI `/api/calls/create` (получение токена)
2. Клиент → ejabberd XMPP (отправка приглашения)
3. Получатель → FastAPI `/api/calls/answer` (подтверждение)
4. Оба клиента → LiveKit (медиа соединение)

## Преимущества новой архитектуры

1. **Стандартизация** - использование зрелого XMPP протокола
2. **Масштабируемость** - ejabberd оптимизирован для миллионов соединений
3. **Надежность** - разделение real-time и бизнес-логики
4. **Совместимость** - возможность использования XMPP клиентов
5. **Производительность** - нативная обработка сообщений в Erlang

## Миграция данных

Существующие сообщения в PostgreSQL совместимы с mod_mam ejabberd.
Пользователи должны перелогиниться для получения XMPP сессии.

## Тестирование

1. Запустите ejabberd: `docker-compose up ejabberd`
2. Запустите FastAPI: `cd backend && python app.py`
3. Запустите фронтенд: `cd frontend_react && npm run dev`
4. Зарегистрируйтесь/войдите в систему
5. Проверьте XMPP соединение в компоненте XMPPTest

## 🎯 Результат

Теперь у вас **чистая XMPP архитектура**:
- Real-time функции через стандартный XMPP протокол
- Бизнес-логика через REST API
- Совместимость с любыми XMPP клиентами
- Масштабируемость благодаря ejabberd (Erlang/OTP)

## 🧪 Тестирование

1. **Запустите систему**: `docker-compose up -d`
2. **Запустите фронтенд**: `cd frontend_react && npm run dev`
3. **Откройте**: http://localhost:5173
4. **Войдите** и проверьте XMPP статус в компоненте "XMPP Тест"

Подробные инструкции: [TEST_XMPP.md](../TEST_XMPP.md)

Система готова к тестированию! 🎉