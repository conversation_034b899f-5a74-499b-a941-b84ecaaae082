# 📞 План развития VoIP-функциональности

## 🏗️ Инфраструктура

- 🔄 **CoTURN:** Дополнение к LiveKit для поддержки NAT

## 📈 Дорожная карта улучшения звонков

### 1️⃣ Этап 1: Инфраструктура
- 📡 Сигнальный сервер (наш бэкенд)
- 🌐 Серверы CoTURN (STUN/TURN)
- 🖥️ Медиа-сервер SFU (LiveKit)

### 2️⃣ Этап 2: Базовый звонок 1-на-1
- 🔌 Реализация логики P2P-звонка с WebRTC SDK
- 🔗 Интеграция с сигнальным сервером
- 🌊 Поддержка STUN/TURN для преодоления NAT

### 3️⃣ Этап 3: Групповые звонки
- 👥 Логика для работы с SFU-сервером
- 🎙️ Организация конференций

### 4️⃣ Этап 4: Оптимизация качества
- 🎛️ Тонкая настройка аудиопроцессинга:
  - 🔊 Эхоподавление (AEC)
  - 🔇 Шумоподавление (NS)
  - 🔉 Автоматическая регулировка усиления (AGC)
- 📊 Мониторинг сети и адаптация битрейта

### 5️⃣ Этап 5: Тестирование
- 📱 Тестирование в нестабильных сетях 3G/LTE
- 📶 Проверка на Wi-Fi с потерями пакетов
- 🌐 Тестирование с различными типами NAT