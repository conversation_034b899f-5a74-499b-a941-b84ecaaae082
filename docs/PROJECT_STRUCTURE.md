# 🏗️ Структура проекта

Описание архитектуры и организации кода FastAPI бекенда мессенджера.

## 📁 Общая структура

```
backend/
├── 📁 api/                    # REST API роутеры
│   ├── __init__.py           # Главный роутер и подключение всех роутеров
│   ├── auth.py               # Аутентификация (регистрация, логин)
│   ├── users.py              # Управление пользователями
│   ├── messages.py           # Отправка и получение сообщений
│   ├── groups.py             # Групповые чаты
│   ├── calls.py              # Аудио/видео звонки
│   ├── files.py              # Загрузка и получение файлов
│   ├── notifications.py      # Push уведомления
│   ├── message_reactions.py  # Реакции на сообщения
│   ├── message_status.py     # Статусы сообщений (прочитано/доставлено)
│   └── channels.py           # Каналы (broadcast)
├── 📁 core/                   # Основные компоненты
│   ├── config.py             # Конфигурация приложения
│   └── security.py           # JWT токены и безопасность
├── 📁 database/               # База данных
│   └── connection.py         # Подключение к PostgreSQL
├── 📁 models/                 # SQLAlchemy модели
│   ├── user.py               # Модель пользователя
│   ├── group.py              # Модели групп и участников
│   ├── call.py               # Модель звонков
│   ├── file.py               # Модель файлов
│   ├── notification.py       # Модель уведомлений
│   ├── message_reaction.py   # Модель реакций
│   ├── message_status.py     # Модель статусов сообщений
│   ├── message_thread.py     # Модель тредов сообщений
│   └── channel.py            # Модель каналов
├── 📁 schemas/                # Pydantic схемы для валидации
│   ├── user.py               # Схемы пользователей
│   ├── group.py              # Схемы групп
│   ├── call.py               # Схемы звонков
│   ├── file.py               # Схемы файлов
│   ├── message.py            # Схемы сообщений
│   ├── message_advanced.py   # Расширенные схемы сообщений
│   ├── message_status.py     # Схемы статусов сообщений
│   ├── notification.py       # Схемы уведомлений
│   └── channel.py            # Схемы каналов
├── 📁 services/               # Бизнес-логика и внешние сервисы
│   ├── ejabberd.py           # Интеграция с eJabberd XMPP
│   ├── ejabberd_db.py        # Прямая работа с БД eJabberd
│   ├── livekit.py            # Интеграция с LiveKit (звонки)
│   ├── s3.py                 # Работа с MinIO/S3 хранилищем
│   ├── redis.py              # Кэширование и сессии
│   └── push_notifications.py # Push уведомления
├── 📁 alembic/                # Миграции базы данных
│   ├── versions/             # Файлы миграций
│   ├── env.py                # Конфигурация Alembic
│   └── alembic.ini           # Настройки Alembic
├── 📁 docs/                   # Документация
│   ├── api_reference.md      # Справочник по API
│   ├── project_structure.md  # Этот файл
│   └── database_schema.md    # Схема базы данных
├── app.py                    # Главный файл приложения FastAPI
├── pyproject.toml            # Зависимости и настройки проекта
└── Dockerfile                # Docker образ для бекенда
```

## 🎯 Архитектурные принципы

### 1. **Слоистая архитектура**
- **API Layer** (`api/`) - HTTP эндпойнты и валидация запросов
- **Service Layer** (`services/`) - бизнес-логика и интеграции
- **Data Layer** (`models/`, `database/`) - работа с данными
- **Schema Layer** (`schemas/`) - валидация и сериализация

### 2. **Разделение ответственности**
- Каждый роутер отвечает за свою предметную область
- Сервисы инкапсулируют логику работы с внешними системами
- Модели описывают только структуру данных

### 3. **Dependency Injection**
- Использование FastAPI Depends для внедрения зависимостей
- Централизованное управление подключениями к БД и сервисам

## 📋 Описание компонентов

### 🌐 API Роутеры (`api/`)

#### `auth.py` - Аутентификация
- Регистрация пользователей в eJabberd
- Авторизация через JWT токены
- Интеграция с Redis для сессий

#### `users.py` - Пользователи
- Профили пользователей
- Поиск по имени/отображаемому имени
- Статусы онлайн/офлайн
- Обновление профиля

#### `messages.py` - Сообщения
- Отправка через eJabberd XMPP
- История сообщений (MAM)
- Ответы, пересылка, редактирование
- Индикаторы печати

#### `groups.py` - Групповые чаты
- Создание MUC комнат в eJabberd
- Управление участниками
- Роли (admin, moderator, member)
- Приглашения и исключения

#### `calls.py` - Звонки
- Создание комнат в LiveKit
- Генерация токенов доступа
- Отслеживание статуса звонков
- Webhook обработка событий

#### `files.py` - Файлы
- Загрузка в MinIO S3
- Генерация presigned URLs
- Метаданные файлов в PostgreSQL

#### `notifications.py` - Уведомления
- Push уведомления
- Отметка как прочитанные
- Счетчик непрочитанных

#### `message_reactions.py` - Реакции
- Эмодзи реакции на сообщения
- Группировка по типу реакции
- История реакций пользователя

#### `message_status.py` - Статусы сообщений
- Доставлено/прочитано
- Пакетное обновление статусов
- Статистика по беседам

#### `channels.py` - Каналы
- Broadcast каналы
- Подписки/отписки
- Публикация контента
- Модерация

### 🔧 Сервисы (`services/`)

#### `ejabberd.py` - XMPP интеграция
- REST API вызовы к eJabberd
- Регистрация пользователей
- Отправка сообщений
- Создание MUC комнат
- Получение истории (MAM)

#### `livekit.py` - Видео звонки
- Создание комнат LiveKit
- Генерация JWT токенов
- Управление участниками
- Webhook обработка

#### `s3.py` - Файловое хранилище
- Загрузка в MinIO
- Presigned URLs для скачивания
- Управление bucket'ами

#### `redis.py` - Кэширование
- Статусы пользователей онлайн
- Сессии и временные данные
- Кэширование частых запросов

#### `push_notifications.py` - Уведомления
- Отправка push уведомлений
- Шаблоны уведомлений
- Счетчики непрочитанных

### 🗃️ Модели данных (`models/`)

#### `user.py` - Пользователи
- Расширенная информация к eJabberd users
- Профиль, контакты, статус
- Время последней активности

#### `group.py` - Группы
- Метаданные MUC комнат
- Участники и их роли
- Настройки группы

#### `call.py` - Звонки
- Связь с LiveKit комнатами
- Участники и статус
- Длительность звонков

#### `file.py` - Файлы
- Метаданные загруженных файлов
- Связь с S3 ключами
- Информация о загрузившем

#### `notification.py` - Уведомления
- Push уведомления пользователей
- Типы и статусы
- Дополнительные данные

#### Дополнительные модели:
- `message_reaction.py` - реакции на сообщения
- `message_status.py` - статусы доставки/прочтения
- `message_thread.py` - треды сообщений
- `channel.py` - каналы и подписки

### 📝 Схемы валидации (`schemas/`)

Pydantic модели для:
- Валидации входящих данных
- Сериализации ответов API
- Документации OpenAPI
- Типизации в IDE

### ⚙️ Конфигурация (`core/`)

#### `config.py` - Настройки
- Переменные окружения
- Подключения к сервисам
- Параметры безопасности

#### `security.py` - Безопасность
- JWT токены
- Хэширование паролей
- Middleware аутентификации

## 🔄 Потоки данных

### Отправка сообщения:
1. **API** получает запрос → валидация схемой
2. **Service** отправляет через eJabberd XMPP
3. **Notification** отправляет push уведомление
4. **Redis** обновляет статистику

### Создание группы:
1. **API** создает запись в PostgreSQL
2. **Service** создает MUC комнату в eJabberd
3. **API** добавляет создателя как админа
4. **Service** отправляет уведомления участникам

### Загрузка файла:
1. **API** получает файл → валидация
2. **Service** загружает в MinIO S3
3. **Model** сохраняет метаданные в PostgreSQL
4. **API** возвращает URL для доступа

## 🔌 Интеграции

### eJabberd XMPP сервер:
- Регистрация пользователей
- Отправка сообщений
- Групповые чаты (MUC)
- История сообщений (MAM)
- Статусы присутствия

### LiveKit медиа сервер:
- Создание комнат для звонков
- JWT токены доступа
- Webhook события
- Управление участниками

### MinIO S3 хранилище:
- Загрузка файлов
- Presigned URLs
- Bucket management

### PostgreSQL база данных:
- Метаданные пользователей
- Группы и участники
- Файлы и звонки
- Уведомления и реакции

### Redis кэш:
- Статусы онлайн
- Сессии пользователей
- Временные данные

## 🚀 Масштабирование

### Горизонтальное:
- Stateless FastAPI приложение
- Балансировка нагрузки
- Кластер PostgreSQL
- Redis Cluster

### Вертикальное:
- Оптимизация запросов к БД
- Кэширование частых операций
- Асинхронная обработка
- Connection pooling

## 🔒 Безопасность

### Аутентификация:
- JWT токены с истечением
- Refresh токены
- Rate limiting

### Авторизация:
- Проверка прав доступа
- Роли в группах
- Валидация JID

### Данные:
- Валидация входящих данных
- SQL injection защита
- XSS защита
- CORS настройки

## 🧪 Тестирование

### Структура тестов:
- Unit тесты для сервисов
- Integration тесты для API
- E2E тесты для сценариев
- Load тесты для производительности

### Инструменты:
- pytest для тестирования
- httpx для HTTP клиента
- factory_boy для тестовых данных
- pytest-asyncio для async тестов
