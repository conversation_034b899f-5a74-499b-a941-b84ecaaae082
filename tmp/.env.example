# =======================================
# Переменные для всего проекта
# =======================================
COMPOSE_PROJECT_NAME=messenger

# =======================================
# PostgreSQL
# =======================================
POSTGRES_DB=messenger
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres123
POSTGRES_PORT=5432

# =======================================
# Redis
# =======================================
REDIS_PORT=6379

# =======================================
# MinIO S3 Storage
# =======================================
MINIO_ROOT_USER=minioadmin
MINIO_ROOT_PASSWORD=minioadmin123
MINIO_API_PORT=9000
MINIO_CONSOLE_PORT=9001
# Имя сервиса для внутреннего общения в Docker сети
MINIO_HOST=minio

# =======================================
# eJabberd XMPP Server
# =======================================
EJABBERD_DOMAIN=localhost
EJABBERD_ADMIN_USER=admin
EJABBERD_ADMIN_PASSWORD=admin123
# Имя сервиса для внутреннего общения в Docker сети
EJABBERD_HOST=ejabberd

# Внешние порты
EJABBERD_C2S_PORT=5222
EJABBERD_S2S_PORT=5269
EJABBERD_API_PORT=5280
EJABBERD_HTTPS_PORT=5443

# =======================================
# LiveKit WebRTC Server
# =======================================
LIVEKIT_API_KEY=APIKey
LIVEKIT_API_SECRET=secret123
# Имя сервиса для внутреннего общения в Docker сети
LIVEKIT_HOST=livekit

# Внешние порты
LIVEKIT_RTC_PORT=7880
LIVEKIT_TURN_PORT=7881

# =======================================
# FastAPI Backend
# =======================================
FASTAPI_PORT=8000