services:
  # База данных PostgreSQL
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "${POSTGRES_PORT}:5432"
    networks:
      - messenger_network

  # Redis для кэширования и сессий
  redis:
    image: redis:7-alpine
    ports:
      - "${REDIS_PORT}:6379"
    networks:
      - messenger_network

  # MinIO для S3-совместимого хранилища файлов
  minio:
    image: minio/minio:latest
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD}
    command: server /data --console-address ":${MINIO_CONSOLE_PORT}"
    ports:
      - "${MINIO_API_PORT}:9000"
      - "${MINIO_CONSOLE_PORT}:9001"
    volumes:
      - minio_data:/data
    networks:
      - messenger_network

  # eJabberd XMPP сервер
  ejabberd:
    image: ghcr.io/processone/ejabberd:latest
    environment:
      EJABBERD_DOMAIN: ${EJABBERD_DOMAIN}
      EJABBERD_ADMIN_USER: ${EJABBERD_ADMIN_USER}
      EJABBERD_ADMIN_PASSWORD: ${EJABBERD_ADMIN_PASSWORD}
    ports:
      - "${EJABBERD_C2S_PORT}:5222"      # XMPP C2S
      - "${EJABBERD_S2S_PORT}:5269"      # XMPP S2S
      - "${EJABBERD_API_PORT}:5280"      # HTTP API
      - "${EJABBERD_HTTPS_PORT}:5443"    # HTTPS
    volumes:
      - ./ejabberd/ejabberd.yml:/home/<USER>/conf/ejabberd.yml
      - ./ejabberd/database.yml:/home/<USER>/conf/database.yml
      - ejabberd_data:/home/<USER>/database
      - ejabberd_logs:/home/<USER>/logs
    depends_on:
      - postgres
    networks:
      - messenger_network

  # LiveKit сервер для видео/аудио звонков
  livekit:
    image: livekit/livekit-server:latest
    command: --config /etc/livekit.yaml
    ports:
      - "${LIVEKIT_RTC_PORT}:7880"        # RTC
      - "${LIVEKIT_TURN_PORT}:7881"        # TURN
    volumes:
      - ./livekit/livekit.yaml:/etc/livekit.yaml
    environment:
      LIVEKIT_KEYS: "${LIVEKIT_API_KEY}: ${LIVEKIT_API_SECRET}"
    networks:
      - messenger_network

  # FastAPI бекенд
  fastapi_backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    environment:
      DATABASE_URL: postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB}
      REDIS_URL: redis://redis:6379
      MINIO_ENDPOINT: ${MINIO_HOST}:${MINIO_API_PORT}
      MINIO_ACCESS_KEY: ${MINIO_ROOT_USER}
      MINIO_SECRET_KEY: ${MINIO_ROOT_PASSWORD}
      EJABBERD_API_URL: http://${EJABBERD_HOST}:${EJABBERD_API_PORT}/api
      EJABBERD_ADMIN_USER: ${EJABBERD_ADMIN_USER}
      EJABBERD_ADMIN_PASSWORD: ${EJABBERD_ADMIN_PASSWORD}
      LIVEKIT_API_KEY: ${LIVEKIT_API_KEY}
      LIVEKIT_API_SECRET: ${LIVEKIT_API_SECRET}
      LIVEKIT_URL: ws://${LIVEKIT_HOST}:${LIVEKIT_RTC_PORT}
    ports:
      - "${FASTAPI_PORT}:8000"
    depends_on:
      - postgres
      - redis
      - minio
      - ejabberd
      - livekit
    volumes:
      - ./backend:/app
    networks:
      - messenger_network

volumes:
  postgres_data:
  minio_data:
  ejabberd_data:
  ejabberd_logs:

networks:
  messenger_network:
    driver: bridge