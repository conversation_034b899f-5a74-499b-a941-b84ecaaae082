# 🚀 Messenger Backend

Лаконичный, но мощный FastAPI бекенд для мессенджера на базе eJabberd XMPP сервера.

## 🎯 Что это?

Это не просто очередной REST API. Это **умный прокси** между вашими клиентами и проверенными временем технологиями:

- **eJabberd** 🔥 - для XMPP сообщений и групповых чатов
- **LiveKit** 📹 - для кристально чистых видео звонков  
- **PostgreSQL** 🐘 - для надежного хранения метаданных
- **MinIO S3** 📦 - для быстрой загрузки файлов
- **Redis** ⚡ - для молниеносного кэширования

## ✨ Фишки

### 🔐 Аутентификация
- JWT токены с автоматическим обновлением
- Интеграция с eJabberd для проверки паролей
- Статусы онлайн/офлайн в реальном времени

### 💬 Сообщения
- Отправка через нативный XMPP
- История сообщений через MAM (Message Archive Management)
- Реакции эмодзи на сообщения
- Статусы доставки и прочтения
- Ответы и пересылка сообщений

### 👥 Групповые чаты
- MUC (Multi-User Chat) комнаты в eJabberd
- Роли участников (admin, moderator, member)
- Приглашения и исключения
- Публичные и приватные группы

### 📞 Звонки
- Аудио/видео через LiveKit
- Автоматическая генерация токенов доступа
- Webhook события для отслеживания статуса
- Групповые звонки до 100 участников

### 📁 Файлы
- Загрузка в S3-совместимое хранилище
- Presigned URLs для безопасного доступа
- Поддержка любых типов файлов
- Автоматическое определение MIME типов

### 🔔 Уведомления
- Push уведомления (готово к интеграции)
- Счетчики непрочитанных сообщений
- Гибкая система шаблонов

### 📺 Каналы
- Broadcast каналы для массовых рассылок
- Подписки и отписки
- Модерация контента

## 🏗️ Архитектура

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Mobile App    │    │    Web App      │    │  Desktop App    │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │     FastAPI Backend      │
                    │   (Этот репозиторий)     │
                    └─────────────┬─────────────┘
                                  │
        ┌─────────────────────────┼─────────────────────────┐
        │                         │                         │
┌───────▼────────┐    ┌───────────▼──────────┐    ┌─────────▼────────┐
│   eJabberd     │    │      LiveKit         │    │     MinIO S3     │
│   (XMPP)       │    │   (Video Calls)      │    │   (File Storage) │
└────────────────┘    └──────────────────────┘    └──────────────────┘
        │                         │                         │
        └─────────────────────────┼─────────────────────────┘
                                  │
                    ┌─────────────▼─────────────┐
                    │      PostgreSQL          │
                    │    (Metadata DB)         │
                    └──────────────────────────┘
```

## 🚀 Быстрый старт

### Предварительные требования
- Python 3.12+ с uv
- Docker и Docker Compose (для инфраструктуры)

### 1. Запуск инфраструктуры
```bash
# Из корня проекта
docker compose up -d postgres redis minio ejabberd livekit
```

### 2. Установка зависимостей
```bash
cd backend
uv sync
```

### 3. Применение миграций
```bash
uv run alembic upgrade head
```

### 4. Запуск бекенда
```bash
# Для разработки
uv run uvicorn app:app --reload --host 0.0.0.0 --port 8000

# Или через Docker
docker compose up -d fastapi_backend
```

### 5. Проверка работоспособности
- 📖 Swagger UI: http://localhost:8000/docs
- 🔍 ReDoc: http://localhost:8000/redoc
- ❤️ Health check: http://localhost:8000/api/health

## 🧪 Тестирование

### Запуск тестов
```bash
# Все тесты
uv run python test_api.py

# Или через pytest
uv run pytest

# Конкретный тест
uv run python test_modern_features.py
```

### Тестирование API через curl
```bash
# Регистрация
curl -X POST http://localhost:8000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username": "test", "password": "test123", "display_name": "Test User"}'

# Авторизация
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "test", "password": "test123"}'

# Отправка сообщения (с токеном)
curl -X POST http://localhost:8000/api/messages/send \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"to_jid": "admin@localhost", "body": "Hello from API!"}'
```

## 📚 Документация

- 📋 [API Reference](docs/api_reference.md) - Полное описание всех эндпойнтов
- 🏗️ [Project Structure](docs/project_structure.md) - Архитектура и организация кода
- 🗄️ [Database Schema](docs/database_schema.md) - Схема БД и миграции

## ⚙️ Конфигурация

### Переменные окружения

```bash
# База данных
DATABASE_URL=postgresql://postgres:postgres123@localhost:5432/messenger

# Redis
REDIS_URL=redis://localhost:6379

# S3 хранилище
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin123

# eJabberd
EJABBERD_API_URL=http://localhost:5280/api
EJABBERD_ADMIN_USER=admin
EJABBERD_ADMIN_PASSWORD=admin123
EJABBERD_DOMAIN=localhost

# LiveKit
LIVEKIT_API_KEY=APIKey
LIVEKIT_API_SECRET=secret123
LIVEKIT_URL=ws://localhost:7880

# JWT
SECRET_KEY=your-super-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=1440
```

### Настройка в production

1. **Смените все пароли по умолчанию**
2. **Используйте HTTPS везде**
3. **Настройте firewall**
4. **Включите SSL для PostgreSQL**
5. **Настройте мониторинг и логирование**
6. **Регулярные бэкапы БД**

## 🔧 Разработка

### Структура проекта
```
backend/
├── api/           # REST API роутеры
├── core/          # Конфигурация и безопасность  
├── database/      # Подключение к БД
├── models/        # SQLAlchemy модели
├── schemas/       # Pydantic схемы
├── services/      # Бизнес-логика и интеграции
├── alembic/       # Миграции БД
└── docs/          # Документация
```

### Добавление нового эндпойнта

1. **Создайте схему** в `schemas/`
2. **Добавьте модель** в `models/` (если нужно)
3. **Реализуйте роутер** в `api/`
4. **Подключите роутер** в `api/__init__.py`
5. **Добавьте тесты**

### Создание миграции
```bash
# Автогенерация миграции
uv run alembic revision --autogenerate -m "Add new feature"

# Применение
uv run alembic upgrade head
```

## 🐛 Отладка

### Логи
```bash
# Все сервисы
docker compose logs -f

# Только бекенд
docker compose logs -f fastapi_backend

# eJabberd
docker compose logs -f ejabberd
```

### Подключение к БД
```bash
# PostgreSQL
docker compose exec postgres psql -U postgres -d messenger

# Redis
docker compose exec redis redis-cli
```

### Полезные команды
```bash
# Статус всех сервисов
docker compose ps

# Перезапуск бекенда
docker compose restart fastapi_backend

# Просмотр переменных окружения
docker compose exec fastapi_backend env
```

## 🤝 Вклад в проект

1. Fork репозитория
2. Создайте feature branch (`git checkout -b feature/amazing-feature`)
3. Commit изменения (`git commit -m 'Add amazing feature'`)
4. Push в branch (`git push origin feature/amazing-feature`)
5. Создайте Pull Request

## 📄 Лицензия

MIT License - используйте как хотите, но не забудьте поставить звездочку ⭐

## 🆘 Поддержка

- 🐛 **Баги**: Создайте issue в GitHub
- 💡 **Идеи**: Discussions в GitHub  
- 📧 **Email**: <EMAIL>
- 💬 **Telegram**: @messenger_support

---

**Сделано с ❤️ для разработчиков, которые ценят простоту и надежность**
