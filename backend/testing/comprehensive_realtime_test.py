#!/usr/bin/env python3
"""
🧪 Comprehensive Real-time Test - Комплексное тестирование реалтайм функций

Эмулирует поведение фронтенда для полного тестирования:
- Множественные пользователи
- Одновременные соединения
- Отправка/получение сообщений
- Индикаторы печати
- Статусы пользователей
- Групповые чаты
- Обработка ошибок
"""

import asyncio
import json
import websockets
import aiohttp
import logging
import time
from typing import List, Dict, Optional
from dataclasses import dataclass

# Настройка логирования
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@dataclass
class TestUser:
    """Тестовый пользователь"""

    username: str
    password: str
    display_name: str
    token: Optional[str] = None
    user_jid: Optional[str] = None
    websocket: Optional[websockets.WebSocketServerProtocol] = None
    messages_received: int = 0
    typing_notifications: int = 0
    status_updates: int = 0
    errors: List[str] = None

    def __post_init__(self):
        if self.errors is None:
            self.errors = []


class ComprehensiveRealtimeTest:
    """Комплексный тест реалтайм функций"""

    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.ws_url = base_url.replace("http://", "ws://").replace("https://", "wss://")
        self.users: List[TestUser] = []
        self.test_results: Dict[str, any] = {}

    async def create_test_users(self, count: int = 5) -> List[TestUser]:
        """Создание тестовых пользователей"""
        users = []
        for i in range(count):
            user = TestUser(
                username=f"test_user_{i + 1}",
                password=f"password{i + 1}",
                display_name=f"User {i + 1}",
            )
            users.append(user)

        self.users = users
        logger.info(f"✅ Created {count} test users")
        return users

    async def authenticate_users(self) -> bool:
        """Аутентификация всех пользователей"""
        logger.info("🔐 Authenticating users...")

        success_count = 0
        for user in self.users:
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        f"{self.base_url}/api/auth/login",
                        json={"username": user.username, "password": user.password},
                    ) as response:
                        if response.status == 200:
                            data = await response.json()
                            user.token = data.get("access_token")
                            user.user_jid = data.get("user_jid")
                            success_count += 1
                            logger.info(
                                f"✅ {user.display_name} authenticated as {user.user_jid}"
                            )
                        else:
                            error_msg = f"Authentication failed for {user.display_name}"
                            user.errors.append(error_msg)
                            logger.error(f"❌ {error_msg}")
            except Exception as e:
                error_msg = f"Authentication error for {user.display_name}: {e}"
                user.errors.append(error_msg)
                logger.error(f"❌ {error_msg}")

        logger.info(
            f"🔐 Authentication completed: {success_count}/{len(self.users)} successful"
        )
        return success_count == len(self.users)

    async def connect_websockets(self) -> bool:
        """Подключение всех пользователей к WebSocket"""
        logger.info("🔌 Connecting to WebSocket...")

        success_count = 0
        for user in self.users:
            if not user.token:
                continue

            try:
                ws_endpoint = f"{self.ws_url}/api/websocket/ws?token={user.token}"
                user.websocket = await websockets.connect(ws_endpoint)
                success_count += 1
                logger.info(f"✅ {user.display_name} connected to WebSocket")
            except Exception as e:
                error_msg = f"WebSocket connection failed for {user.display_name}: {e}"
                user.errors.append(error_msg)
                logger.error(f"❌ {error_msg}")

        logger.info(
            f"🔌 WebSocket connections: {success_count}/{len(self.users)} successful"
        )
        return success_count > 0

    async def start_message_listeners(self) -> List[asyncio.Task]:
        """Запуск прослушивания сообщений для всех пользователей"""
        logger.info("👂 Starting message listeners...")

        tasks = []
        for user in self.users:
            if user.websocket:
                task = asyncio.create_task(self.listen_user_messages(user))
                tasks.append(task)

        logger.info(f"👂 Started {len(tasks)} message listeners")
        return tasks

    async def listen_user_messages(self, user: TestUser):
        """Прослушивание сообщений для конкретного пользователя"""
        try:
            async for message in user.websocket:
                try:
                    data = json.loads(message)
                    await self.handle_user_message(user, data)
                except json.JSONDecodeError:
                    error_msg = f"Invalid JSON received by {user.display_name}"
                    user.errors.append(error_msg)
                    logger.error(f"❌ {error_msg}")
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"🔌 {user.display_name} WebSocket connection closed")
        except Exception as e:
            error_msg = f"WebSocket error for {user.display_name}: {e}"
            user.errors.append(error_msg)
            logger.error(f"❌ {error_msg}")

    async def handle_user_message(self, user: TestUser, data: dict):
        """Обработка сообщения для пользователя"""
        message_type = data.get("type")

        if message_type == "new_message":
            user.messages_received += 1
            from_jid = data.get("from_jid")
            body = data.get("body")
            timestamp = data.get("timestamp")
            logger.info(
                f"💬 {user.display_name} received message from {from_jid}: '{body[:50]}...'"
            )

        elif message_type == "typing":
            user.typing_notifications += 1
            from_jid = data.get("from_jid")
            is_typing = data.get("is_typing")
            action = "started" if is_typing else "stopped"
            logger.info(f"⌨️ {user.display_name} notified: {from_jid} {action} typing")

        elif message_type == "user_status":
            user.status_updates += 1
            user_jid = data.get("user_jid")
            status = data.get("status")
            logger.info(f"👤 {user.display_name} notified: {user_jid} is {status}")

        elif message_type == "pong":
            logger.info(f"🏓 {user.display_name} received pong")

        elif message_type == "online_users":
            users_list = data.get("users", [])
            logger.info(f"👥 {user.display_name} sees {len(users_list)} online users")

        elif message_type == "error":
            error_msg = data.get("message", "Unknown error")
            user.errors.append(f"Server error: {error_msg}")
            logger.error(f"❌ {user.display_name} received error: {error_msg}")

    async def send_websocket_message(self, user: TestUser, message: dict):
        """Отправка сообщения через WebSocket"""
        if not user.websocket:
            return False

        try:
            await user.websocket.send(json.dumps(message))
            return True
        except Exception as e:
            error_msg = f"Failed to send WebSocket message for {user.display_name}: {e}"
            user.errors.append(error_msg)
            logger.error(f"❌ {error_msg}")
            return False

    async def send_api_message(
        self, sender: TestUser, recipient_jid: str, body: str
    ) -> bool:
        """Отправка сообщения через REST API"""
        if not sender.token:
            return False

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/api/messages/send",
                    json={
                        "to_jid": recipient_jid,
                        "body": body,
                        "message_type": "chat",
                    },
                    headers={"Authorization": f"Bearer {sender.token}"},
                ) as response:
                    if response.status == 200:
                        logger.info(
                            f"📤 {sender.display_name} sent API message to {recipient_jid}"
                        )
                        return True
                    else:
                        error_msg = f"API message failed for {sender.display_name}"
                        sender.errors.append(error_msg)
                        logger.error(f"❌ {error_msg}")
                        return False
        except Exception as e:
            error_msg = f"API message error for {sender.display_name}: {e}"
            sender.errors.append(error_msg)
            logger.error(f"❌ {error_msg}")
            return False

    async def test_ping_pong(self):
        """Тест ping/pong для всех пользователей"""
        logger.info("\n🧪 Testing ping/pong...")

        for user in self.users:
            if user.websocket:
                await self.send_websocket_message(user, {"type": "ping"})
                await asyncio.sleep(0.1)

        await asyncio.sleep(1)
        logger.info("✅ Ping/pong test completed")

    async def test_online_users(self):
        """Тест запроса онлайн пользователей"""
        logger.info("\n🧪 Testing online users request...")

        if self.users:
            user = self.users[0]
            if user.websocket:
                await self.send_websocket_message(user, {"type": "get_online_users"})
                await asyncio.sleep(1)

        logger.info("✅ Online users test completed")

    async def test_typing_indicators(self):
        """Тест индикаторов печати"""
        logger.info("\n🧪 Testing typing indicators...")

        if len(self.users) >= 2:
            sender = self.users[0]
            recipient = self.users[1]

            # Начать печатать
            await self.send_websocket_message(
                sender,
                {"type": "typing", "to_jid": recipient.user_jid, "is_typing": True},
            )
            await asyncio.sleep(1)

            # Прекратить печатать
            await self.send_websocket_message(
                sender,
                {"type": "typing", "to_jid": recipient.user_jid, "is_typing": False},
            )
            await asyncio.sleep(1)

        logger.info("✅ Typing indicators test completed")

    async def test_message_delivery(self):
        """Тест доставки сообщений"""
        logger.info("\n🧪 Testing message delivery...")

        messages_sent = 0
        for i, sender in enumerate(self.users):
            for j, recipient in enumerate(self.users):
                if i != j and sender.token and recipient.user_jid:
                    message = f"Test message from {sender.display_name} to {recipient.display_name} #{messages_sent + 1}"
                    success = await self.send_api_message(
                        sender, recipient.user_jid, message
                    )
                    if success:
                        messages_sent += 1
                    await asyncio.sleep(0.5)

        await asyncio.sleep(2)  # Время на доставку всех сообщений
        logger.info(
            f"✅ Message delivery test completed: {messages_sent} messages sent"
        )

    async def test_room_functionality(self):
        """Тест функций комнат"""
        logger.info("\n🧪 Testing room functionality...")

        test_room = "comprehensive-test-room"

        # Присоединение к комнате
        for user in self.users:
            if user.websocket:
                await self.send_websocket_message(
                    user, {"type": "join_room", "room_id": test_room}
                )
                await asyncio.sleep(0.2)

        await asyncio.sleep(1)

        # Выход из комнаты
        for user in self.users:
            if user.websocket:
                await self.send_websocket_message(
                    user, {"type": "leave_room", "room_id": test_room}
                )
                await asyncio.sleep(0.2)

        await asyncio.sleep(1)
        logger.info("✅ Room functionality test completed")

    async def run_comprehensive_test(self):
        """Запуск комплексного теста"""
        logger.info("🧪 Starting Comprehensive Real-time Test")
        logger.info("=" * 60)

        start_time = time.time()

        # Создание пользователей
        await self.create_test_users(5)

        # Аутентификация
        if not await self.authenticate_users():
            logger.error("❌ Authentication failed, aborting test")
            return

        # Подключение WebSocket
        if not await self.connect_websockets():
            logger.error("❌ WebSocket connections failed, aborting test")
            return

        # Запуск прослушивания
        listen_tasks = await self.start_message_listeners()

        # Даем время на подключение
        await asyncio.sleep(2)

        try:
            # Выполнение тестов
            await self.test_ping_pong()
            await self.test_online_users()
            await self.test_typing_indicators()
            await self.test_message_delivery()
            await self.test_room_functionality()

            # Финальная пауза для обработки сообщений
            await asyncio.sleep(3)

        finally:
            # Закрытие соединений
            logger.info("\n🧹 Cleaning up...")
            for task in listen_tasks:
                task.cancel()

            for user in self.users:
                if user.websocket:
                    await user.websocket.close()

        # Результаты
        end_time = time.time()
        await self.print_test_results(end_time - start_time)

    async def print_test_results(self, duration: float):
        """Вывод результатов тестирования"""
        logger.info("\n📊 Test Results")
        logger.info("=" * 40)

        total_messages = sum(user.messages_received for user in self.users)
        total_typing = sum(user.typing_notifications for user in self.users)
        total_status = sum(user.status_updates for user in self.users)
        total_errors = sum(len(user.errors) for user in self.users)

        logger.info(f"⏱️  Test duration: {duration:.2f} seconds")
        logger.info(f"👥 Users tested: {len(self.users)}")
        logger.info(f"💬 Total messages received: {total_messages}")
        logger.info(f"⌨️  Total typing notifications: {total_typing}")
        logger.info(f"👤 Total status updates: {total_status}")
        logger.info(f"❌ Total errors: {total_errors}")

        logger.info("\n📋 Per-user statistics:")
        for user in self.users:
            logger.info(f"  👤 {user.display_name}:")
            logger.info(f"     📨 Messages: {user.messages_received}")
            logger.info(f"     ⌨️  Typing: {user.typing_notifications}")
            logger.info(f"     👤 Status: {user.status_updates}")
            logger.info(f"     ❌ Errors: {len(user.errors)}")

            if user.errors:
                logger.info("     🔍 Error details:")
                for error in user.errors:
                    logger.info(f"        - {error}")

        # Оценка результатов
        if total_errors == 0 and total_messages > 0:
            logger.info("\n🎉 COMPREHENSIVE TEST PASSED!")
            logger.info("✅ All real-time functions working correctly")
        elif total_errors == 0:
            logger.info("\n⚠️  TEST PARTIALLY PASSED")
            logger.info("✅ No errors, but limited message activity")
        else:
            logger.info("\n❌ TEST FAILED")
            logger.info(f"❌ {total_errors} errors detected")


if __name__ == "__main__":

    async def main():
        test = ComprehensiveRealtimeTest()
        await test.run_comprehensive_test()

    asyncio.run(main())
