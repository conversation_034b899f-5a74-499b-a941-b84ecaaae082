#!/usr/bin/env python3
"""
Тест современных функций мессенджера
"""

import requests


def test_modern_features():
    """Тестирование современных функций"""
    base_url = "http://localhost:8000"

    print("🚀 Тестирование современных функций мессенджера")

    try:
        # 1. Проверка здоровья API
        print("\n1. Проверка API...")
        resp = requests.get(f"{base_url}/api/health", timeout=5)
        if resp.status_code == 200:
            print("✅ API работает")
        else:
            print(f"❌ API недоступен: {resp.status_code}")
            return

        # 2. Авторизация
        print("\n2. Авторизация...")
        login_data = {"username": "testuser1", "password": "password123"}
        resp = requests.post(f"{base_url}/api/auth/login", json=login_data, timeout=5)

        if resp.status_code == 200:
            token = resp.json()["access_token"]
            headers = {"Authorization": f"Bearer {token}"}
            print("✅ Авторизация успешна")
        else:
            print(f"❌ Ошибка авторизации: {resp.status_code}")
            return

        # 3. Создание канала
        print("\n3. Создание канала...")
        channel_data = {
            "name": "Тестовый канал",
            "username": "test_channel",
            "description": "Канал для тестирования современных функций",
            "is_public": True,
        }
        resp = requests.post(
            f"{base_url}/api/channels/create",
            json=channel_data,
            headers=headers,
            timeout=5,
        )

        if resp.status_code == 200:
            channel_info = resp.json()
            channel_id = channel_info["id"]
            print(f"✅ Канал создан: {channel_info['name']} (ID: {channel_id})")
        else:
            print(f"❌ Ошибка создания канала: {resp.status_code}")
            channel_id = None

        # 4. Поиск каналов
        print("\n4. Поиск каналов...")
        resp = requests.get(
            f"{base_url}/api/channels/search?q=тест", headers=headers, timeout=5
        )

        if resp.status_code == 200:
            channels = resp.json()
            print(f"✅ Найдено каналов: {len(channels)}")
            for channel in channels[:3]:
                print(
                    f"  - {channel.get('name', 'N/A')} (@{channel.get('username', 'N/A')})"
                )
        else:
            print(f"❌ Ошибка поиска каналов: {resp.status_code}")

        # 5. Отправка сообщения для тестирования реакций
        print("\n5. Отправка сообщения...")
        message_data = {
            "to_jid": "testuser2@localhost",
            "body": "Сообщение для тестирования реакций",
            "message_type": "chat",
        }
        resp = requests.post(
            f"{base_url}/api/messages/send",
            json=message_data,
            headers=headers,
            timeout=5,
        )

        if resp.status_code == 200:
            print("✅ Сообщение отправлено")
            # Генерируем тестовый ID сообщения
            test_message_id = "test_message_123"
        else:
            print(f"❌ Ошибка отправки сообщения: {resp.status_code}")
            test_message_id = "test_message_123"  # Используем тестовый ID

        # 6. Добавление реакции
        print("\n6. Добавление реакции...")
        reaction_data = {"message_id": test_message_id, "emoji": "👍"}
        resp = requests.post(
            f"{base_url}/api/reactions/add",
            json=reaction_data,
            headers=headers,
            timeout=5,
        )

        if resp.status_code == 200:
            print("✅ Реакция добавлена")
        else:
            print(f"❌ Ошибка добавления реакции: {resp.status_code}")

        # 7. Получение реакций на сообщение
        print("\n7. Получение реакций...")
        resp = requests.get(
            f"{base_url}/api/reactions/message/{test_message_id}",
            headers=headers,
            timeout=5,
        )

        if resp.status_code == 200:
            reactions = resp.json()
            print(f"✅ Реакций на сообщение: {reactions.get('total_count', 0)}")
            for emoji, users in reactions.get("reactions", {}).items():
                print(f"  {emoji}: {len(users)} пользователей")
        else:
            print(f"❌ Ошибка получения реакций: {resp.status_code}")

        # 8. Индикатор печати
        print("\n8. Тестирование индикатора печати...")
        typing_data = {"to_jid": "testuser2@localhost", "is_typing": True}
        resp = requests.post(
            f"{base_url}/api/messages/typing",
            json=typing_data,
            headers=headers,
            timeout=5,
        )

        if resp.status_code == 200:
            print("✅ Индикатор печати установлен")
        else:
            print(f"❌ Ошибка установки индикатора печати: {resp.status_code}")

        # 9. Популярные реакции
        print("\n9. Популярные реакции...")
        resp = requests.get(
            f"{base_url}/api/reactions/popular", headers=headers, timeout=5
        )

        if resp.status_code == 200:
            popular = resp.json()
            print(f"✅ Популярных реакций: {len(popular)}")
            for reaction in popular[:5]:
                print(
                    f"  {reaction.get('emoji', 'N/A')}: {reaction.get('count', 0)} раз"
                )
        else:
            print(f"❌ Ошибка получения популярных реакций: {resp.status_code}")

        print("\n🎉 Тестирование современных функций завершено!")

    except requests.exceptions.RequestException as e:
        print(f"❌ Ошибка сети: {e}")
    except Exception as e:
        print(f"❌ Неожиданная ошибка: {e}")


if __name__ == "__main__":
    test_modern_features()
