#!/usr/bin/env python3
"""
Консольный мессенджер для тестирования API
"""

import asyncio
import aiohttp
import sys
from typing import Optional
from rich.console import Console
from rich.prompt import Prompt
from rich.table import Table
from rich.panel import Panel

console = Console()


class MessengerClient:
    """Клиент для работы с API мессенджера"""

    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.access_token: Optional[str] = None
        self.user_jid: Optional[str] = None

    async def register(self, username: str, password: str, display_name: str = None):
        """Регистрация пользователя"""
        data = {
            "username": username,
            "password": password,
            "display_name": display_name or username,
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/api/auth/register", json=data
            ) as resp:
                result = await resp.json()
                if resp.status == 200:
                    console.print("✅ Регистрация успешна!", style="green")
                    return True
                else:
                    console.print(
                        f"❌ Ошибка регистрации: {result.get('detail', 'Unknown error')}",
                        style="red",
                    )
                    return False

    async def login(self, username: str, password: str):
        """Авторизация пользователя"""
        data = {"username": username, "password": password}

        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/api/auth/login", json=data
            ) as resp:
                result = await resp.json()
                if resp.status == 200:
                    self.access_token = result["access_token"]
                    self.user_jid = result["user_jid"]
                    console.print(
                        f"✅ Авторизация успешна! JID: {self.user_jid}", style="green"
                    )
                    return True
                else:
                    console.print(
                        f"❌ Ошибка авторизации: {result.get('detail', 'Unknown error')}",
                        style="red",
                    )
                    return False

    def _get_headers(self):
        """Получение заголовков с токеном"""
        if not self.access_token:
            raise Exception("Не авторизован")
        return {"Authorization": f"Bearer {self.access_token}"}

    async def send_message(
        self, to_jid: str, body: str, message_type: str = "chat", file_id: str = None
    ):
        """Отправка сообщения"""
        data = {"to_jid": to_jid, "body": body, "message_type": message_type}

        if file_id:
            data["file_id"] = file_id

        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/api/messages/send",
                json=data,
                headers=self._get_headers(),
            ) as resp:
                result = await resp.json()
                if resp.status == 200:
                    console.print("✅ Сообщение отправлено!", style="green")
                    return True
                else:
                    console.print(
                        f"❌ Ошибка отправки: {result.get('detail', 'Unknown error')}",
                        style="red",
                    )
                    return False

    async def get_message_history(self, jid: str, limit: int = 10):
        """Получение истории сообщений"""
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{self.base_url}/api/messages/history/{jid}?limit={limit}",
                headers=self._get_headers(),
            ) as resp:
                result = await resp.json()
                if resp.status == 200:
                    messages = result.get("messages", [])
                    if messages:
                        table = Table(title=f"История сообщений с {jid}")
                        table.add_column("Время", style="cyan")
                        table.add_column("От", style="magenta")
                        table.add_column("Сообщение", style="white")

                        for msg in messages[-10:]:  # Показываем последние 10
                            table.add_row(
                                msg.get("timestamp", "N/A"),
                                msg.get("from", "N/A"),
                                msg.get("body", "N/A"),
                            )

                        console.print(table)
                    else:
                        console.print("📭 История сообщений пуста", style="yellow")
                    return True
                else:
                    console.print(
                        f"❌ Ошибка получения истории: {result.get('detail', 'Unknown error')}",
                        style="red",
                    )
                    return False

    async def create_group(self, name: str, description: str = None):
        """Создание группового чата"""
        data = {
            "name": name,
            "description": description,
            "is_public": True,
            "members": [],
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/api/groups/create",
                json=data,
                headers=self._get_headers(),
            ) as resp:
                result = await resp.json()
                if resp.status == 200:
                    console.print(
                        f"✅ Группа создана! JID: {result['jid']}", style="green"
                    )
                    return result["jid"]
                else:
                    console.print(
                        f"❌ Ошибка создания группы: {result.get('detail', 'Unknown error')}",
                        style="red",
                    )
                    return None

    async def list_groups(self):
        """Список групп пользователя"""
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{self.base_url}/api/groups/list", headers=self._get_headers()
            ) as resp:
                result = await resp.json()
                if resp.status == 200:
                    groups = result if isinstance(result, list) else []
                    if groups:
                        table = Table(title="Мои группы")
                        table.add_column("Название", style="cyan")
                        table.add_column("JID", style="magenta")
                        table.add_column("Роль", style="green")

                        for group in groups:
                            table.add_row(
                                group.get("name", "N/A"),
                                group.get("jid", "N/A"),
                                group.get("role", "N/A"),
                            )

                        console.print(table)
                    else:
                        console.print("📭 У вас нет групп", style="yellow")
                    return True
                else:
                    console.print(
                        f"❌ Ошибка получения групп: {result.get('detail', 'Unknown error')}",
                        style="red",
                    )
                    return False

    async def create_call(self, participants: list, call_type: str = "video"):
        """Создание звонка"""
        data = {"participants": participants, "call_type": call_type}

        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/api/calls/create",
                json=data,
                headers=self._get_headers(),
            ) as resp:
                result = await resp.json()
                if resp.status == 200:
                    console.print(
                        f"✅ Звонок создан! Room: {result['room_name']}", style="green"
                    )
                    console.print(
                        f"🔗 LiveKit URL: {result['livekit_url']}", style="blue"
                    )
                    console.print("🎫 Токены для участников:", style="yellow")

                    for participant, token in result["tokens"].items():
                        console.print(f"  {participant}: {token[:50]}...", style="dim")

                    return result
                else:
                    console.print(
                        f"❌ Ошибка создания звонка: {result.get('detail', 'Unknown error')}",
                        style="red",
                    )
                    return None

    async def upload_file(self, file_path: str):
        """Загрузка файла"""
        try:
            import os

            if not os.path.exists(file_path):
                console.print(f"❌ Файл не найден: {file_path}", style="red")
                return None

            async with aiohttp.ClientSession() as session:
                with open(file_path, "rb") as f:
                    data = aiohttp.FormData()
                    data.add_field("file", f, filename=os.path.basename(file_path))

                    async with session.post(
                        f"{self.base_url}/api/files/upload",
                        data=data,
                        headers=self._get_headers(),
                    ) as resp:
                        result = await resp.json()
                        if resp.status == 200:
                            console.print(
                                f"✅ Файл загружен! ID: {result['file_id']}",
                                style="green",
                            )
                            return result
                        else:
                            console.print(
                                f"❌ Ошибка загрузки файла: {result.get('detail', 'Unknown error')}",
                                style="red",
                            )
                            return None
        except Exception as e:
            console.print(f"❌ Ошибка при загрузке файла: {str(e)}", style="red")
            return None

    async def invite_to_group(self, group_id: str, user_jids: list):
        """Приглашение пользователей в группу"""
        data = {"user_jids": user_jids}

        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/api/groups/{group_id}/invite",
                json=data,
                headers=self._get_headers(),
            ) as resp:
                result = await resp.json()
                if resp.status == 200:
                    console.print(
                        f"✅ Приглашено {len(result['added_members'])} пользователей",
                        style="green",
                    )
                    return True
                else:
                    console.print(
                        f"❌ Ошибка приглашения: {result.get('detail', 'Unknown error')}",
                        style="red",
                    )
                    return False

    async def kick_from_group(self, group_id: str, user_jid: str):
        """Исключение пользователя из группы"""
        data = {"user_jid": user_jid}

        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/api/groups/{group_id}/kick",
                json=data,
                headers=self._get_headers(),
            ) as resp:
                result = await resp.json()
                if resp.status == 200:
                    console.print(
                        f"✅ Пользователь {user_jid} исключен из группы", style="green"
                    )
                    return True
                else:
                    console.print(
                        f"❌ Ошибка исключения: {result.get('detail', 'Unknown error')}",
                        style="red",
                    )
                    return False


async def main_menu(client: MessengerClient):
    """Главное меню"""
    while True:
        console.print("\n" + "=" * 50)
        console.print(Panel.fit("🚀 Консольный мессенджер", style="bold blue"))

        if client.user_jid:
            console.print(f"Пользователь: {client.user_jid}", style="green")

        console.print("\nВыберите действие:")
        console.print("1. Отправить сообщение")
        console.print("2. Посмотреть историю сообщений")
        console.print("3. Создать группу")
        console.print("4. Список моих групп")
        console.print("5. Создать звонок")
        console.print("6. Загрузить файл")
        console.print("7. Пригласить в группу")
        console.print("8. Исключить из группы")
        console.print("9. Выйти")

        choice = Prompt.ask(
            "Ваш выбор", choices=["1", "2", "3", "4", "5", "6", "7", "8", "9"]
        )

        if choice == "1":
            to_jid = Prompt.ask("JID получателя (например, user@localhost)")
            body = Prompt.ask("Текст сообщения")

            # Спрашиваем о файле
            attach_file = Prompt.ask("Прикрепить файл? (y/n)", default="n")
            file_id = None

            if attach_file.lower() == "y":
                file_path = Prompt.ask("Путь к файлу")
                file_result = await client.upload_file(file_path)
                if file_result:
                    file_id = file_result["file_id"]

            await client.send_message(to_jid, body, file_id=file_id)

        elif choice == "2":
            jid = Prompt.ask("JID собеседника")
            await client.get_message_history(jid)

        elif choice == "3":
            name = Prompt.ask("Название группы")
            description = Prompt.ask("Описание группы (необязательно)", default="")
            await client.create_group(name, description or None)

        elif choice == "4":
            await client.list_groups()

        elif choice == "5":
            participants_input = Prompt.ask("JID участников (через запятую)")
            participants = [
                p.strip() for p in participants_input.split(",") if p.strip()
            ]
            call_type = Prompt.ask(
                "Тип звонка", choices=["video", "audio"], default="video"
            )
            await client.create_call(participants, call_type)

        elif choice == "6":
            file_path = Prompt.ask("Путь к файлу")
            await client.upload_file(file_path)

        elif choice == "7":
            group_id = Prompt.ask("ID группы")
            user_jids_input = Prompt.ask(
                "JID пользователей для приглашения (через запятую)"
            )
            user_jids = [
                jid.strip() for jid in user_jids_input.split(",") if jid.strip()
            ]
            await client.invite_to_group(group_id, user_jids)

        elif choice == "8":
            group_id = Prompt.ask("ID группы")
            user_jid = Prompt.ask("JID пользователя для исключения")
            await client.kick_from_group(group_id, user_jid)

        elif choice == "9":
            console.print("👋 До свидания!", style="blue")
            break


async def main(url="http://localhost:8000"):
    """Консольный мессенджер"""
    client = MessengerClient(url)

    console.print(
        Panel.fit("🚀 Добро пожаловать в консольный мессенджер!", style="bold blue")
    )

    # Проверяем доступность API
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{url}/api/health") as resp:
                if resp.status != 200:
                    console.print("❌ API недоступен", style="red")
                    return
    except Exception:
        console.print("❌ Не удается подключиться к API", style="red")
        return

    # Авторизация или регистрация
    while True:
        action = Prompt.ask("Выберите действие", choices=["login", "register", "exit"])

        if action == "exit":
            return

        username = Prompt.ask("Имя пользователя")
        password = Prompt.ask("Пароль", password=True)

        if action == "register":
            display_name = Prompt.ask("Отображаемое имя", default=username)
            success = await client.register(username, password, display_name)
            if success:
                # После регистрации сразу авторизуемся
                await client.login(username, password)
                break

        elif action == "login":
            success = await client.login(username, password)
            if success:
                break

    # Главное меню
    if client.user_jid:
        await main_menu(client)


if __name__ == "__main__":
    import sys

    url = sys.argv[1] if len(sys.argv) > 1 else "http://localhost:8000"
    asyncio.run(main(url))
