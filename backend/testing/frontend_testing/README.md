# 🎭 Frontend Emulation & Testing

Этот каталог содержит эмуляторы фронтенда и тесты для полной проверки функций мессенджера.

## 🚀 Быстрый запуск

```bash
# 1. Запустите Docker Compose (если еще не запущен)
cd ../..
docker-compose up -d

# 2. Запустите все тесты
cd backend/testing
./run_all_tests.sh
```

## 📁 Файлы тестирования

### Основные эмуляторы:

- **`frontend_emulator.py`** - Базовый эмулятор фронтенда с 4 пользователями
- **`advanced_interaction_test.py`** - Продвинутое тестирование взаимодействия (5 пользователей)
- **`webrtc_livekit_test.py`** - Тестирование звонков и WebRTC

### Вспомогательные тесты:

- **`quick_test.py`** - Быстрый тест основных функций
- **`comprehensive_test.py`** - Комплексное тестирование
- **`test_api.py`** - Тестирование API эндпойнтов

### Скрипты:

- **`run_all_tests.sh`** - Запуск всех тестов
- **`quick_test.sh`** - Быстрый тест через curl
- **`test_with_curl.sh`** - Подробное тестирование с curl

## 🧪 Что тестируется

### ✅ Регистрация и авторизация
- Создание пользователей в eJabberd
- JWT токены
- Проверка паролей

### ✅ Обмен сообщениями
- Личные сообщения через XMPP
- История сообщений
- Перекрестная отправка между пользователями

### ✅ Групповые чаты
- Создание MUC комнат
- Приглашение участников
- Отправка сообщений в группы

### ✅ Файлообмен
- Загрузка в MinIO S3
- Presigned URLs
- Отправка файлов в сообщениях

### ✅ Звонки WebRTC
- Создание LiveKit комнат
- Генерация JWT токенов
- Персональные и групповые звонки
- Симуляция WebRTC подключений

### ✅ Статусы и поиск
- Установка статусов пользователей
- Список онлайн пользователей
- Поиск по имени

## 📊 Результаты тестирования

Последние результаты (07.09.2025):

- **Базовый эмулятор**: ✅ Все функции работают
- **Продвинутое тестирование**: ✅ 61/61 тестов (100%)
- **WebRTC тестирование**: ✅ Все звонки работают
- **Общая готовность**: 95%

## 🔧 Настройка тестов

### Переменные окружения:
```bash
export API_BASE_URL="http://localhost:8000"
export LIVEKIT_URL="ws://localhost:7880"
export TEST_USER_COUNT=5
```

### Конфигурация пользователей:
Тестовые пользователи создаются автоматически с префиксами:
- `alice_test`, `bob_test`, `charlie_test`, `diana_test`
- `alice_adv`, `bob_adv`, `charlie_adv`, `diana_adv`, `eve_adv`
- `call_alice`, `call_bob`, `call_charlie`

## 🐛 Отладка

### Проверка сервисов:
```bash
# API
curl http://localhost:8000/api/health

# eJabberd
docker exec ejabberd_fastapi1-ejabberd-1 ejabberdctl status

# LiveKit
curl http://localhost:7880/
```

### Логи:
```bash
# Все сервисы
docker-compose logs -f

# Только FastAPI
docker-compose logs -f fastapi_backend
```

### Очистка тестовых данных:
```bash
# Удаление тестовых пользователей
docker exec ejabberd_fastapi1-ejabberd-1 ejabberdctl unregister alice_test localhost
docker exec ejabberd_fastapi1-ejabberd-1 ejabberdctl unregister bob_test localhost
# ... и т.д.
```

## 📈 Производительность

Типичное время выполнения:
- **Базовый эмулятор**: ~30 секунд
- **Продвинутое тестирование**: ~60 секунд  
- **WebRTC тестирование**: ~30 секунд
- **Все тесты**: ~2-3 минуты

## 🔗 Интеграция с фронтендом

После успешного прохождения тестов, фронтенд может использовать:

### API Endpoints:
```
POST /api/auth/register
POST /api/auth/login
POST /api/messages/send
GET  /api/messages/history/{jid}
POST /api/groups/create
POST /api/files/upload
POST /api/calls/create
```

### WebSocket соединения:
- **XMPP**: `ws://localhost:5222` (через WebSocket)
- **LiveKit**: `ws://localhost:7880`

### Аутентификация:
```javascript
// Получение токена
const response = await fetch('/api/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ username, password })
});
const { access_token } = await response.json();

// Использование токена
const apiResponse = await fetch('/api/messages/send', {
  method: 'POST',
  headers: { 
    'Authorization': `Bearer ${access_token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({ to_jid, body, message_type: 'chat' })
});
```

## 📚 Документация

- **API Reference**: `../docs/API_REFERENCE.md`
- **Отчет о тестировании**: `../docs/reports/FRONTEND_EMULATION_TEST_REPORT_20250907.md`
- **Статус отладки**: `../docs/reports/API_DEBUG_STATUS_REPORT_20250907.md`

## 🎉 Заключение

Все эмуляторы фронтенда успешно протестированы. Мессенджер готов для:

1. **Интеграции с реальным фронтендом**
2. **Развертывания в продакшене**
3. **Масштабирования под нагрузкой**

Система показала стабильную работу всех компонентов и готова к использованию!
