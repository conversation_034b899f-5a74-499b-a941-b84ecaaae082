#!/usr/bin/env python3
"""
🚀 Advanced Interaction Test - Продвинутое тестирование взаимодействия

Тестирует реальное взаимодействие между пользователями:
- Многопользовательские чаты
- Файлообмен между пользователями
- Групповые звонки
- Статусы и уведомления
- WebRTC соединения (проверка инициализации)
"""

import asyncio
import aiohttp
import json
import os
from typing import List, Optional
from dataclasses import dataclass
from datetime import datetime
# import websockets  # Не используется в текущих тестах
# import ssl


@dataclass
class TestUser:
    """Тестовый пользователь с расширенными возможностями"""

    username: str
    password: str
    display_name: str
    email: str
    phone: str
    token: Optional[str] = None
    jid: Optional[str] = None
    status: str = "offline"
    messages_sent: int = 0
    messages_received: int = 0
    files_uploaded: int = 0
    calls_made: int = 0


class AdvancedMessengerTest:
    """Продвинутое тестирование мессенджера"""

    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.users: List[TestUser] = []
        self.session: Optional[aiohttp.ClientSession] = None
        self.test_results = {
            "registration": 0,
            "authentication": 0,
            "messaging": 0,
            "group_chats": 0,
            "file_sharing": 0,
            "calls": 0,
            "webrtc_init": 0,
            "status_updates": 0,
            "user_search": 0,
            "total_tests": 0,
        }

        # Создаем расширенный набор пользователей
        self.create_advanced_users()

    def create_advanced_users(self):
        """Создание расширенного набора пользователей"""
        users_data = [
            (
                "alice_adv",
                "alice123",
                "Alice Advanced",
                "<EMAIL>",
                "+1111111111",
            ),
            ("bob_adv", "bob123", "Bob Advanced", "<EMAIL>", "+1111111112"),
            (
                "charlie_adv",
                "charlie123",
                "Charlie Advanced",
                "<EMAIL>",
                "+1111111113",
            ),
            (
                "diana_adv",
                "diana123",
                "Diana Advanced",
                "<EMAIL>",
                "+1111111114",
            ),
            ("eve_adv", "eve123", "Eve Advanced", "<EMAIL>", "+1111111115"),
        ]

        self.users = [TestUser(*data) for data in users_data]

    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()

    async def api_request(
        self,
        method: str,
        endpoint: str,
        data: dict = None,
        token: str = None,
        files: dict = None,
    ) -> dict:
        """Выполнение API запроса с поддержкой файлов"""
        url = f"{self.base_url}/api{endpoint}"
        headers = {}

        if token:
            headers["Authorization"] = f"Bearer {token}"

        try:
            if files:
                # Для загрузки файлов
                async with self.session.request(
                    method, url, data=files, headers=headers
                ) as response:
                    response_text = await response.text()
                    if response.status == 200:
                        return json.loads(response_text) if response_text else {}
                    else:
                        return {"error": response_text, "status": response.status}
            else:
                # Обычные JSON запросы
                if data:
                    headers["Content-Type"] = "application/json"

                async with self.session.request(
                    method, url, json=data, headers=headers
                ) as response:
                    response_text = await response.text()
                    if response.status == 200:
                        return json.loads(response_text) if response_text else {}
                    else:
                        return {"error": response_text, "status": response.status}

        except Exception as e:
            return {"error": str(e)}

    def log_test(self, test_name: str, success: bool, details: str = ""):
        """Логирование результата теста"""
        emoji = "✅" if success else "❌"
        print(f"{emoji} {test_name}: {details}")

        if success:
            self.test_results[test_name] += 1
        self.test_results["total_tests"] += 1

    def print_section(self, title: str):
        """Красивый заголовок секции"""
        print(f"\n🚀 {title}")
        print("=" * (len(title) + 4))


async def test_user_lifecycle(tester: AdvancedMessengerTest):
    """Тест полного жизненного цикла пользователей"""
    tester.print_section("Жизненный цикл пользователей")

    # Регистрация всех пользователей
    for user in tester.users:
        result = await tester.api_request(
            "POST",
            "/auth/register",
            {
                "username": user.username,
                "password": user.password,
                "display_name": user.display_name,
                "email": user.email,
                "phone": user.phone,
            },
        )

        success = result.get("success", False)
        tester.log_test("registration", success, f"User {user.username}")

        if success:
            user.status = "registered"

    # Авторизация всех пользователей
    for user in tester.users:
        if user.status == "registered":
            result = await tester.api_request(
                "POST",
                "/auth/login",
                {"username": user.username, "password": user.password},
            )

            if result.get("success") and result.get("access_token"):
                user.token = result["access_token"]
                user.jid = result["user_jid"]
                user.status = "authenticated"
                tester.log_test("authentication", True, f"User {user.username}")
            else:
                tester.log_test(
                    "authentication", False, f"User {user.username}: {result}"
                )


async def test_cross_user_messaging(tester: AdvancedMessengerTest):
    """Тест перекрестного обмена сообщениями"""
    tester.print_section("Перекрестный обмен сообщениями")

    authenticated_users = [u for u in tester.users if u.token]
    if len(authenticated_users) < 2:
        tester.log_test("messaging", False, "Недостаточно авторизованных пользователей")
        return

    # Каждый пользователь отправляет сообщение каждому другому
    for sender in authenticated_users:
        for receiver in authenticated_users:
            if sender != receiver:
                message = f"Привет, {receiver.display_name}! Это {sender.display_name}. Время: {datetime.now().strftime('%H:%M:%S')}"

                result = await tester.api_request(
                    "POST",
                    "/messages/send",
                    {"to_jid": receiver.jid, "body": message, "message_type": "chat"},
                    sender.token,
                )

                if result.get("success"):
                    sender.messages_sent += 1
                    receiver.messages_received += 1
                    tester.log_test(
                        "messaging", True, f"{sender.username} → {receiver.username}"
                    )
                else:
                    tester.log_test(
                        "messaging",
                        False,
                        f"{sender.username} → {receiver.username}: {result}",
                    )

                # Небольшая пауза между сообщениями
                await asyncio.sleep(0.1)


async def test_group_interactions(tester: AdvancedMessengerTest):
    """Тест групповых взаимодействий"""
    tester.print_section("Групповые взаимодействия")

    authenticated_users = [u for u in tester.users if u.token]
    if len(authenticated_users) < 3:
        tester.log_test("group_chats", False, "Недостаточно пользователей для группы")
        return

    creator = authenticated_users[0]
    members = authenticated_users[1:]

    # Создаем группу
    result = await tester.api_request(
        "POST",
        "/groups/create",
        {
            "name": "Продвинутая тестовая группа",
            "description": "Группа для продвинутого тестирования функций",
            "is_public": True,
            "members": [u.jid for u in members],
        },
        creator.token,
    )

    if result.get("success"):
        group_id = result["group_id"]
        group_jid = result["jid"]
        tester.log_test("group_chats", True, f"Группа создана: {group_id}")

        # Каждый участник отправляет сообщение в группу
        for user in authenticated_users:
            message = f"Сообщение в группу от {user.display_name}!"

            msg_result = await tester.api_request(
                "POST",
                "/messages/send",
                {"to_jid": group_jid, "body": message, "message_type": "groupchat"},
                user.token,
            )

            if msg_result.get("success"):
                tester.log_test("group_chats", True, f"Сообщение от {user.username}")
            else:
                tester.log_test(
                    "group_chats", False, f"Сообщение от {user.username}: {msg_result}"
                )

            await asyncio.sleep(0.1)
    else:
        tester.log_test("group_chats", False, f"Ошибка создания группы: {result}")


async def test_file_sharing_network(tester: AdvancedMessengerTest):
    """Тест сети обмена файлами"""
    tester.print_section("Сеть обмена файлами")

    authenticated_users = [u for u in tester.users if u.token]
    if not authenticated_users:
        tester.log_test("file_sharing", False, "Нет авторизованных пользователей")
        return

    # Каждый пользователь создает и загружает файл
    for i, user in enumerate(authenticated_users):
        # Создаем уникальный файл для каждого пользователя
        file_content = f"""Файл от {user.display_name}
Время создания: {datetime.now()}
Пользователь: {user.username}
Email: {user.email}
Тестовые данные: {"A" * 100}
"""

        file_path = f"test_file_{user.username}.txt"

        with open(file_path, "w", encoding="utf-8") as f:
            f.write(file_content)

        try:
            # Загружаем файл
            data = aiohttp.FormData()
            data.add_field(
                "file",
                open(file_path, "rb"),
                filename=file_path,
                content_type="text/plain",
            )

            url = f"{tester.base_url}/api/files/upload"
            headers = {"Authorization": f"Bearer {user.token}"}

            async with tester.session.post(url, data=data, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    file_id = result.get("file_id")
                    user.files_uploaded += 1
                    tester.log_test(
                        "file_sharing",
                        True,
                        f"Файл загружен {user.username}: {file_id}",
                    )

                    # Отправляем файл другим пользователям
                    for other_user in authenticated_users:
                        if other_user != user:
                            msg_result = await tester.api_request(
                                "POST",
                                "/messages/send",
                                {
                                    "to_jid": other_user.jid,
                                    "body": f"Отправляю тебе файл от {user.display_name}!",
                                    "message_type": "chat",
                                    "file_id": file_id,
                                },
                                user.token,
                            )

                            if msg_result.get("success"):
                                tester.log_test(
                                    "file_sharing",
                                    True,
                                    f"Файл отправлен {user.username} → {other_user.username}",
                                )
                            else:
                                tester.log_test(
                                    "file_sharing",
                                    False,
                                    f"Ошибка отправки файла: {msg_result}",
                                )
                else:
                    error_text = await response.text()
                    tester.log_test(
                        "file_sharing",
                        False,
                        f"Ошибка загрузки {user.username}: {error_text}",
                    )

        finally:
            # Удаляем временный файл
            if os.path.exists(file_path):
                os.remove(file_path)


if __name__ == "__main__":

    async def main():
        print("🚀 Запуск продвинутого тестирования взаимодействия")
        print("=" * 55)

        async with AdvancedMessengerTest() as tester:
            await test_user_lifecycle(tester)
            await test_cross_user_messaging(tester)
            await test_group_interactions(tester)
            await test_file_sharing_network(tester)

            # Выводим финальную статистику
            print("\n📊 Финальная статистика тестирования")
            print("=" * 40)

            for test_name, count in tester.test_results.items():
                if test_name != "total_tests":
                    print(f"✅ {test_name}: {count} успешных тестов")

            total = tester.test_results["total_tests"]
            successful = sum(
                v for k, v in tester.test_results.items() if k != "total_tests"
            )
            success_rate = (successful / total * 100) if total > 0 else 0

            print(f"\n🎯 Общий результат: {successful}/{total} ({success_rate:.1f}%)")

            if success_rate >= 90:
                print("🎉 Отличный результат! Мессенджер работает стабильно!")
            elif success_rate >= 70:
                print("👍 Хороший результат! Есть небольшие проблемы для исправления.")
            else:
                print("⚠️ Требуется доработка. Много ошибок в тестах.")

    asyncio.run(main())
