#!/usr/bin/env python3
"""
🎭 Frontend Emulator - Эмуляция фронтенда мессенджера

Создает несколько "пользователей" и тестирует все функции мессенджера:
- Регистрация и авторизация
- Обмен сообщениями
- Групповые чаты
- Звонки через LiveKit
- Загрузка файлов
- Статусы пользователей
"""

import asyncio
import aiohttp
import json
import os
from typing import List, Optional
from dataclasses import dataclass
from datetime import datetime


@dataclass
class User:
    """Эмулированный пользователь"""

    username: str
    password: str
    display_name: str
    email: str
    phone: str
    token: Optional[str] = None
    jid: Optional[str] = None


class MessengerEmulator:
    """Эмулятор фронтенда мессенджера"""

    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.users: List[User] = []
        self.session: Optional[aiohttp.ClientSession] = None

        # Создаем тестовых пользователей
        self.create_test_users()

    def create_test_users(self):
        """Создание тестовых пользователей"""
        test_users = [
            User(
                username="alice_test",
                password="alice123",
                display_name="Alice Cooper",
                email="<EMAIL>",
                phone="+1234567890",
            ),
            User(
                username="bob_test",
                password="bob123",
                display_name="Bob Dylan",
                email="<EMAIL>",
                phone="+1234567891",
            ),
            User(
                username="charlie_test",
                password="charlie123",
                display_name="Charlie Brown",
                email="<EMAIL>",
                phone="+1234567892",
            ),
            User(
                username="diana_test",
                password="diana123",
                display_name="Diana Prince",
                email="<EMAIL>",
                phone="+1234567893",
            ),
        ]
        self.users = test_users

    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()

    async def api_request(
        self, method: str, endpoint: str, data: dict = None, token: str = None
    ) -> dict:
        """Выполнение API запроса"""
        url = f"{self.base_url}/api{endpoint}"
        headers = {"Content-Type": "application/json"}

        if token:
            headers["Authorization"] = f"Bearer {token}"

        try:
            async with self.session.request(
                method, url, json=data, headers=headers
            ) as response:
                response_text = await response.text()

                if response.status == 200:
                    return json.loads(response_text) if response_text else {}
                else:
                    print(f"❌ API Error {response.status}: {response_text}")
                    return {"error": response_text, "status": response.status}

        except Exception as e:
            print(f"❌ Request failed: {e}")
            return {"error": str(e)}

    def print_step(self, step: str, emoji: str = "🔄"):
        """Красивый вывод шага тестирования"""
        print(f"\n{emoji} {step}")
        print("=" * (len(step) + 4))

    def print_success(self, message: str):
        """Вывод успешного результата"""
        print(f"✅ {message}")

    def print_error(self, message: str):
        """Вывод ошибки"""
        print(f"❌ {message}")

    def print_info(self, message: str):
        """Информационное сообщение"""
        print(f"📊 {message}")


async def test_user_registration(emulator: MessengerEmulator):
    """Тест регистрации пользователей"""
    emulator.print_step("Регистрация пользователей", "👤")

    success_count = 0
    for user in emulator.users:
        emulator.print_info(f"Регистрируем пользователя: {user.username}")

        result = await emulator.api_request(
            "POST",
            "/auth/register",
            {
                "username": user.username,
                "password": user.password,
                "display_name": user.display_name,
                "email": user.email,
                "phone": user.phone,
            },
        )

        if result.get("success"):
            emulator.print_success(f"Пользователь {user.username} зарегистрирован")
            success_count += 1
        else:
            emulator.print_error(f"Ошибка регистрации {user.username}: {result}")

    emulator.print_info(
        f"Зарегистрировано: {success_count}/{len(emulator.users)} пользователей"
    )


async def test_user_authentication(emulator: MessengerEmulator):
    """Тест авторизации пользователей"""
    emulator.print_step("Авторизация пользователей", "🔐")

    success_count = 0
    for user in emulator.users:
        emulator.print_info(f"Авторизуем пользователя: {user.username}")

        result = await emulator.api_request(
            "POST",
            "/auth/login",
            {"username": user.username, "password": user.password},
        )

        if result.get("success") and result.get("access_token"):
            user.token = result["access_token"]
            user.jid = result["user_jid"]
            emulator.print_success(f"Пользователь {user.username} авторизован")
            success_count += 1
        else:
            emulator.print_error(f"Ошибка авторизации {user.username}: {result}")

    emulator.print_info(
        f"Авторизовано: {success_count}/{len(emulator.users)} пользователей"
    )


async def main():
    """Главная функция тестирования"""
    print("🎭 Запуск эмулятора фронтенда мессенджера")
    print("=" * 50)

    async with MessengerEmulator() as emulator:
        # Тестируем все функции
        await test_user_registration(emulator)
        await test_user_authentication(emulator)

        # Проверяем что пользователи авторизованы
        authorized_users = [u for u in emulator.users if u.token]
        if len(authorized_users) < 2:
            print("❌ Недостаточно авторизованных пользователей для продолжения тестов")
            return

        await test_messaging(emulator)
        await test_group_chats(emulator)
        await test_file_uploads(emulator)
        await test_calls(emulator)
        await test_user_statuses(emulator)
        await test_user_search(emulator)

        # Финальный отчет
        print_final_report()


def print_final_report():
    """Финальный отчет о тестировании"""
    print("\n🎉 Тестирование завершено!")
    print("=" * 30)
    print("📊 Все функции мессенджера протестированы")
    print("✅ Готово к использованию!")


async def test_messaging(emulator: MessengerEmulator):
    """Тест обмена сообщениями"""
    emulator.print_step("Обмен сообщениями", "💬")

    authorized_users = [u for u in emulator.users if u.token]
    if len(authorized_users) < 2:
        emulator.print_error("Недостаточно авторизованных пользователей")
        return

    alice = authorized_users[0]
    bob = authorized_users[1]

    # Alice отправляет сообщение Bob
    emulator.print_info(f"{alice.username} отправляет сообщение {bob.username}")
    result = await emulator.api_request(
        "POST",
        "/messages/send",
        {
            "to_jid": bob.jid,
            "body": f"Привет, {bob.display_name}! Это тестовое сообщение от {alice.display_name}",
            "message_type": "chat",
        },
        alice.token,
    )

    if result.get("success"):
        emulator.print_success("Сообщение отправлено")
    else:
        emulator.print_error(f"Ошибка отправки: {result}")

    # Bob отвечает Alice
    emulator.print_info(f"{bob.username} отвечает {alice.username}")
    result = await emulator.api_request(
        "POST",
        "/messages/send",
        {
            "to_jid": alice.jid,
            "body": f"Привет, {alice.display_name}! Получил твое сообщение!",
            "message_type": "chat",
        },
        bob.token,
    )

    if result.get("success"):
        emulator.print_success("Ответ отправлен")
    else:
        emulator.print_error(f"Ошибка ответа: {result}")

    # Проверяем историю сообщений
    emulator.print_info(f"Получаем историю сообщений для {alice.username}")
    result = await emulator.api_request(
        "GET", f"/messages/history/{bob.jid}", token=alice.token
    )

    if "messages" in result:
        emulator.print_success(f"История получена: {len(result['messages'])} сообщений")
    else:
        emulator.print_error(f"Ошибка получения истории: {result}")


async def test_group_chats(emulator: MessengerEmulator):
    """Тест групповых чатов"""
    emulator.print_step("Групповые чаты", "👥")

    authorized_users = [u for u in emulator.users if u.token]
    if len(authorized_users) < 3:
        emulator.print_error("Недостаточно пользователей для группового чата")
        return

    alice = authorized_users[0]
    bob = authorized_users[1]
    charlie = authorized_users[2]

    # Alice создает группу
    emulator.print_info(f"{alice.username} создает групповой чат")
    result = await emulator.api_request(
        "POST",
        "/groups/create",
        {
            "name": "Тестовая группа",
            "description": "Группа для тестирования функций мессенджера",
            "is_public": True,
            "members": [bob.jid, charlie.jid],
        },
        alice.token,
    )

    if result.get("success"):
        group_id = result["group_id"]
        group_jid = result["jid"]
        emulator.print_success(f"Группа создана: {group_id}")

        # Отправляем сообщение в группу
        emulator.print_info("Отправляем сообщение в группу")
        msg_result = await emulator.api_request(
            "POST",
            "/messages/send",
            {
                "to_jid": group_jid,
                "body": "Привет всем в группе! Это тестовое сообщение.",
                "message_type": "groupchat",
            },
            alice.token,
        )

        if msg_result.get("success"):
            emulator.print_success("Сообщение в группу отправлено")
        else:
            emulator.print_error(f"Ошибка отправки в группу: {msg_result}")

        # Получаем информацию о группе
        emulator.print_info("Получаем информацию о группе")
        info_result = await emulator.api_request(
            "GET", f"/groups/{group_id}/info", token=alice.token
        )

        if "name" in info_result:
            emulator.print_success(
                f"Информация о группе получена: {info_result['name']}"
            )
        else:
            emulator.print_error(f"Ошибка получения информации: {info_result}")
    else:
        emulator.print_error(f"Ошибка создания группы: {result}")


async def test_file_uploads(emulator: MessengerEmulator):
    """Тест загрузки файлов"""
    emulator.print_step("Загрузка файлов", "📁")

    authorized_users = [u for u in emulator.users if u.token]
    if not authorized_users:
        emulator.print_error("Нет авторизованных пользователей")
        return

    alice = authorized_users[0]

    # Создаем тестовый файл
    test_file_content = (
        f"Тестовый файл от {alice.display_name}\nВремя создания: {datetime.now()}\n"
    )
    test_file_path = "test_upload.txt"

    with open(test_file_path, "w", encoding="utf-8") as f:
        f.write(test_file_content)

    try:
        # Загружаем файл через API
        emulator.print_info(f"{alice.username} загружает файл")

        # Для загрузки файла используем multipart/form-data
        data = aiohttp.FormData()
        data.add_field(
            "file",
            open(test_file_path, "rb"),
            filename="test_upload.txt",
            content_type="text/plain",
        )

        url = f"{emulator.base_url}/api/files/upload"
        headers = {"Authorization": f"Bearer {alice.token}"}

        async with emulator.session.post(url, data=data, headers=headers) as response:
            if response.status == 200:
                result = await response.json()
                file_id = result.get("file_id")
                emulator.print_success(f"Файл загружен: {file_id}")

                # Получаем ссылку на файл
                emulator.print_info("Получаем ссылку на файл")
                file_result = await emulator.api_request(
                    "GET", f"/files/{file_id}", token=alice.token
                )

                if "url" in file_result:
                    emulator.print_success("Ссылка на файл получена")

                    # Отправляем сообщение с файлом
                    if len(authorized_users) > 1:
                        bob = authorized_users[1]
                        emulator.print_info("Отправляем сообщение с файлом")
                        msg_result = await emulator.api_request(
                            "POST",
                            "/messages/send",
                            {
                                "to_jid": bob.jid,
                                "body": "Отправляю тебе файл!",
                                "message_type": "chat",
                                "file_id": file_id,
                            },
                            alice.token,
                        )

                        if msg_result.get("success"):
                            emulator.print_success("Сообщение с файлом отправлено")
                        else:
                            emulator.print_error(
                                f"Ошибка отправки с файлом: {msg_result}"
                            )
                else:
                    emulator.print_error(f"Ошибка получения ссылки: {file_result}")
            else:
                error_text = await response.text()
                emulator.print_error(f"Ошибка загрузки файла: {error_text}")

    finally:
        # Удаляем тестовый файл
        if os.path.exists(test_file_path):
            os.remove(test_file_path)


async def test_calls(emulator: MessengerEmulator):
    """Тест звонков через LiveKit"""
    emulator.print_step("Звонки через LiveKit", "📞")

    authorized_users = [u for u in emulator.users if u.token]
    if len(authorized_users) < 2:
        emulator.print_error("Недостаточно пользователей для звонка")
        return

    alice = authorized_users[0]
    bob = authorized_users[1]

    # Alice инициирует видео звонок с Bob
    emulator.print_info(f"{alice.username} звонит {bob.username}")
    result = await emulator.api_request(
        "POST",
        "/calls/create",
        {"participants": [bob.jid], "call_type": "video"},
        alice.token,
    )

    if "call_id" in result:
        call_id = result["call_id"]
        room_name = result["room_name"]
        livekit_url = result["livekit_url"]
        tokens = result["tokens"]

        emulator.print_success(f"Звонок создан: {call_id}")
        emulator.print_info(f"LiveKit комната: {room_name}")
        emulator.print_info(f"LiveKit URL: {livekit_url}")
        emulator.print_info(f"Токены сгенерированы для {len(tokens)} участников")

        # Проверяем что токены созданы для всех участников
        expected_participants = [alice.jid, bob.jid]
        for participant in expected_participants:
            if participant in tokens:
                emulator.print_success(f"Токен создан для {participant}")
            else:
                emulator.print_error(f"Токен НЕ создан для {participant}")

        # Получаем информацию о звонке
        emulator.print_info("Получаем информацию о звонке")
        call_info = await emulator.api_request(
            "GET", f"/calls/{call_id}", token=alice.token
        )

        if "livekit_room_id" in call_info:
            emulator.print_success("Информация о звонке получена")
            emulator.print_info(f"Статус звонка: {call_info.get('status', 'unknown')}")
        else:
            emulator.print_error(f"Ошибка получения информации о звонке: {call_info}")

        # Завершаем звонок
        emulator.print_info("Завершаем звонок")
        end_result = await emulator.api_request(
            "POST", f"/calls/{call_id}/end", token=alice.token
        )

        if end_result.get("success"):
            emulator.print_success("Звонок завершен")
        else:
            emulator.print_error(f"Ошибка завершения звонка: {end_result}")

    else:
        emulator.print_error(f"Ошибка создания звонка: {result}")


async def test_user_statuses(emulator: MessengerEmulator):
    """Тест статусов пользователей"""
    emulator.print_step("Статусы пользователей", "📊")

    authorized_users = [u for u in emulator.users if u.token]
    if not authorized_users:
        emulator.print_error("Нет авторизованных пользователей")
        return

    alice = authorized_users[0]

    # Устанавливаем статус
    emulator.print_info(f"{alice.username} устанавливает статус")
    result = await emulator.api_request(
        "POST",
        "/users/status",
        {"status": "online", "status_message": "Тестирую мессенджер! 🚀"},
        alice.token,
    )

    if result.get("success"):
        emulator.print_success("Статус установлен")
    else:
        emulator.print_error(f"Ошибка установки статуса: {result}")

    # Получаем список онлайн пользователей
    emulator.print_info("Получаем список онлайн пользователей")
    online_result = await emulator.api_request(
        "GET", "/users/online", token=alice.token
    )

    if "online_users" in online_result:
        online_count = len(online_result["online_users"])
        emulator.print_success(f"Онлайн пользователей: {online_count}")

        for user_info in online_result["online_users"]:
            emulator.print_info(
                f"  - {user_info.get('username', 'unknown')}: {user_info.get('status', 'unknown')}"
            )
    else:
        emulator.print_error(f"Ошибка получения онлайн пользователей: {online_result}")


async def test_user_search(emulator: MessengerEmulator):
    """Тест поиска пользователей"""
    emulator.print_step("Поиск пользователей", "🔍")

    authorized_users = [u for u in emulator.users if u.token]
    if not authorized_users:
        emulator.print_error("Нет авторизованных пользователей")
        return

    alice = authorized_users[0]

    # Ищем пользователей по запросу "test"
    emulator.print_info("Ищем пользователей по запросу 'test'")
    result = await emulator.api_request(
        "GET", "/users/search?q=test&limit=10", token=alice.token
    )

    if isinstance(result, list):
        emulator.print_success(f"Найдено пользователей: {len(result)}")

        for user_info in result:
            username = user_info.get("username", "unknown")
            display_name = user_info.get("display_name", "unknown")
            emulator.print_info(f"  - {username} ({display_name})")
    else:
        emulator.print_error(f"Ошибка поиска пользователей: {result}")

    # Ищем конкретного пользователя
    if len(authorized_users) > 1:
        bob = authorized_users[1]
        search_query = bob.username.split("_")[0]  # "bob" из "bob_test"

        emulator.print_info(f"Ищем пользователя по запросу '{search_query}'")
        result = await emulator.api_request(
            "GET", f"/users/search?q={search_query}", token=alice.token
        )

        if isinstance(result, list) and len(result) > 0:
            found_user = result[0]
            if bob.username in found_user.get("username", ""):
                emulator.print_success(f"Пользователь {bob.username} найден")
            else:
                emulator.print_error(
                    f"Пользователь {bob.username} не найден в результатах"
                )
        else:
            emulator.print_error(f"Пользователь {bob.username} не найден")


if __name__ == "__main__":
    asyncio.run(main())
