#!/bin/bash

# 🎭 Полное тестирование мессенджера - Запуск всех тестов
# Этот скрипт запускает все эмуляторы фронтенда и тесты

echo "🎭 Запуск полного тестирования мессенджера"
echo "=========================================="

# Проверяем что Docker Compose запущен
echo "🔍 Проверяем состояние сервисов..."
if ! curl -s http://localhost:8000/api/health > /dev/null; then
    echo "❌ API недоступен. Запустите Docker Compose:"
    echo "   docker-compose up -d"
    exit 1
fi

echo "✅ API доступен"

# Создаем директорию для отчетов если её нет
mkdir -p ../docs/reports

echo ""
echo "🧪 Тест 1: Базовый эмулятор фронтенда"
echo "======================================"
python3 frontend_emulator.py

echo ""
echo "🧪 Тест 2: Продвинутое тестирование взаимодействия"
echo "=================================================="
python3 advanced_interaction_test.py

echo ""
echo "🧪 Тест 3: Тестирование WebRTC и LiveKit"
echo "========================================"
python3 webrtc_livekit_test.py

echo ""
echo "🧪 Тест 4: Быстрый тест API (для сравнения)"
echo "==========================================="
python3 quick_test.py

echo ""
echo "🎉 Все тесты завершены!"
echo "======================="
echo ""
echo "📊 Отчеты сохранены в:"
echo "   - backend/docs/reports/FRONTEND_EMULATION_TEST_REPORT_20250907.md"
echo "   - backend/docs/reports/API_DEBUG_STATUS_REPORT_20250907.md"
echo ""
echo "🚀 Мессенджер готов к использованию!"
echo ""
echo "📋 Что протестировано:"
echo "   ✅ Регистрация и авторизация пользователей"
echo "   ✅ Обмен личными сообщениями"
echo "   ✅ Групповые чаты"
echo "   ✅ Загрузка и отправка файлов"
echo "   ✅ Звонки через LiveKit (WebRTC)"
echo "   ✅ Статусы пользователей"
echo "   ✅ Поиск пользователей"
echo ""
echo "🔗 Полезные ссылки:"
echo "   - API документация: http://localhost:8000/docs"
echo "   - eJabberd админка: http://localhost:5280/admin (admin/admin123)"
echo "   - MinIO консоль: http://localhost:9001 (minioadmin/minioadmin123)"
echo ""
echo "💡 Для интеграции с фронтендом используйте:"
echo "   - Base URL: http://localhost:8000"
echo "   - WebSocket: ws://localhost:7880 (LiveKit)"
echo "   - XMPP: localhost:5222"
