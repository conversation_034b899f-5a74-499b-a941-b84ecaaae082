
import axios from 'axios';
import { createClient } from 'stanza';

// --- Configuration ---
const API_BASE_URL = 'http://localhost:8000/api';
const XMPP_WS_URL = 'ws://localhost:5280/websocket';

// --- Test User Credentials ---
const TEST_USERNAME = `test-emulator-user-${Date.now()}`;
const TEST_PASSWORD = 'testpassword';
const TEST_DISPLAY_NAME = 'Test Emulator';
const RECIPIENT_JID = 'user3333@localhost';

console.log('--- Starting Frontend Emulator Test (Node.js) ---');

async function registerUser() {
    const url = `${API_BASE_URL}/auth/register`;
    const payload = {
        username: TEST_USERNAME,
        password: TEST_PASSWORD,
        display_name: TEST_DISPLAY_NAME,
        email: `${TEST_USERNAME}@example.com`,
        phone: '1234567890',
    };
    console.log(`[*] Attempting to register user: ${TEST_USERNAME}`);
    try {
        await axios.post(url, payload);
        console.log(`[+] Successfully registered user: ${TEST_USERNAME}`);
        return true;
    } catch (error) {
        if (error.response && error.response.status === 400 && error.response.data.detail.includes('already exists')) {
            console.log(`[*] User ${TEST_USERNAME} already exists. Proceeding to login.`);
            return true;
        } else {
            console.error(`[!] Failed to register user. Status: ${error.response?.status}, Body:`, error.response?.data);
            return false;
        }
    }
}

async function loginUser() {
    const url = `${API_BASE_URL}/auth/login`;
    const payload = { username: TEST_USERNAME, password: TEST_PASSWORD };
    console.log(`[*] Attempting to login as: ${TEST_USERNAME}`);
    try {
        const response = await axios.post(url, payload);
        if (response.status === 200) {
            const user_jid = response.data.user_jid;
            console.log(`[+] Successfully logged in. JID: ${user_jid}`);
            return { jid: user_jid, password: TEST_PASSWORD };
        }
    } catch (error) {
        console.error(`[!] Failed to login. Status: ${error.response?.status}, Body:`, error.response?.data);
    }
    return null;
}

async function runXMPPTest(credentials) {
    if (!credentials) return;

    console.log(`[*] Initializing XMPP client for ${credentials.jid}`);
    const client = createClient({
        jid: credentials.jid,
        password: credentials.password,
        transports: {
            websocket: XMPP_WS_URL,
        },
    });

    client.on('*', (eventName, data) => {
        // Log all events for debugging, but keep it concise
        if (typeof data === 'object') {
            // Avoid logging the full stanza XML unless needed
            console.log(`[XMPP Event] ${eventName}`, data.type || data.name || '');
        } else {
            console.log(`[XMPP Event] ${eventName}`, data || '');
        }
    });

    client.on('session:started', async () => {
        console.log('[XMPP] ✅ Session started! Sending presence.');
        await client.sendPresence();

        const messageBody = `Hello from Node.js emulator at ${new Date().toISOString()}`;
        console.log(`[XMPP] Sending message to ${RECIPIENT_JID}: '${messageBody}'`);
        await client.sendMessage({ to: RECIPIENT_JID, body: messageBody, type: 'chat' });

        console.log(`\n[XMPP] Now attempting to fetch message history with ${RECIPIENT_JID}...`);
        try {
            const history = await client.searchHistory({ with: RECIPIENT_JID });
            if (history && history.results.length > 0) {
                console.log('[XMPP] --- Start of Message History ---');
                for (const item of history.results) {
                    const message = item.item && item.item.message;
                    if (message) {
                        const { from, to, body } = message;
                        console.log(`  - Archived Msg from ${from.bare} to ${to.bare}: ${body}`);
                    } else {
                        console.log(`  - (Could not parse archived message: ${JSON.stringify(item)})`);
                    }
                }
                console.log('[XMPP] --- End of Message History ---');
            } else {
                console.log('[XMPP] [!] No message history found.');
            }
        } catch (err) {
            console.error('[XMPP] [!] Error fetching history:', err);
        }

        console.log('[XMPP] Test actions complete. Disconnecting.');
        await client.disconnect();
    });

    client.on('auth:failed', (err) => {
        console.error('[XMPP] ❌ Authentication failed:', err);
    });

    client.on('disconnected', (err) => {
        console.log('[XMPP] 🔌 Disconnected.');
    });

    console.log(`[*] Connecting to XMPP server at ${XMPP_WS_URL}`);
    client.connect();
}

async function main() {
    if (await registerUser()) {
        const credentials = await loginUser();
        await runXMPPTest(credentials);
    }
    console.log('\n--- Test Finished ---');
}

main();
