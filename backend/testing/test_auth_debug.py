#!/usr/bin/env python3
"""
Отладка аутентификации
"""

import asyncio
import aiohttp
import json

BASE_URL = "http://localhost:8000"


async def test_auth_scenarios():
    """Тестируем различные сценарии аутентификации"""

    async with aiohttp.ClientSession() as session:
        print("🔍 Testing authentication scenarios...\n")

        # Тест 1: Регистрация нового пользователя
        print("1️⃣ Testing new user registration...")
        new_user = {
            "username": "debuguser1",
            "password": "correctpass123",
            "display_name": "Debug User 1",
            "email": "<EMAIL>",
            "phone": "+1234567890",
        }

        async with session.post(f"{BASE_URL}/api/auth/register", json=new_user) as resp:
            data = await resp.json()
            print(f"   Status: {resp.status}")
            print(f"   Response: {data}")
            if resp.status == 200 and data.get("success"):
                print("   ✅ Registration successful")
            else:
                print("   ℹ️ User might already exist")
        print()

        # Тест 2: Авторизация с правильным паролем
        print("2️⃣ Testing login with correct password...")
        login_correct = {"username": "debuguser1", "password": "correctpass123"}

        async with session.post(
            f"{BASE_URL}/api/auth/login", json=login_correct
        ) as resp:
            data = await resp.json()
            print(f"   Status: {resp.status}")
            print(f"   Response: {json.dumps(data, indent=2)}")
            if resp.status == 200 and data.get("success"):
                print("   ✅ Correct password accepted")
                token = data.get("access_token")
            else:
                print("   ❌ Correct password rejected")
                token = None
        print()

        # Тест 3: Авторизация с неправильным паролем
        print("3️⃣ Testing login with incorrect password...")
        login_incorrect = {"username": "debuguser1", "password": "wrongpass123"}

        async with session.post(
            f"{BASE_URL}/api/auth/login", json=login_incorrect
        ) as resp:
            data = await resp.json()
            print(f"   Status: {resp.status}")
            print(f"   Response: {json.dumps(data, indent=2)}")
            if resp.status == 401:
                print("   ✅ Incorrect password correctly rejected")
            elif resp.status == 200 and data.get("success"):
                print("   ❌ Incorrect password incorrectly accepted")
            else:
                print("   ⚠️ Unexpected response")
        print()

        # Тест 4: Авторизация несуществующего пользователя
        print("4️⃣ Testing login with non-existent user...")
        login_nonexistent = {
            "username": "nonexistentuser999",
            "password": "anypassword",
        }

        async with session.post(
            f"{BASE_URL}/api/auth/login", json=login_nonexistent
        ) as resp:
            data = await resp.json()
            print(f"   Status: {resp.status}")
            print(f"   Response: {json.dumps(data, indent=2)}")
            if resp.status == 401:
                print("   ✅ Non-existent user correctly rejected")
            else:
                print("   ❌ Non-existent user handling failed")
        print()

        # Тест 5: Использование токена для защищенного эндпойнта
        if token:
            print("5️⃣ Testing protected endpoint with token...")
            headers = {"Authorization": f"Bearer {token}"}

            async with session.get(
                f"{BASE_URL}/api/users/search?q=debug", headers=headers
            ) as resp:
                data = await resp.json()
                print(f"   Status: {resp.status}")
                print(f"   Response: {json.dumps(data, indent=2)}")
                if resp.status == 200:
                    print("   ✅ Protected endpoint accessible with token")
                else:
                    print("   ❌ Protected endpoint failed with token")
            print()

        # Тест 6: Использование недействительного токена
        print("6️⃣ Testing protected endpoint with invalid token...")
        invalid_headers = {"Authorization": "Bearer invalid_token_123"}

        async with session.get(
            f"{BASE_URL}/api/users/search?q=debug", headers=invalid_headers
        ) as resp:
            data = await resp.json()
            print(f"   Status: {resp.status}")
            print(f"   Response: {json.dumps(data, indent=2)}")
            if resp.status == 401:
                print("   ✅ Invalid token correctly rejected")
            else:
                print("   ❌ Invalid token handling failed")
        print()


if __name__ == "__main__":
    asyncio.run(test_auth_scenarios())
