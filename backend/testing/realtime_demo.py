#!/usr/bin/env python3
"""
🎭 Real-time Demo - Демонстрация real-time связи

Демонстрирует работу WebSocket real-time уведомлений:
- Несколько пользователей подключаются к WebSocket
- Отправляют сообщения друг другу через REST API
- Получают уведомления в реальном времени через WebSocket
"""

import asyncio
import json
import websockets
import aiohttp
import logging

# Настройка логирования
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class RealtimeUser:
    """Пользователь для демонстрации real-time связи"""

    def __init__(self, username: str, password: str, display_name: str):
        self.username = username
        self.password = password
        self.display_name = display_name
        self.token = None
        self.user_jid = None
        self.websocket = None
        self.messages_received = 0
        self.typing_notifications = 0

    async def authenticate(self, base_url: str) -> bool:
        """Аутентификация пользователя"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{base_url}/api/auth/login",
                    json={"username": self.username, "password": self.password},
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        self.token = data.get("access_token")
                        self.user_jid = data.get("user_jid")
                        logger.info(
                            f"✅ {self.display_name} authenticated as {self.user_jid}"
                        )
                        return True
                    else:
                        logger.error(f"❌ {self.display_name} authentication failed")
                        return False
        except Exception as e:
            logger.error(f"❌ {self.display_name} authentication error: {e}")
            return False

    async def connect_websocket(self, ws_url: str) -> bool:
        """Подключение к WebSocket"""
        try:
            ws_endpoint = f"{ws_url}/api/websocket/ws?token={self.token}"
            self.websocket = await websockets.connect(ws_endpoint)
            logger.info(f"🔌 {self.display_name} connected to WebSocket")
            return True
        except Exception as e:
            logger.error(f"❌ {self.display_name} WebSocket connection failed: {e}")
            return False

    async def listen_messages(self):
        """Прослушивание WebSocket сообщений"""
        try:
            async for message in self.websocket:
                try:
                    data = json.loads(message)
                    await self.handle_websocket_message(data)
                except json.JSONDecodeError:
                    logger.error(f"❌ {self.display_name} received invalid JSON")
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"🔌 {self.display_name} WebSocket connection closed")
        except Exception as e:
            logger.error(f"❌ {self.display_name} WebSocket error: {e}")

    async def handle_websocket_message(self, data: dict):
        """Обработка WebSocket сообщения"""
        message_type = data.get("type")

        if message_type == "new_message":
            from_jid = data.get("from_jid")
            body = data.get("body")
            timestamp = data.get("timestamp")
            self.messages_received += 1

            logger.info(
                f"💬 {self.display_name} received message from {from_jid}: '{body}' at {timestamp}"
            )

        elif message_type == "typing":
            from_jid = data.get("from_jid")
            is_typing = data.get("is_typing")
            action = "started" if is_typing else "stopped"
            self.typing_notifications += 1

            logger.info(f"⌨️ {self.display_name} notified: {from_jid} {action} typing")

        elif message_type == "user_status":
            user_jid = data.get("user_jid")
            status = data.get("status")

            logger.info(f"👤 {self.display_name} notified: {user_jid} is {status}")

        elif message_type == "pong":
            logger.info(f"🏓 {self.display_name} received pong")

        elif message_type == "online_users":
            users = data.get("users", [])
            logger.info(
                f"👥 {self.display_name} sees {len(users)} online users: {users}"
            )

    async def send_message_api(self, base_url: str, to_jid: str, body: str) -> bool:
        """Отправка сообщения через REST API"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{base_url}/api/messages/send",
                    json={"to_jid": to_jid, "body": body, "message_type": "chat"},
                    headers={"Authorization": f"Bearer {self.token}"},
                ) as response:
                    if response.status == 200:
                        logger.info(
                            f"📤 {self.display_name} sent message to {to_jid}: '{body}'"
                        )
                        return True
                    else:
                        logger.error(f"❌ {self.display_name} failed to send message")
                        return False
        except Exception as e:
            logger.error(f"❌ {self.display_name} message send error: {e}")
            return False

    async def send_typing_indicator(self, to_jid: str, is_typing: bool):
        """Отправка индикатора печати через WebSocket"""
        try:
            message = {"type": "typing", "to_jid": to_jid, "is_typing": is_typing}
            await self.websocket.send(json.dumps(message))
            action = "started" if is_typing else "stopped"
            logger.info(f"⌨️ {self.display_name} {action} typing to {to_jid}")
        except Exception as e:
            logger.error(f"❌ {self.display_name} typing indicator error: {e}")

    async def ping(self):
        """Отправка ping"""
        try:
            await self.websocket.send(json.dumps({"type": "ping"}))
            logger.info(f"🏓 {self.display_name} sent ping")
        except Exception as e:
            logger.error(f"❌ {self.display_name} ping error: {e}")

    async def get_online_users(self):
        """Запрос онлайн пользователей"""
        try:
            await self.websocket.send(json.dumps({"type": "get_online_users"}))
            logger.info(f"👥 {self.display_name} requested online users")
        except Exception as e:
            logger.error(f"❌ {self.display_name} online users request error: {e}")

    async def close(self):
        """Закрытие соединения"""
        if self.websocket:
            await self.websocket.close()
            logger.info(f"🔌 {self.display_name} disconnected")


async def realtime_demo():
    """Демонстрация real-time связи"""
    base_url = "http://localhost:8000"
    ws_url = "ws://localhost:8000"

    # Создаем пользователей
    users = [
        RealtimeUser("alice_test", "alice123", "Alice"),
        RealtimeUser("bob_test", "bob123", "Bob"),
        RealtimeUser("charlie_test", "charlie123", "Charlie"),
    ]

    logger.info("🎭 Starting Real-time Demo")
    logger.info("=" * 50)

    try:
        # Аутентификация всех пользователей
        logger.info("🔐 Authenticating users...")
        for user in users:
            success = await user.authenticate(base_url)
            if not success:
                logger.error(f"❌ Failed to authenticate {user.display_name}")
                return

        # Подключение к WebSocket
        logger.info("🔌 Connecting to WebSocket...")
        for user in users:
            success = await user.connect_websocket(ws_url)
            if not success:
                logger.error(f"❌ Failed to connect {user.display_name} to WebSocket")
                return

        # Запускаем прослушивание сообщений в фоне
        logger.info("👂 Starting message listeners...")
        listen_tasks = []
        for user in users:
            task = asyncio.create_task(user.listen_messages())
            listen_tasks.append(task)

        # Даем время на подключение
        await asyncio.sleep(2)

        # Демонстрация функций
        logger.info("\n🧪 Demo 1: Ping test")
        for user in users:
            await user.ping()
            await asyncio.sleep(0.5)

        await asyncio.sleep(1)

        logger.info("\n🧪 Demo 2: Online users request")
        await users[0].get_online_users()
        await asyncio.sleep(1)

        logger.info("\n🧪 Demo 3: Typing indicators")
        alice, bob, charlie = users[0], users[1], users[2]

        # Alice начинает печатать Bob
        await alice.send_typing_indicator(bob.user_jid, True)
        await asyncio.sleep(1)

        # Alice прекращает печатать
        await alice.send_typing_indicator(bob.user_jid, False)
        await asyncio.sleep(1)

        logger.info("\n🧪 Demo 4: Real-time messaging")

        # Alice отправляет сообщение Bob
        await alice.send_message_api(
            base_url,
            bob.user_jid,
            "Hi Bob! This is a real-time demo message from Alice! 🚀",
        )
        await asyncio.sleep(1)

        # Bob отвечает Alice
        await bob.send_message_api(
            base_url,
            alice.user_jid,
            "Hello Alice! I received your message instantly via WebSocket! ⚡",
        )
        await asyncio.sleep(1)

        # Charlie отправляет сообщения всем
        await charlie.send_message_api(
            base_url,
            alice.user_jid,
            "Hey Alice! Charlie here! Real-time is working great! 🎉",
        )
        await asyncio.sleep(0.5)

        await charlie.send_message_api(
            base_url,
            bob.user_jid,
            "Hi Bob! This is Charlie! WebSocket notifications are awesome! 💫",
        )
        await asyncio.sleep(1)

        logger.info("\n🧪 Demo 5: Group conversation simulation")

        # Симулируем групповую беседу
        messages = [
            (alice, bob.user_jid, "Bob, are you ready for the meeting?"),
            (bob, alice.user_jid, "Yes Alice, I'm ready! Charlie, are you joining?"),
            (charlie, alice.user_jid, "I'll be there in 5 minutes!"),
            (charlie, bob.user_jid, "Thanks for waiting, Bob!"),
            (alice, charlie.user_jid, "Perfect! See you both soon."),
        ]

        for sender, recipient, message in messages:
            await sender.send_message_api(base_url, recipient, message)
            await asyncio.sleep(1.5)

        # Даем время на обработку всех сообщений
        await asyncio.sleep(3)

        # Статистика
        logger.info("\n📊 Demo Results:")
        logger.info("=" * 30)
        for user in users:
            logger.info(f"👤 {user.display_name}:")
            logger.info(f"   📨 Messages received: {user.messages_received}")
            logger.info(f"   ⌨️ Typing notifications: {user.typing_notifications}")

        logger.info("\n✅ Real-time demo completed successfully!")
        logger.info("🎉 All WebSocket notifications were delivered instantly!")

    except Exception as e:
        logger.error(f"❌ Demo failed: {e}")
    finally:
        # Закрываем все соединения
        logger.info("\n🧹 Cleaning up...")
        for task in listen_tasks:
            task.cancel()

        for user in users:
            await user.close()

        logger.info("🎭 Real-time demo finished!")


if __name__ == "__main__":
    asyncio.run(realtime_demo())
