#!/usr/bin/env python3
"""
Симуляция запросов фронтенда к API бекенда
"""

import asyncio
import aiohttp
from typing import Dict, Any

BASE_URL = "http://localhost:8000"


class FrontendSimulator:
    def __init__(self):
        self.session = None
        self.user_tokens = {}

    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    async def api_call(
        self, method: str, endpoint: str, data: Dict[Any, Any] = None, token: str = None
    ) -> Dict[str, Any]:
        """Выполнить API вызов"""
        url = f"{BASE_URL}{endpoint}"
        headers = {"Content-Type": "application/json"}

        if token:
            headers["Authorization"] = f"Bearer {token}"

        try:
            async with self.session.request(
                method, url, json=data, headers=headers
            ) as resp:
                response_data = await resp.json()
                return {
                    "status": resp.status,
                    "data": response_data,
                    "success": resp.status < 400,
                }
        except Exception as e:
            return {"status": 0, "error": str(e), "success": False}

    async def simulate_user_registration_and_login(
        self, username: str, password: str, display_name: str, email: str
    ):
        """Симуляция регистрации и входа пользователя"""
        print(f"👤 Simulating user flow for {username}...")

        # 1. Регистрация
        print(f"   📝 Registering {username}...")
        reg_result = await self.api_call(
            "POST",
            "/api/auth/register",
            {
                "username": username,
                "password": password,
                "display_name": display_name,
                "email": email,
                "phone": f"+1555{hash(username) % 10000:04d}",
            },
        )

        if reg_result["success"]:
            print("   ✅ Registration successful")
        else:
            print(
                f"   ℹ️ User might already exist: {reg_result.get('data', {}).get('detail', 'Unknown error')}"
            )

        # 2. Авторизация
        print(f"   🔐 Logging in {username}...")
        login_result = await self.api_call(
            "POST", "/api/auth/login", {"username": username, "password": password}
        )

        if login_result["success"]:
            token = login_result["data"]["access_token"]
            user_jid = login_result["data"]["user_jid"]
            self.user_tokens[username] = token
            print(f"   ✅ Login successful, JID: {user_jid}")
            return token
        else:
            print(
                f"   ❌ Login failed: {login_result.get('data', {}).get('detail', 'Unknown error')}"
            )
            return None

    async def simulate_messaging_flow(self, sender: str, receiver: str):
        """Симуляция обмена сообщениями"""
        print(f"💬 Simulating messaging between {sender} and {receiver}...")

        sender_token = self.user_tokens.get(sender)
        receiver_token = self.user_tokens.get(receiver)

        if not sender_token or not receiver_token:
            print("   ❌ Missing tokens for messaging")
            return

        # 1. Отправка сообщения от sender к receiver
        message_text = f"Hello {receiver}, this is {sender}!"
        print(f"   📤 {sender} sending message to {receiver}...")

        send_result = await self.api_call(
            "POST",
            "/api/messages/send",
            {
                "to_jid": f"{receiver}@localhost",
                "body": message_text,
                "message_type": "chat",
            },
            sender_token,
        )

        if send_result["success"]:
            print("   ✅ Message sent successfully")
        else:
            print(
                f"   ❌ Message sending failed: {send_result.get('data', {}).get('detail', 'Unknown error')}"
            )
            return

        # 2. Получение истории сообщений
        print(f"   📥 {receiver} checking message history...")

        history_result = await self.api_call(
            "GET",
            f"/api/messages/history/{sender}@localhost?limit=5",
            token=receiver_token,
        )

        if history_result["success"]:
            messages = history_result["data"].get("messages", [])
            print(f"   ✅ Message history retrieved: {len(messages)} messages")

            # Показываем последнее сообщение
            if messages:
                last_msg = messages[-1]
                print(f'   📝 Last message: "{last_msg.get("txt", "N/A")}"')
        else:
            print(
                f"   ❌ Failed to get message history: {history_result.get('data', {}).get('detail', 'Unknown error')}"
            )

    async def simulate_group_chat_flow(self, admin_user: str, members: list):
        """Симуляция создания и использования группового чата"""
        print(
            f"👥 Simulating group chat flow with admin {admin_user} and members {members}..."
        )

        admin_token = self.user_tokens.get(admin_user)
        if not admin_token:
            print("   ❌ Missing admin token")
            return

        # 1. Создание группы
        print("   🏗️ Creating group chat...")
        group_result = await self.api_call(
            "POST",
            "/api/groups/create",
            {
                "name": "Frontend Test Group",
                "description": "Group created by frontend simulation",
                "is_public": True,
            },
            admin_token,
        )

        if not group_result["success"]:
            print(
                f"   ❌ Group creation failed: {group_result.get('data', {}).get('detail', 'Unknown error')}"
            )
            return

        group_id = group_result["data"]["group_id"]
        group_jid = group_result["data"].get(
            "group_jid", f"group-{group_id}@conference.localhost"
        )
        print(f"   ✅ Group created: {group_id}")

        # 2. Приглашение участников
        for member in members:
            if member in self.user_tokens:
                print(f"   👋 Inviting {member} to group...")
                invite_result = await self.api_call(
                    "POST",
                    f"/api/groups/{group_id}/invite",
                    {"user_jid": f"{member}@localhost"},
                    admin_token,
                )

                if invite_result["success"]:
                    print(f"   ✅ {member} invited successfully")
                else:
                    print(
                        f"   ❌ Failed to invite {member}: {invite_result.get('data', {}).get('detail', 'Unknown error')}"
                    )

        # 3. Получение информации о группе
        print("   ℹ️ Getting group info...")
        info_result = await self.api_call(
            "GET", f"/api/groups/{group_id}/info", token=admin_token
        )

        if info_result["success"]:
            group_info = info_result["data"]
            print(
                f"   ✅ Group info: {group_info.get('name')} ({group_info.get('member_count')} members)"
            )
        else:
            print(
                f"   ❌ Failed to get group info: {info_result.get('data', {}).get('detail', 'Unknown error')}"
            )

        return group_id

    async def simulate_file_upload_flow(self, username: str):
        """Симуляция загрузки файла"""
        print(f"📁 Simulating file upload for {username}...")

        token = self.user_tokens.get(username)
        if not token:
            print("   ❌ Missing token for file upload")
            return

        # Создаем тестовый файл
        test_content = f"Test file content from {username}\nUploaded at: {asyncio.get_event_loop().time()}"

        # Загружаем файл
        data = aiohttp.FormData()
        data.add_field(
            "file",
            test_content,
            filename=f"{username}_test.txt",
            content_type="text/plain",
        )

        headers = {"Authorization": f"Bearer {token}"}

        try:
            async with self.session.post(
                f"{BASE_URL}/api/files/upload", data=data, headers=headers
            ) as resp:
                if resp.status == 200:
                    result = await resp.json()
                    file_id = result["file_id"]
                    print(f"   ✅ File uploaded: {file_id}")

                    # Получаем URL файла
                    file_result = await self.api_call(
                        "GET", f"/api/files/{file_id}", token=token
                    )
                    if file_result["success"]:
                        print("   ✅ File URL retrieved")
                    else:
                        print("   ❌ Failed to get file URL")

                    return file_id
                else:
                    error_data = await resp.json()
                    print(
                        f"   ❌ File upload failed: {error_data.get('detail', 'Unknown error')}"
                    )
        except Exception as e:
            print(f"   ❌ File upload error: {e}")

        return None

    async def run_full_simulation(self):
        """Запуск полной симуляции фронтенда"""
        print("🚀 Starting full frontend simulation...\n")

        # Пользователи для тестирования
        users = [
            ("alice_sim", "password123", "Alice Simulator", "<EMAIL>"),
            ("bob_sim", "password456", "Bob Simulator", "<EMAIL>"),
            (
                "charlie_sim",
                "password789",
                "Charlie Simulator",
                "<EMAIL>",
            ),
        ]

        # 1. Регистрация и авторизация пользователей
        print("=" * 50)
        print("PHASE 1: User Registration and Authentication")
        print("=" * 50)

        for username, password, display_name, email in users:
            await self.simulate_user_registration_and_login(
                username, password, display_name, email
            )
            print()

        # 2. Обмен сообщениями
        print("=" * 50)
        print("PHASE 2: Messaging")
        print("=" * 50)

        await self.simulate_messaging_flow("alice_sim", "bob_sim")
        print()
        await self.simulate_messaging_flow("bob_sim", "charlie_sim")
        print()

        # 3. Групповые чаты
        print("=" * 50)
        print("PHASE 3: Group Chats")
        print("=" * 50)

        group_id = await self.simulate_group_chat_flow(
            "alice_sim", ["bob_sim", "charlie_sim"]
        )
        print()

        # 4. Загрузка файлов
        print("=" * 50)
        print("PHASE 4: File Uploads")
        print("=" * 50)

        for username, _, _, _ in users:
            await self.simulate_file_upload_flow(username)
            print()

        print("=" * 50)
        print("🎉 Frontend simulation completed successfully!")
        print("=" * 50)


async def main():
    """Главная функция"""
    async with FrontendSimulator() as simulator:
        await simulator.run_full_simulation()


if __name__ == "__main__":
    asyncio.run(main())
