#!/usr/bin/env python3
"""
🔌 WebSocket Test Client - Тестирование real-time связи

Тестирует WebSocket соединения и real-time уведомления:
- Подключение к WebSocket
- Получение уведомлений о сообщениях
- Индикаторы печати
- Статусы пользователей
"""

import asyncio
import json
import websockets
import aiohttp
from typing import Optional
import logging

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class WebSocketTestClient:
    """Тестовый WebSocket клиент"""

    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.ws_url = base_url.replace("http://", "ws://").replace("https://", "wss://")
        self.token: Optional[str] = None
        self.user_jid: Optional[str] = None
        self.websocket: Optional[websockets.WebSocketServerProtocol] = None

    async def authenticate(self, username: str, password: str) -> bool:
        """Аутентификация пользователя"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/api/auth/login",
                    json={"username": username, "password": password},
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        self.token = data.get("access_token")
                        self.user_jid = data.get("user_jid")
                        logger.info(f"✅ Authenticated as {self.user_jid}")
                        return True
                    else:
                        error = await response.text()
                        logger.error(f"❌ Authentication failed: {error}")
                        return False
        except Exception as e:
            logger.error(f"❌ Authentication error: {e}")
            return False

    async def connect_websocket(self) -> bool:
        """Подключение к WebSocket"""
        if not self.token:
            logger.error("❌ No authentication token")
            return False

        try:
            ws_endpoint = f"{self.ws_url}/api/websocket/ws?token={self.token}"
            self.websocket = await websockets.connect(ws_endpoint)
            logger.info(f"✅ WebSocket connected for {self.user_jid}")
            return True
        except Exception as e:
            logger.error(f"❌ WebSocket connection failed: {e}")
            return False

    async def send_message(self, message: dict):
        """Отправка сообщения через WebSocket"""
        if not self.websocket:
            logger.error("❌ WebSocket not connected")
            return

        try:
            await self.websocket.send(json.dumps(message))
            logger.info(f"📤 Sent: {message}")
        except Exception as e:
            logger.error(f"❌ Failed to send message: {e}")

    async def listen_messages(self):
        """Прослушивание входящих сообщений"""
        if not self.websocket:
            logger.error("❌ WebSocket not connected")
            return

        try:
            async for message in self.websocket:
                try:
                    data = json.loads(message)
                    await self.handle_message(data)
                except json.JSONDecodeError:
                    logger.error(f"❌ Invalid JSON received: {message}")
        except websockets.exceptions.ConnectionClosed:
            logger.info("🔌 WebSocket connection closed")
        except Exception as e:
            logger.error(f"❌ Error listening to messages: {e}")

    async def handle_message(self, data: dict):
        """Обработка входящего сообщения"""
        message_type = data.get("type")

        if message_type == "new_message":
            from_jid = data.get("from_jid")
            body = data.get("body")
            timestamp = data.get("timestamp")
            logger.info(f"💬 New message from {from_jid}: {body} ({timestamp})")

        elif message_type == "user_status":
            user_jid = data.get("user_jid")
            status = data.get("status")
            logger.info(f"👤 User status: {user_jid} is {status}")

        elif message_type == "typing":
            from_jid = data.get("from_jid")
            is_typing = data.get("is_typing")
            action = "started" if is_typing else "stopped"
            logger.info(f"⌨️ {from_jid} {action} typing")

        elif message_type == "pong":
            logger.info("🏓 Pong received")

        elif message_type == "online_users":
            users = data.get("users", [])
            logger.info(f"👥 Online users: {len(users)} - {users}")

        elif message_type == "room_joined":
            room_id = data.get("room_id")
            logger.info(f"🏠 Joined room: {room_id}")

        elif message_type == "room_left":
            room_id = data.get("room_id")
            logger.info(f"🚪 Left room: {room_id}")

        elif message_type == "error":
            error_msg = data.get("message")
            logger.error(f"❌ Server error: {error_msg}")

        else:
            logger.info(f"📨 Received: {data}")

    async def ping(self):
        """Отправка ping"""
        await self.send_message({"type": "ping"})

    async def get_online_users(self):
        """Запрос онлайн пользователей"""
        await self.send_message({"type": "get_online_users"})

    async def join_room(self, room_id: str):
        """Присоединение к комнате"""
        await self.send_message({"type": "join_room", "room_id": room_id})

    async def leave_room(self, room_id: str):
        """Выход из комнаты"""
        await self.send_message({"type": "leave_room", "room_id": room_id})

    async def start_typing(self, to_jid: str):
        """Начать печатать"""
        await self.send_message({"type": "typing", "to_jid": to_jid, "is_typing": True})

    async def stop_typing(self, to_jid: str):
        """Прекратить печатать"""
        await self.send_message(
            {"type": "typing", "to_jid": to_jid, "is_typing": False}
        )

    async def send_api_message(self, to_jid: str, body: str):
        """Отправка сообщения через REST API"""
        if not self.token:
            logger.error("❌ No authentication token")
            return

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/api/messages/send",
                    json={"to_jid": to_jid, "body": body, "message_type": "chat"},
                    headers={"Authorization": f"Bearer {self.token}"},
                ) as response:
                    if response.status == 200:
                        logger.info(f"📤 API message sent to {to_jid}: {body}")
                    else:
                        error = await response.text()
                        logger.error(f"❌ Failed to send API message: {error}")
        except Exception as e:
            logger.error(f"❌ API message error: {e}")

    async def close(self):
        """Закрытие соединения"""
        if self.websocket:
            await self.websocket.close()
            logger.info("🔌 WebSocket connection closed")


async def test_two_users_chat():
    """Тест чата между двумя пользователями"""
    logger.info("🧪 Starting two users chat test")

    # Создаем двух клиентов
    alice = WebSocketTestClient()
    bob = WebSocketTestClient()

    try:
        # Аутентификация
        if not await alice.authenticate("alice_test", "alice123"):
            logger.error("❌ Alice authentication failed")
            return

        if not await bob.authenticate("bob_test", "bob123"):
            logger.error("❌ Bob authentication failed")
            return

        # Подключение WebSocket
        if not await alice.connect_websocket():
            logger.error("❌ Alice WebSocket connection failed")
            return

        if not await bob.connect_websocket():
            logger.error("❌ Bob WebSocket connection failed")
            return

        # Запускаем прослушивание в фоне
        alice_task = asyncio.create_task(alice.listen_messages())
        bob_task = asyncio.create_task(bob.listen_messages())

        # Даем время на подключение
        await asyncio.sleep(1)

        # Тестируем различные функции
        logger.info("🧪 Testing ping")
        await alice.ping()
        await asyncio.sleep(0.5)

        logger.info("🧪 Testing online users")
        await alice.get_online_users()
        await asyncio.sleep(0.5)

        logger.info("🧪 Testing typing indicators")
        await alice.start_typing(bob.user_jid)
        await asyncio.sleep(1)
        await alice.stop_typing(bob.user_jid)
        await asyncio.sleep(0.5)

        logger.info("🧪 Testing message sending")
        await alice.send_api_message(
            bob.user_jid, "Hello Bob! This is a WebSocket test message."
        )
        await asyncio.sleep(1)

        await bob.send_api_message(
            alice.user_jid, "Hi Alice! I received your message via WebSocket!"
        )
        await asyncio.sleep(1)

        logger.info("🧪 Testing room functionality")
        test_room = "test-room-123"
        await alice.join_room(test_room)
        await bob.join_room(test_room)
        await asyncio.sleep(0.5)

        await alice.leave_room(test_room)
        await bob.leave_room(test_room)
        await asyncio.sleep(0.5)

        logger.info("✅ Test completed successfully!")

        # Даем время на обработку последних сообщений
        await asyncio.sleep(2)

    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
    finally:
        # Закрываем соединения
        alice_task.cancel()
        bob_task.cancel()
        await alice.close()
        await bob.close()


async def main():
    """Главная функция тестирования"""
    logger.info("🔌 Starting WebSocket Test Client")
    logger.info("=" * 50)

    await test_two_users_chat()

    logger.info("🎉 WebSocket testing completed!")


if __name__ == "__main__":
    asyncio.run(main())
