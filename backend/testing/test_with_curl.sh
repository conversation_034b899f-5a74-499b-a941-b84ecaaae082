#!/bin/bash

# Тестирование API мессенджера с помощью curl

BASE_URL="http://localhost:8000"
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting API tests with curl...${NC}\n"

# Функция для выполнения HTTP запросов
make_request() {
    local method=$1
    local endpoint=$2
    local data=$3
    local token=$4

    local url="${BASE_URL}${endpoint}"

    if [ ! -z "$token" ]; then
        if [ ! -z "$data" ]; then
            curl -s -X $method "$url" -H "Content-Type: application/json" -H "Authorization: Bearer $token" -d "$data"
        else
            curl -s -X $method "$url" -H "Content-Type: application/json" -H "Authorization: Bearer $token"
        fi
    else
        if [ ! -z "$data" ]; then
            curl -s -X $method "$url" -H "Content-Type: application/json" -d "$data"
        else
            curl -s -X $method "$url" -H "Content-Type: application/json"
        fi
    fi
}

# Тест 1: Health Check
echo -e "${YELLOW}🔍 Test 1: Health Check${NC}"
response=$(make_request "GET" "/api/health")
if echo "$response" | grep -q '"status":"healthy"'; then
    echo -e "${GREEN}✅ Health check passed${NC}"
else
    echo -e "${RED}❌ Health check failed: $response${NC}"
fi
echo

# Тест 2: Регистрация пользователя
echo -e "${YELLOW}🔍 Test 2: User Registration${NC}"
user_data='{"username":"curluser1","password":"testpass123","display_name":"Curl Test User","email":"<EMAIL>","phone":"+**********"}'
response=$(make_request "POST" "/api/auth/register" "$user_data")
if echo "$response" | grep -q '"success":true'; then
    echo -e "${GREEN}✅ User registration successful${NC}"
else
    echo -e "${YELLOW}ℹ️ User might already exist: $response${NC}"
fi
echo

# Тест 3: Авторизация пользователя
echo -e "${YELLOW}🔍 Test 3: User Login${NC}"
login_data='{"username":"curluser1","password":"testpass123"}'
response=$(make_request "POST" "/api/auth/login" "$login_data")
if echo "$response" | grep -q '"access_token"'; then
    token=$(echo "$response" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
    user_jid=$(echo "$response" | grep -o '"user_jid":"[^"]*"' | cut -d'"' -f4)
    echo -e "${GREEN}✅ User login successful${NC}"
    echo -e "${BLUE}   Token: ${token:0:20}...${NC}"
    echo -e "${BLUE}   JID: $user_jid${NC}"
else
    echo -e "${RED}❌ User login failed: $response${NC}"
    exit 1
fi
echo

# Тест 4: Неправильная авторизация
echo -e "${YELLOW}🔍 Test 4: Invalid Login${NC}"
invalid_login='{"username":"curluser1","password":"wrongpassword"}'
response=$(make_request "POST" "/api/auth/login" "$invalid_login")
if echo "$response" | grep -q '"detail":"Invalid credentials"'; then
    echo -e "${GREEN}✅ Invalid login correctly rejected${NC}"
else
    echo -e "${RED}❌ Invalid login test failed: $response${NC}"
fi
echo

# Тест 5: Регистрация второго пользователя
echo -e "${YELLOW}🔍 Test 5: Second User Registration${NC}"
user2_data='{"username":"curluser2","password":"testpass456","display_name":"Curl Test User 2","email":"<EMAIL>","phone":"+1234567891"}'
response=$(make_request "POST" "/api/auth/register" "$user2_data")
if echo "$response" | grep -q '"success":true'; then
    echo -e "${GREEN}✅ Second user registration successful${NC}"
else
    echo -e "${YELLOW}ℹ️ Second user might already exist: $response${NC}"
fi

# Авторизация второго пользователя
login2_data='{"username":"curluser2","password":"testpass456"}'
response=$(make_request "POST" "/api/auth/login" "$login2_data")
if echo "$response" | grep -q '"access_token"'; then
    token2=$(echo "$response" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
    echo -e "${GREEN}✅ Second user login successful${NC}"
else
    echo -e "${RED}❌ Second user login failed: $response${NC}"
fi
echo

# Тест 6: Отправка сообщения
echo -e "${YELLOW}🔍 Test 6: Send Message${NC}"
message_data='{"to_jid":"curluser2@localhost","body":"Hello from curl test!","message_type":"chat"}'
response=$(make_request "POST" "/api/messages/send" "$message_data" "$token")
if echo "$response" | grep -q '"success":true'; then
    echo -e "${GREEN}✅ Message sent successfully${NC}"
else
    echo -e "${RED}❌ Message sending failed: $response${NC}"
fi
echo

# Тест 7: Получение истории сообщений
echo -e "${YELLOW}🔍 Test 7: Get Message History${NC}"
response=$(make_request "GET" "/api/messages/history/curluser2@localhost?limit=5" "" "$token")
if echo "$response" | grep -q '"messages"'; then
    message_count=$(echo "$response" | grep -o '"messages":\[[^]]*\]' | grep -o '{"[^}]*}' | wc -l)
    echo -e "${GREEN}✅ Message history retrieved: $message_count messages${NC}"
else
    echo -e "${RED}❌ Message history retrieval failed: $response${NC}"
fi
echo

# Тест 8: Создание группы
echo -e "${YELLOW}🔍 Test 8: Create Group${NC}"
group_data='{"name":"Curl Test Group","description":"Group created by curl test","is_public":true}'
response=$(make_request "POST" "/api/groups/create" "$group_data" "$token")
if echo "$response" | grep -q '"group_id"'; then
    group_id=$(echo "$response" | grep -o '"group_id":"[^"]*"' | cut -d'"' -f4)
    echo -e "${GREEN}✅ Group created successfully${NC}"
    echo -e "${BLUE}   Group ID: $group_id${NC}"
else
    echo -e "${RED}❌ Group creation failed: $response${NC}"
fi
echo

# Тест 9: Получение списка групп
echo -e "${YELLOW}🔍 Test 9: Get Groups List${NC}"
response=$(make_request "GET" "/api/groups/list" "" "$token")
if echo "$response" | grep -q '"groups"'; then
    group_count=$(echo "$response" | grep -o '"groups":\[[^]]*\]' | grep -o '{"[^}]*}' | wc -l)
    echo -e "${GREEN}✅ Groups list retrieved: $group_count groups${NC}"
else
    echo -e "${RED}❌ Groups list retrieval failed: $response${NC}"
fi
echo

# Тест 10: Поиск пользователей
echo -e "${YELLOW}🔍 Test 10: Search Users${NC}"
response=$(make_request "GET" "/api/users/search?q=curl" "" "$token")
if echo "$response" | grep -q 'curluser'; then
    user_count=$(echo "$response" | grep -o '{"[^}]*}' | wc -l)
    echo -e "${GREEN}✅ User search successful: found $user_count users${NC}"
else
    echo -e "${RED}❌ User search failed: $response${NC}"
fi
echo

# Тест 11: Получение онлайн пользователей
echo -e "${YELLOW}🔍 Test 11: Get Online Users${NC}"
response=$(make_request "GET" "/api/users/online" "" "$token")
if echo "$response" | grep -q '"online_users"'; then
    online_count=$(echo "$response" | grep -o '"online_users":\[[^]]*\]' | grep -o '{"[^}]*}' | wc -l)
    echo -e "${GREEN}✅ Online users retrieved: $online_count users online${NC}"
else
    echo -e "${RED}❌ Online users retrieval failed: $response${NC}"
fi
echo

# Тест 12: Получение статистики
echo -e "${YELLOW}🔍 Test 12: Get System Stats${NC}"
response=$(make_request "GET" "/api/stats")
if echo "$response" | grep -q '"global"'; then
    echo -e "${GREEN}✅ System stats retrieved${NC}"
    echo -e "${BLUE}   Stats: $response${NC}"
else
    echo -e "${RED}❌ System stats retrieval failed: $response${NC}"
fi
echo

echo -e "${BLUE}🎉 All curl tests completed!${NC}"
echo -e "${GREEN}✅ API is working correctly${NC}"
