"""
Схемы для OTP аутентификации
"""

from typing import Optional
from pydantic import BaseModel, EmailStr


class OTPRequest(BaseModel):
    """Схема для запроса OTP кода"""
    
    email: EmailStr
    
    class Config:
        json_schema_extra = {
            "example": {
                "email": "<EMAIL>"
            }
        }


class OTPVerify(BaseModel):
    """Схема для верификации OTP кода"""
    
    email: EmailStr
    otp: str
    
    class Config:
        json_schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "otp": "123456"
            }
        }


class OTPResponse(BaseModel):
    """Схема ответа на запрос OTP"""
    
    success: bool
    message: str
    
    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "message": "OTP code sent to your email"
            }
        }


class AuthResponse(BaseModel):
    """Схема ответа после успешной аутентификации"""
    
    success: bool
    access_token: str
    token_type: str
    user_id: str
    email: str
    display_name: Optional[str] = None
    user_jid: str
    xmpp_username: str
    xmpp_password: str
    avatar: Optional[str] = None
    status: str
    is_new_user: bool  # Флаг, указывающий на новую регистрацию
    
    class Config:
        json_schema_extra = {
            "example": {
                "success": True,
                "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                "token_type": "bearer",
                "user_id": "123e4567-e89b-12d3-a456-426614174000",
                "email": "<EMAIL>",
                "display_name": "John Doe",
                "user_jid": "123e4567-e89b-12d3-a456-426614174000@localhost",
                "xmpp_username": "123e4567-e89b-12d3-a456-426614174000",
                "xmpp_password": "generated_secure_password",
                "avatar": None,
                "status": "online",
                "is_new_user": False
            }
        }
