"""
Схемы уведомлений
"""

from datetime import datetime
from typing import Dict, Any, List
from pydantic import BaseModel
from uuid import UUID


class NotificationInfo(BaseModel):
    """Информация об уведомлении"""

    id: UUID
    user_jid: str
    type: str
    title: str
    body: str
    data: Dict[str, Any]
    is_read: bool
    created_at: datetime

    class Config:
        from_attributes = True


class NotificationList(BaseModel):
    """Список уведомлений"""

    notifications: List[NotificationInfo]


class MarkNotificationsRead(BaseModel):
    """Схема для отметки уведомлений как прочитанных"""

    notification_ids: List[str]
