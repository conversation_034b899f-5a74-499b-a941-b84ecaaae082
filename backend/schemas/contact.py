"""
Схемы для контактов
"""

from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime


class ContactAdd(BaseModel):
    """Схема для добавления контакта"""

    username: str = <PERSON>(..., description="Username пользователя для добавления")


class ContactInfo(BaseModel):
    """Информация о контакте"""

    contact_jid: str
    contact_username: str
    contact_display_name: str
    added_at: datetime
    last_message_at: Optional[datetime] = None
    last_message_preview: Optional[str] = None
    unread_count: int = 0
    is_pinned: bool = False
    is_muted: bool = False

    class Config:
        from_attributes = True


class ChatInfo(BaseModel):
    """Информация о чате"""

    chat_jid: str
    chat_type: str
    chat_name: str
    chat_avatar: Optional[str] = None
    last_message_body: Optional[str] = None
    last_message_at: Optional[datetime] = None
    last_message_from: Optional[str] = None
    unread_count: int = 0
    total_messages: int = 0
    is_pinned: bool = False
    is_muted: bool = False
    is_archived: bool = False
    updated_at: datetime

    class Config:
        from_attributes = True


class ChatUpdate(BaseModel):
    """Обновление чата"""

    last_message_body: Optional[str] = None
    last_message_from: Optional[str] = None
    increment_unread: bool = False
    mark_as_read: bool = False
