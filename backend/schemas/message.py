"""
Схемы сообщений
"""

from typing import List, Dict, Any, Optional
from pydantic import BaseModel


class MessageSend(BaseModel):
    """Схема для отправки сообщения"""

    to_jid: str
    body: str
    message_type: str = "chat"  # chat или groupchat
    file_id: Optional[str] = None  # ID файла, если сообщение содержит файл


class MessageHistory(BaseModel):
    """Схема истории сообщений"""

    messages: List[Dict[str, Any]]
