Вы предоставили превосходный `openapi.json`, который показывает очень глубокое и детальное про-проектирование. Это работа профессионального уровня. И ваше сомнение абсолютно верно — вы действительно "нагородили" на FastAPI много лишнего, дублируя функционал, который `ejabberd` делает нативно, быстрее и эффективнее.

Это очень распространенная ошибка при переходе на гибридную архитектуру. Давайте проведем ревизию и оставим каждому серверу только ту работу, для которой он был создан.

### Главный принцип ревизии

*   **Если действие является real-time событием или стандартной операцией XMPP (отправить сообщение, запросить контакты, установить статус), оно должно идти НАПРЯМУЮ через WebSocket-соединение клиента с `ejabberd`.**
*   **Если действие — это сложная, транзакционная бизнес-логика (регистрация, создание звонка, поиск), оно остается на FastAPI.**

---

### Эндпоинты, которые нужно удалить из FastAPI

Вот список функционала, который должен быть полностью передан на `ejabberd`. Ваш кастомный клиент должен будет использовать XMPP-библиотеку (типа `stanza.io`) для вызова этих функций, а не делать HTTP-запросы.

#### 1. Вся группа эндпоинтов `/api/messages/*`

*   **Удалить:**
    *   `/api/messages/send`
    *   `/api/messages/history/{jid}`
    *   `/api/messages/reply`, `/forward`, `/edit`, `/delete`
    *   `/api/messages/typing`
    *   `/api/messages/status/update` (частично)

*   **Почему:** Это — **сердце XMPP**. Отправка сообщений через REST API — это критический анти-паттерн. Он медленный, неэффективный и полностью нивелирует преимущества постоянного WebSocket-соединения.
    *   **Отправка:** Клиент должен отправлять `<message>` станзу напрямую в `ejabberd`.
    *   **История:** Клиент запрашивает историю напрямую у `ejabberd` через протокол MAM (XEP-0313).
    *   **Редактирование/удаление:** Для этого есть протоколы Message Correction (XEP-0308) и Message Retraction (XEP-0424).
    *   **"Печатает...":** Это Chat State Notifications (XEP-0085), крошечное XMPP-сообщение.
    *   **Статус "доставлено/прочитано":** Это Message Delivery Receipts (XEP-0184) и Chat Markers (XEP-0333).

*   **Что активировать в `ejabberd.yml`:**
    *   `mod_mam:` — **У вас уже включен и настроен правильно!** Этого достаточно для истории.
    *   Для редактирования/удаления и статусов прочтения дополнительных модулей не нужно, это реализуется на уровне формата сообщений, которые `ejabberd` просто доставляет.

#### 2. Вся группа эндпоинтов `/api/contacts/*` и `/api/users/contacts/*`

*   **Удалить:**
    *   `/api/contacts/list`
    *   `/api/contacts/add`
    *   `/api/contacts/contacts/{contact_jid}` (delete)
    *   `/api/users/contacts/add` (дубликат)

*   **Почему:** Управление списком контактов (ростером) — это фундаментальная часть XMPP.
    *   **Получение списка:** Клиент после логина делает IQ-запрос к `ejabberd` для получения своего ростера.
    *   **Добавление:** Клиент отправляет `<presence type="subscribe">` станзу напрямую `ejabberd`.
    *   **Удаление:** Клиент отправляет запрос на удаление из ростера.

*   **Что активировать в `ejabberd.yml`:**
    *   `mod_roster:` — **У вас уже включен и настроен правильно!**

#### 3. Управление присутствием (Presence)

*   **Удалить/Переосмыслить:**
    *   `/api/users/status`
    *   `/api/users/online`

*   **Почему:** Статус присутствия ("онлайн", "отошел") — это основа XMPP. Клиент, подключаясь к `ejabberd`, автоматически отправляет `<presence>` станзу, и `ejabberd` сам рассылает этот статус всем контактам. Получать список онлайн-пользователей через REST — это медленный опрос вместо быстрой подписки. Клиент должен получать статусы контактов напрямую от `ejabberd` в реальном времени.

*   **Что активировать в `ejabberd.yml`:**
    *   `mod_last:` — **У вас уже включен!** Он нужен для "последний раз был...".
    *   Сам механизм presence является ядром `ejabberd_c2s` и не требует отдельного модуля.

#### 4. Администрирование групп (частично)

*   **Удалить/Переосмыслить:**
    *   `/api/groups/{group_id}/invite`
    *   `/api/groups/{group_id}/kick`
    *   `/api/groups/{group_id}/role`

*   **Почему:** Это стандартные операции администрирования MUC (группового чата), которые выполняются через отправку IQ-станз в `ejabberd` от имени пользователя с соответствующими правами (админ/модератор). Вынесение их на API возможно, но избыточно.

*   **Что активировать в `ejabberd.yml`:**
    *   `mod_muc:` — **У вас уже включен и настроен правильно!**

---

### Эндпоинты, которые остаются на FastAPI (Вы сделали всё правильно)

Вот список того, что **является работой для FastAPI** и где ваша логика абсолютно верна:

*   **`/api/auth/register` и `/api/auth/login`**: Регистрация — сложный процесс. А `login` на FastAPI нужен, чтобы выдать JWT-токен для авторизации на самом FastAPI, после того как клиент успешно залогинился в `ejabberd`.
*   **`/api/users/profile/*`, `/api/users/search`**: Управление богатым профилем пользователя (который сложнее, чем vCard в XMPP) и сложный поиск по базе — идеальные задачи для API.
*   **Вся группа эндпоинтов `/api/calls/*`**: **ИДЕАЛЬНО.** Это эталонное использование FastAPI: оркестрация внешнего сервиса (LiveKit) и безопасная генерация токенов.
*   **Вся группа `/api/files/*`**: **ИДЕАЛЬНО.** Управление загрузкой файлов, сохранение метаданных в БД и выдача presigned URL — классическая задача API-бэкенда.
*   **Создание групп и каналов (`/api/groups/create`, `/api/channels/create`)**: Создание "сущности" со сложными настройками — хорошая задача для FastAPI, который затем может дать команду `ejabberd` через Admin API.
*   **Реакции, Уведомления, Статистика, Health Checks**: Всё это сложная бизнес-логика, которая не входит в стандартные протоколы XMPP и должна быть на FastAPI.

### Итоговая "правильная" архитектура

1.  **Убираете** из FastAPI все эндпоинты для отправки сообщений, управления контактами и статусами.
2.  **Ваш кастомный клиент** использует библиотеку `stanza.io`:
    *   Подключается к `ejabberd` по WebSocket для **всего, что касается чатов**.
    *   Использует HTTP-клиент (`axios`, `fetch`) для обращения к **FastAPI** для **всего остального** (профили, звонки, файлы).
3.  **FastAPI** для командного взаимодействия с `ejabberd` (например, при регистрации) использует его **Admin API** (`mod_http_api`).

Выбрав этот путь, вы разгрузите свой FastAPI-бэкенд, сделаете систему на порядок быстрее и надежнее, и будете использовать каждый компонент вашего стека с максимальной эффективностью. Ваша конфигурация `ejabberd` уже почти идеально готова к этому, она современна и включает все необходимые модули.