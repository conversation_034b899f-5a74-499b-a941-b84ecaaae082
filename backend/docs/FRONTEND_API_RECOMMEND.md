## Руководство для разработчиков клиентов: Пользовательские сценарии и API

В этом документе описаны основные пользовательские сценарии мессенджера и эндпойнты API, необходимые для их реализации. Для всех защищенных запросов требуется `access_token`, полученный при авторизации, который передается в заголовке `Authorization: Bearer <your_jwt_token>`.

### 1. Регистрация нового пользователя

**Описание:** Пользователь впервые создает аккаунт в приложении.

**Эндпойнты:**
1.  **`POST /api/auth/register`**: Отправка данных пользователя для создания аккаунта.
    *   **Детали запроса:** В теле запроса передаются `username`, `password`, `display_name`, `email`, `phone`.
    *   **Результат:** В случае успеха пользователь зарегистрирован и может переходить к авторизации.

### 2. Авторизация пользователя

**Описание:** Существующий пользователь входит в свой аккаунт.

**Эндпойнты:**
1.  **`POST /api/auth/login`**: Отправка логина и пароля для получения токена доступа.
    *   **Детали запроса:** В теле запроса передаются `username` и `password`.
    *   **Сохраняемые детали:** Клиент должен сохранить `access_token` и `user_jid` из ответа для последующих запросов и идентификации пользователя.

### 3. Редактирование собственного профиля

**Описание:** Пользователь изменяет информацию в своем профиле (например, отображаемое имя или email).

**Эндпойнты:**
1.  **`POST /api/users/profile/update`**: Отправка обновленных данных профиля.
    *   **Детали запроса:** В теле запроса передаются поля, которые пользователь хочет изменить: `display_name`, `email`, `phone`.
    *   **Необходимые детали:** Требуется `access_token` авторизованного пользователя.

### 4. Поиск других пользователей

**Описание:** Пользователь ищет других людей в мессенджере по имени или никнейму.

**Эндпойнты:**
1.  **`GET /api/users/search`**: Получение списка пользователей по поисковому запросу.
    *   **Детали запроса:** В query-параметрах передается поисковый запрос `q`. Например, `/api/users/search?q=John`.
    *   **Результат:** Клиент получает список пользователей, из которого можно выбрать нужного для просмотра профиля или начала чата.

### 5. Просмотр профиля пользователя

**Описание:** Пользователь открывает экран с детальной информацией о другом пользователе.

**Эндпойнты:**
1.  **`GET /api/users/profile/{username}`**: Получение полной информации о профиле пользователя.
    *   **Детали запроса:** В пути запроса указывается `username` пользователя, профиль которого нужно посмотреть. Этот `username` может быть получен из списка поиска или из информации об участнике группы.

### 6. Установка и просмотр статуса

**Описание:** Пользователь устанавливает свой текущий статус (например, "в сети", "занят") и текстовое сообщение к нему.

**Эндпойнты:**
1.  **`POST /api/users/status`**: Обновление статуса текущего пользователя.
    *   **Детали запроса:** В теле запроса передается новый `status` и `status_message`.
    *   **Просмотр статусов:** Статус конкретного пользователя виден в его профиле (`GET /api/users/profile/{username}`), а также в списке участников группы (`GET /api/groups/{group_id}/members`).

### 7. Отправка текстового сообщения пользователю

**Описание:** Базовый сценарий отправки сообщения в личном чате.

**Эндпойнты:**
1.  **`POST /api/messages/send`**: Отправка сообщения.
    *   **Детали запроса:** В теле запроса указывается `to_jid` получателя (например, "jane_doe@localhost"), `body` (текст сообщения) и `message_type` ("chat").
    *   **Необходимые детали:** `to_jid` можно получить при поиске пользователя или из предыдущей переписки.

### 8. Просмотр истории сообщений

**Описание:** Пользователь открывает чат с другим пользователем и видит предыдущую переписку.

**Эндпойнты:**
1.  **`GET /api/messages/history/{jid}`**: Запрос истории сообщений.
    *   **Детали запроса:** В пути указывается `jid` собеседника. Можно использовать параметры `limit` и `offset` для пагинации.
    *   **Результат:** Клиент получает массив сообщений для отображения в чате.

### 9. Отправка сообщения с файлом

**Описание:** Пользователь прикрепляет и отправляет файл (изображение, документ) в чат.

**Эндпойнты (двухэтапный процесс):**
1.  **`POST /api/files/upload`**: Сначала файл загружается на сервер.
    *   **Детали запроса:** Запрос отправляется как `multipart/form-data` с полем `file`.
    *   **Сохраняемые детали:** Из ответа нужно сохранить `file_id`.
2.  **`POST /api/messages/send`**: Затем отправляется сообщение со ссылкой на загруженный файл.
    *   **Детали запроса:** В теле запроса передается `to_jid`, `body` (опциональный комментарий к файлу), `message_type` ("chat") и полученный на предыдущем шаге `file_id`.

### 10. Редактирование отправленного сообщения

**Описание:** Пользователь изменяет текст уже отправленного сообщения.

**Эндпойнты:**
1.  **`PUT /api/messages/edit`**: Обновление текста сообщения.
    *   **Детали запроса:** В теле запроса передается `message_id` редактируемого сообщения и `new_body` с новым текстом.
    *   **Необходимые детали:** `message_id` известен клиенту после отправки сообщения или при получении истории.

### 11. Удаление сообщения

**Описание:** Пользователь удаляет свое сообщение у себя или у всех участников чата.

**Эндпойнты:**
1.  **`DELETE /api/messages/delete`**: Удаление сообщения.
    *   **Детали запроса:** В теле запроса передается `message_id` и флаг `delete_for_everyone` (`true` или `false`).

### 12. Ответ на сообщение

**Описание:** Пользователь цитирует другое сообщение в своем ответе.

**Эндпойнты:**
1.  **`POST /api/messages/reply`**: Отправка сообщения в ответ на другое.
    *   **Детали запроса:** В теле указывается `to_jid`, `body` (текст ответа) и `reply_to_message_id` — ID сообщения, на которое отвечают.

### 13. Создание группового чата

**Описание:** Пользователь создает новый чат с несколькими участниками.

**Эндпойнты:**
1.  **`POST /api/groups/create`**: Создание группы.
    *   **Детали запроса:** В теле передается `name` и `description` группы, а также флаг `is_public`.
    *   **Сохраняемые детали:** Клиент должен сохранить `group_id` и `group_jid` для дальнейших действий с группой (отправка сообщений, приглашения).

### 14. Приглашение пользователей в группу

**Описание:** Администратор или участник группы добавляет в нее новых людей.

**Эндпойнты:**
1.  **`POST /api/groups/{group_id}/invite`**: Приглашение пользователя.
    *   **Детали запроса:** В пути указывается `group_id` группы, а в теле запроса — `user_jid` приглашаемого пользователя.

### 15. Отправка сообщения в группу

**Описание:** Пользователь пишет сообщение, которое увидят все участники группового чата.

**Эндпойнты:**
1.  **`POST /api/messages/send`**: Тот же эндпоинт, что и для личных сообщений.
    *   **Детали запроса:** В поле `to_jid` указывается `group_jid` группы (например, "<EMAIL>").

### 16. Видеозвонок (1-на-1 или групповой)

**Описание:** Пользователь инициирует видеозвонок с одним или несколькими собеседниками.

**Эндпойнты:**
1.  **`POST /api/calls/create`**: Создание звонка и получение токенов для подключения.
    *   **Детали запроса:** В теле передается массив `participants` с `jid` всех участников и `call_type` ("video" или "audio").
    *   **Результат:** В ответе приходят `livekit_url` и `tokens` для каждого участника. Эти данные клиент использует для подключения к медиа-серверу LiveKit.

### 17. Добавление реакции на сообщение

**Описание:** Пользователь ставит "лайк" или другую эмодзи-реакцию на сообщение.

**Эндпойнты:**
1.  **`POST /api/reactions/add`**: Добавление реакции.
    *   **Детали запроса:** В теле запроса указывается `message_id` и `emoji`, который пользователь выбрал для реакции.

### 18. Просмотр участников группы

**Описание:** Пользователь открывает список всех, кто состоит в групповом чате.

**Эндпойнты:**
1.  **`GET /api/groups/{group_id}/members`**: Получение списка участников.
    *   **Детали запроса:** В пути указывается `group_id` интересующей группы.
    *   **Результат:** Клиент получает массив участников с их ролями, статусами и другой информацией.