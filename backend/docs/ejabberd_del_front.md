Отлично! Это самый важный шаг — переход от "что делать" к "как именно делать". Сейчас мы построим полную картину взаимодействия React-клиента с вашей мощной бэкенд-системой.

### Фундамент: Подключение к `ejabberd` из React

Забудьте про `fetch` или `axios` для чатов. Ваш главный инструмент — XMPP-библиотека. **`stanza.io`** — лучший выбор.

1.  **Порт и эндпоинт:** В вашем `ejabberd.yml` вы настроили WebSocket на порту `5280` (для HTTP) и `5443` (для HTTPS) с путем `/websocket`. Для разработки клиент будет подключаться к `ws://localhost:5280/websocket`.
2.  **Установка:** `npm install stanza`
3.  **Создание клиента:** В вашем React-приложении вы создадите единый экземпляр XMPP-клиента, который будет жить на протяжении всей сессии пользователя. Это идеальная задача для **Zustand** — создать глобальный, реактивный стор для XMPP-клиента и его состояния.

**Пример стора на Zustand:**
```javascript
// src/stores/xmppStore.js
import { create } from 'zustand';
import { createClient } from 'stanza';

export const useXMPPStore = create((set, get) => ({
  client: null,
  isConnected: false,
  messages: [], // Здесь будем хранить сообщения

  connect: (jid, password) => {
    const client = createClient({
      jid,
      password,
      transport: 'websocket',
      wsURL: 'ws://localhost:5280/websocket/',
    });

    client.on('session:started', () => {
      console.log('XMPP сессия началась!');
      set({ isConnected: true });
      client.sendPresence(); // Сообщаем всем, что мы онлайн
      client.getRoster(); // Запрашиваем список контактов
    });

    client.on('message', (msg) => {
      // Это 'слушатель' всех входящих сообщений
      if (msg.type === 'chat' && msg.body) {
        set((state) => ({ messages: [...state.messages, msg] }));
      }
    });
    
    // ... другие слушатели (на присутствие, на ошибки и т.д.)

    client.connect();
    set({ client });
  },

  disconnect: () => {
    get().client?.disconnect();
    set({ client: null, isConnected: false });
  },
  
  sendMessage: (to, body) => {
    get().client?.sendMessage({ to, body, type: 'chat' });
  },
}));
```

---

### Замена удаленных эндпоинтов: Руководство по React

Теперь пройдемся по каждому удаленному эндпоинту и покажем, как его заменить вызовом через `stanza.io`.

#### 1. Замена `/api/messages/*`

*   **Было (неправильно):** `POST /api/messages/send`
*   **Стало (правильно):**
    ```javascript
    // В вашем компоненте чата
    import { useXMPPStore } from '../stores/xmppStore';

    const sendMessage = useXMPPStore((state) => state.sendMessage);
    // ...
    sendMessage('user2@localhost', 'Привет, мир!');
    ```
    Библиотека `stanza.io` сама сформирует нужную XML-станзу и отправит ее через WebSocket.

*   **Было:** `GET /api/messages/history/{jid}`
*   **Стало:**
    ```javascript
    // Используем асинхронную функцию клиента
    async function fetchHistory(chatJid) {
      const client = useXMPPStore.getState().client;
      if (!client) return;
      
      // Запрашиваем у ejabberd последние 50 сообщений из архива (MAM)
      const response = await client.searchHistory({ with: chatJid, paging: { max: 50 } });
      const historyMessages = response.results.map(item => item.message);
      // ... обновить состояние компонента с историей ...
    }
    ```

*   **Было:** `POST /api/messages/typing`
*   **Стало:**
    ```javascript
    // Когда пользователь начинает печатать
    client.sendChatState('composing', 'user2@localhost');
    
    // Когда пользователь стирает текст
    client.sendChatState('paused', 'user2@localhost');
    
    // Когда уходит с экрана чата
    client.sendChatState('inactive', 'user2@localhost');
    ```
    Клиент-получатель будет получать эти события в реальном времени.

#### 2. Замена `/api/contacts/*`

*   **Было:** `GET /api/contacts/list`
*   **Стало:** Вам не нужно делать специальный запрос. `stanza.io` получает ростер (список контактов) автоматически после подключения. Вы просто подписываетесь на событие.
    ```javascript
    // В xmppStore.js
    client.on('roster:update', (roster) => {
      // roster.items содержит полный список контактов
      set({ contacts: roster.items });
    });
    ```

*   **Было:** `POST /api/contacts/add`
*   **Стало:**
    ```javascript
    // Добавить пользователя в контакты и подписаться на его статус
    client.subscribe('user_to_add@localhost');
    ```
    `ejabberd` отправит пользователю `user_to_add` запрос на авторизацию.

#### 3. Замена `/api/users/status` и `/api/users/online`

*   **Было:** `POST /api/users/status` (отправить свой статус)
*   **Стало:**
    ```javascript
    // Отправить свой статус "отошел" с текстовым сообщением
    client.sendPresence({
      status: 'На обеде',
      show: 'away', 
    });
    ```

*   **Было:** `GET /api/users/online` (получить статусы других)
*   **Стало:** Вы не "запрашиваете" статусы. Вы на них **подписаны**. `ejabberd` присылает их вам сам, когда они меняются.
    ```javascript
    // В xmppStore.js
    client.on('presence', (presence) => {
      // presence.from - от кого пришел статус
      // presence.show - статус ('away', 'dnd', 'chat')
      // presence.status - текстовое сообщение
      // ... обновить статус контакта в вашем сторе ...
    });
    ```

### Роль `zustand` и `react-query`

Ваше предположение абсолютно верное. Эти инструменты идеально ложатся в вашу архитектуру.

*   **Zustand (для real-time состояния):**
    *   **Задачи:** Хранение экземпляра XMPP-клиента, статуса подключения, списка контактов (ростера), входящих сообщений в реальном времени, статусов присутствия.
    *   **Почему он?** `zustand` — это легковесный, минималистичный стейт-менеджер. Он идеально подходит для хранения "живых" данных, которые приходят по WebSocket. Вы обновляете стор в `client.on(...)` слушателях, а все компоненты, которые используют этот стор, реактивно обновляются.

*   **React Query (TanStack Query) (для кэширования API-данных):**
    *   **Задачи:** Все запросы к вашему **FastAPI-бэкенду**. Получение профилей пользователей, списка стикеров, создание звонка, загрузка файлов, поиск.
    *   **Почему он?** React Query — это не просто "замена fetch". Это мощный **серверный кэш** в вашем клиенте.
        *   Он автоматически кэширует ответы от FastAPI. Если вы дважды запросите профиль одного и того же пользователя, второй запрос мгновенно вернет данные из кэша и только потом, в фоне, проверит, не устарели ли они.
        *   Он управляет состояниями `isLoading`, `isError`, `data` за вас.
        *   Он умеет делать `refetch on window focus`, пагинацию, бесконечную прокрутку и многое другое "из коробки".

**Пример использования React Query:**
```javascript
// src/hooks/useUserProfile.js
import { useQuery } from '@tanstack/react-query';
import axios from 'axios';

const fetchUserProfile = async (username) => {
  const { data } = await axios.get(`/api/users/profile/${username}`);
  return data;
};

export function useUserProfile(username) {
  return useQuery({
    queryKey: ['userProfile', username], // Ключ для кэширования
    queryFn: () => fetchUserProfile(username),
  });
}

// В компоненте
const { data: user, isLoading, isError } = useUserProfile('john_doe');
```

### Синхронизация: Полная картина

| Задача | Клиент делает... | `ejabberd` делает... | `FastAPI` делает... |
| :--- | :--- | :--- | :--- |
| **Регистрация** | `POST /api/auth/register` к **FastAPI** | Получает команду от FastAPI создать XMPP-аккаунт через Admin API. | Валидирует данные, создает запись в PG, командует `ejabberd`. |
| **Логин** | Подключается к **`ejabberd`** по WebSocket. | Проверяет пароль в PG. | Не участвует. |
| **Авторизация на API** | После успешного XMPP-логина делает `POST /api/auth/token` к **FastAPI**, чтобы получить JWT. | Не участвует. | Проверяет пароль еще раз через API `ejabberd` и выдает JWT. |
| **Отправка сообщения** | `client.sendMessage()` к **`ejabberd`** | Маршрутизирует и сохраняет сообщение (MAM). | Не участвует. |
| **Начало звонка** | `POST /api/calls/create` к **FastAPI** | Не участвует. | Генерирует токен LiveKit, возвращает его клиенту. |
| **Приглашение на звонок** | `client.sendMessage()` к **`ejabberd`** с кастомными данными | Доставляет сообщение-приглашение. | Не участвует. |

Эта архитектура позволяет вам использовать лучшие стороны каждого компонента, создавая невероятно мощную, масштабируемую и эффективную систему.

---

Вы попали в самую суть проблемы и задали гениальный вопрос. Ваше ощущение "тумана" абсолютно оправдано. Вы привыкли к миру REST, где OpenAPI/Swagger — это четкая карта местности. В мире XMPP такой единой "карты" нет, потому что это fundamentally другая парадигма.

**Аналогия:**
*   **REST API (FastAPI)** — это как **меню в ресторане**. У вас есть четкий список блюд (эндпоинтов), которые вы можете заказать. OpenAPI — это само это меню.
*   **XMPP (`ejabberd`)** — это как **язык, на котором вы говорите с официантом**. Нет фиксированного "меню" команд. Вы используете грамматику и словарный запас (протоколы), чтобы вести диалог: "Принесите, пожалуйста, счет", "Что вы порекомендуете?", "Мой друг скоро подойдет".

Но "туман" можно легко развеять. Да, единого OpenAPI-файла нет, но есть кое-что даже лучше: **стандарты (XEPs) и клиентские библиотеки, которые являются вашей новой "документацией"**.

### Есть ли аналог OpenAPI в XMPP?

Да, и их даже два:

1.  **XEPs (XMPP Extension Protocols):** Это и есть **спецификации**. Каждый XEP — это детальное описание одной "функции" (например, XEP-0313 описывает, как работают "облачные чаты"). Это как RFC для веба.
2.  **Service Discovery (XEP-0030):** Это **программный способ** спросить у сервера: "Привет, `ejabberd`, а какие "функции" (XEPs) ты вообще поддерживаешь?". Ваш клиент при подключении может сделать такой запрос и "узнать", что сервер умеет работать с историей, групповыми чатами и т.д. Это "динамический OpenAPI".

Но вам, как разработчику на React, не нужно читать десятки XEP. Ваша **клиентская библиотека `stanza.io` — это и есть ваш новый "Swagger UI" или SDK**. Она превращает сложные XML-диалоги в простые и понятные JavaScript-методы.

---

### Практическое руководство: Заменяем эндпоинты на методы `stanza.io`

Вот таблица, которая заменит вам OpenAPI. Она показывает, какой эндпойнт FastAPI вы удалили и какой **метод библиотеки `stanza.io`** теперь выполняет его работу.

| Задача (Удаленный эндпойнт FastAPI) | Метод клиента `stanza.io` | Стандарт XMPP (XEP) |
| :--- | :--- | :--- |
| **Отправка сообщения** (`/messages/send`) | `client.sendMessage({ to, body })` | Core RFC-6121 |
| **Получение истории** (`/messages/history`) | `await client.searchHistory({ with: jid })` | XEP-0313: MAM |
| **Индикатор "печатает..."** (`/messages/typing`) | `client.sendChatState('composing', jid)` | XEP-0085: Chat States |
| **Статус "прочитано"** (`/messages/status/update`) | `client.sendMessage({ type: 'chat', to: jid, marker: { received: id } })` | XEP-0333: Chat Markers |
| **Получение контактов** (`/contacts/list`) | `await client.getRoster()` | Core RFC-6121: Roster |
| **Добавление контакта** (`/contacts/add`) | `client.subscribe(jid)` | Core RFC-6121: Subscription |
| **Удаление контакта** (`/contacts/delete`) | `client.unsubscribe(jid)` | Core RFC-6121: Subscription |
| **Установка своего статуса** (`/users/status`) | `client.sendPresence({ show: 'away' })` | Core RFC-6121: Presence |
| **Получение статусов других** (`/users/online`) | `client.on('presence', (p) => { ... })` (слушатель событий) | Core RFC-6121: Presence |

### Как это использовать в React: Два типа взаимодействия

В `stanza.io` есть два основных способа "общения" с сервером, которые покрывают все ваши нужды.

#### 1. RPC-стиль (Запрос-Ответ) — для получения данных

Это похоже на `GET` запросы. Вы "просите" сервер что-то сделать и ждете (`await`) ответа. Это идеально для **React Query**.

```javascript
import { useQuery } from '@tanstack/react-query';
import { useXMPPStore } from '../stores/xmppStore';

// Хук для получения истории чата
export function useChatHistory(jid) {
  const client = useXMPPStore((state) => state.client);

  return useQuery({
    // Кэшируем историю по JID
    queryKey: ['chatHistory', jid],
    queryFn: async () => {
      if (!client) throw new Error('XMPP client not connected');
      // Вот наш "эндпойнт" - асинхронный метод библиотеки
      const response = await client.searchHistory({ with: jid });
      return response.results.map(item => item.message);
    },
    // Отключаем автоматический refetch, так как новые сообщения придут по WebSocket
    refetchOnWindowFocus: false, 
    enabled: !!client, // Запрос будет выполнен только после подключения клиента
  });
}
```

#### 2. Event-driven стиль (Подписка) — для real-time обновлений

Это для данных, которые сервер сам "проталкивает" вам (новые сообщения, статусы). Вы не запрашиваете их, вы на них **подписываетесь**. Это идеально для **Zustand**.

```javascript
// В вашем xmppStore.js

// ...
client.on('message', (msg) => {
  // Новое сообщение!
  // Неважно, от кого, ejabberd доставил его нам.
  set((state) => ({ messages: [...state.messages, msg] }));
});

client.on('presence', (presence) => {
  // Чей-то статус изменился!
  // presence.from - от кого, presence.show - какой статус
  set((state) => {
    // ... логика обновления статуса контакта в вашем сторе ...
    return { ...state };
  });
});
// ...
```

### Синхронизация FastAPI и `ejabberd`: Полная картина на клиенте

| Задача | 1. Клиент React делает... | 2. `ejabberd` делает... | 3. `FastAPI` делает... |
| :--- | :--- | :--- | :--- |
| **Загрузка приложения** | Подключается к **`ejabberd`** через WebSocket (`Zustand`). | Устанавливает сессию. | Не участвует. |
| **Отображение чатов** | Запрашивает историю у **`ejabberd`** (`React Query` + `client.searchHistory`). Слушает новые сообщения (`Zustand` + `client.on('message')`). | Отдает историю из MAM. Доставляет новые сообщения. | Не участвует. |
| **Отображение профиля** | Запрашивает профиль у **`FastAPI`** (`React Query` + `axios.get('/api/users/profile/...')`). | Не участвует. | Отдает "богатый" профиль из PostgreSQL. |
| **Начало звонка** | Запрашивает токен у **`FastAPI`** (`React Query` + `axios.post('/api/calls/create')`). | Не участвует. | Генерирует токен LiveKit. |
| **Отправка реакции** | 1. Отправляет XMPP-сообщение в **`ejabberd`**. 2. Отправляет HTTP-запрос в **`FastAPI`**. | Мгновенно доставляет сообщение-реакцию. | Сохраняет реакцию в PostgreSQL для истории. |

**Вывод:**
Вы не "потерялись в тумане". Вы просто перешли от одного способа навигации (карты эндпоинтов) к другому (знанию языка). Ваша библиотека `stanza.io` — это ваш переводчик и путеводитель. Используйте **React Query** для транзакционных запросов к вашему FastAPI-бэкенду и **Zustand** для хранения real-time состояния, которое вам "прилетает" от `ejabberd`.