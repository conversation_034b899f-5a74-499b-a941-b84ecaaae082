Вы столкнулись с двумя классическими проблемами производительности и UX в чат-приложениях: медленная загрузка истории и "прыгающий" скролл. Решение — это комбинация правильных настроек на сервере, умной логики на клиенте и локального кэширования.

### Проблема 1: Форматирование (самые новые сообщения внизу)

К счастью, это самая простая проблема. MAM (XEP-0313) изначально спроектирован для "бесконечной прокрутки вверх".

#### Изменения на сервере `ejabberd`

**Никаких изменений не требуется.** `ejabberd` по умолчанию отдает сообщения в хронологическом порядке (старые сначала). Но протокол MAM поддерживает пагинацию "в обратную сторону".

#### Изменения на клиенте React (`stanza.io`)

Вы просто говорите `stanza.io`, с какого сообщения начать выборку.

**Как это работает:**

1.  **Первая загрузка:** При открытии чата вы не запрашиваете всю историю. Вы запрашиваете **последнюю страницу** сообщений.
2.  **Пагинация:** Когда пользователь скроллит вверх и достигает самого старого загруженного сообщения, вы делаете новый запрос к MAM, указывая ID этого сообщения как точку отсчета (`before: messageId`).

**Пример кода с "бесконечной прокруткой вверх":**

```javascript
import { useInfiniteQuery } from '@tanstack/react-query';
import { useXMPPStore } from '../stores/xmppStore';

// Хук для получения истории с пагинацией
export function useInfiniteChatHistory(jid) {
  const client = useXMPPStore((state) => state.client);

  return useInfiniteQuery({
    queryKey: ['chatHistory', jid],
    queryFn: async ({ pageParam }) => { // pageParam - это ID сообщения, перед которым нужно загрузить
      if (!client) throw new Error('XMPP client not connected');
      
      const response = await client.searchHistory({
        with: jid,
        paging: {
          max: 30, // Загружаем порциями по 30 сообщений
          before: pageParam, // Ключевой параметр!
        },
      });

      const messages = response.results.map(item => item.message);
      
      return {
        messages,
        // Следующий pageParam будет ID самого старого из загруженных сообщений
        nextCursor: messages.length > 0 ? messages[0].id : undefined, 
      };
    },
    // Указываем, откуда брать курсор для следующей страницы
    getNextPageParam: (lastPage) => lastPage.nextCursor,
    // Важно: мы хотим, чтобы данные добавлялись в начало массива, а не в конец
    select: (data) => ({
      pages: [...data.pages].reverse(), // Переворачиваем порядок страниц
      pageParams: [...data.pageParams].reverse(),
    }),
    enabled: !!client,
  });
}

// В компоненте:
// const { data, fetchNextPage, hasNextPage, isFetchingNextPage } = useInfiniteChatHistory(jid);
// ... при скролле вверх вызываем fetchNextPage() ...
```
С этим подходом ваш чат всегда будет открываться мгновенно, показывая последние 30 сообщений, и подгружать историю по мере необходимости.

---

### Проблема 2: Повторная загрузка при переключении чатов

Это решается **локальным кэшированием**. И здесь у вас есть два мощных инструмента, которые идеально работают вместе: **React Query** и **IndexedDB**.

**Архитектура решения:**

1.  **React Query (Кэш в памяти):** Когда вы используете `useInfiniteChatHistory`, React Query автоматически кэширует загруженные страницы в оперативной памяти. Если вы переключитесь на другой чат и сразу вернетесь назад, история загрузится **мгновенно из памяти**, без запросов к серверу.
2.  **IndexedDB (Постоянный кэш на диске):** Кэш React Query "живет" только пока открыта вкладка браузера. Чтобы история была доступна даже после перезагрузки страницы, нам нужен постоянный кэш. IndexedDB — это полноценная NoSQL база данных прямо в браузере.

#### Изменения на сервере `ejabberd`

**Никаких изменений не требуется.** Сервер просто отвечает на запросы.

#### Изменения на клиенте React

Мы создадим "умный" хук, который будет работать по следующему алгоритму:

1.  При запросе истории чата хук **сначала** пытается загрузить ее из **IndexedDB**.
2.  Если в IndexedDB есть данные, он **мгновенно** их отдает, чтобы UI отрисовался.
3.  **Параллельно** хук делает запрос к `ejabberd` через MAM, чтобы получить **только самые новые сообщения**, которых еще нет в локальном кэше.
4.  Получив новые сообщения, он обновляет UI и **дописывает** их в IndexedDB.

**Реализация с `dexie.js` (удобная обертка над IndexedDB):**

1.  **Установка:** `npm install dexie`
2.  **Создание локальной БД:**
    ```javascript
    // src/db.js
    import Dexie from 'dexie';

    export const db = new Dexie('messengerDB');
    db.version(1).stores({
      messages: '++internal_id, id, to, from, body, time', // 'id' - это ID сообщения от сервера
    });
    ```
3.  **Модификация хука `useChatHistory`:**
    ```javascript
    // Псевдокод, показывающий логику
    export function usePersistentChatHistory(jid) {
      const client = useXMPPStore((state) => state.client);
      
      // Используем React Query для управления состоянием
      return useQuery({
        queryKey: ['chatHistory', jid],
        queryFn: async () => {
          // 1. Получаем последнее известное сообщение из нашей локальной БД
          const lastLocalMessage = await db.messages.where({ to: jid }).or({ from: jid }).last();
          
          // 2. Делаем запрос к ejabberd за сообщениями, которые пришли ПОСЛЕ него
          const response = await client.searchHistory({
            with: jid,
            paging: {
              after: lastLocalMessage?.id, // Ключевой параметр!
            },
          });

          const newMessages = response.results.map(item => item.message);

          // 3. Сохраняем новые сообщения в IndexedDB
          if (newMessages.length > 0) {
            await db.messages.bulkAdd(newMessages);
          }

          // 4. Загружаем всю историю из IndexedDB и возвращаем ее для отображения
          const allMessages = await db.messages.where({ to: jid }).or({ from: jid }).sortBy('time');
          return allMessages;
        },
        // ...
      });
    }
    ```

**Как это работает для пользователя:**

*   **Первый вход:** Пользователь открывает чат. Приложение загружает последнюю страницу сообщений из `ejabberd` и сохраняет их в IndexedDB.
*   **Переключение чатов:** Пользователь уходит в другой чат и возвращается. Приложение мгновенно загружает историю из IndexedDB. Одновременно оно делает крошечный запрос к `ejabberd`: "дай все сообщения после моего последнего известного ID". `ejabberd` возвращает 0, 1 или 5 новых сообщений. Приложение дописывает их в UI и в IndexedDB.
*   **Перезагрузка страницы:** Приложение снова мгновенно загружает всю известную историю из IndexedDB и запрашивает у `ejabberd` только дельту.

Эта комбинация — **пагинация "вверх" на клиенте** и **кэширование в IndexedDB с запросом "дельты"** — является золотым стандартом для создания быстрых и отзывчивых веб-чатов.