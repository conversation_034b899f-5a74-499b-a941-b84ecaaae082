Это абсолютно верный, продуктово-ориентированный взгляд на архитектуру. Вы совершенно правы: заставлять пользователя придумывать `<EMAIL>` (JID) и работать с ним — это UX из 2005 года. Современный мессенджер должен "просто работать", используя номер телефона или внутренний ID, а все технические детали прятать "под капот".

Ваш стек (`ejabberd` + `FastAPI`) идеально подходит для реализации этой современной модели. Вот как это сделать правильно.

### Центральная идея: `ejabberd` ничего не знает о людях, он знает только об аккаунтах

Мы должны разделить эти две сущности:
*   **"Пользователь" (User):** Это "человек" в вашей системе. У него есть постоянный, неизменный **внутренний ID** (например, `UUID`), профиль, настройки, история покупок и т.д. Эта сущность живет в **PostgreSQL** и управляется вашим **FastAPI-бэкендом**.
*   **"Аккаунт" (Account):** Это техническая учетная запись для подключения к чат-серверу. У нее есть JID (Jabber ID) и пароль. Эта сущность живет в **`ejabberd`**.

**Ключ к успеху — связать их 1-к-1, но сделать главным "Пользователя", а "Аккаунт" — его техническим атрибутом.**

---

### Шаг 1: Используем внутренний ID как `username` в `ejabberd`

Это самый важный и элегантный "трюк". Вместо того чтобы позволять пользователю выбирать себе `username` для XMPP, вы **генерируете его сами** на основе неизменного внутреннего ID.

**Процесс регистрации (новая версия):**

1.  **Клиент -> FastAPI:** Пользователь регистрируется по номеру телефона.
2.  **FastAPI:**
    а. Валидирует номер телефона.
    б. Создает новую запись в таблице `users` в PostgreSQL. **При создании генерируется `id UUID`**, например `123e4567-e89b-12d3-a456-************`.
    в. **Вот магия:** FastAPI формирует `username` для `ejabberd` из этого UUID.
       `xmpp_username = "123e4567-e89b-12d3-a456-************"`
    г. Генерирует сложный, случайный пароль для XMPP-аккаунта (пользователь его никогда не увидит).
    д. Командует `ejabberd` через Admin API: `register("123e4567...", "your_domain.com", "случайный_пароль")`.
    е. Сохраняет этот случайный XMPP-пароль в своей таблице `users`, чтобы иметь возможность генерировать JWT-токены.

**Что мы получили?**
*   Для `ejabberd` существует аккаунт с JID: `123e4567-e89b-12d3-a456-************@your_domain.com`.
*   Пользователь об этом **даже не догадывается**.

---

### Шаг 2: Управление публичными, изменяемыми идентификаторами

Теперь нам нужно место, где будут храниться "красивые" и изменяемые имена.

**Модификация БД (PostgreSQL):**

В таблицу `users` добавляются новые поля:
```sql
ALTER TABLE users
ADD COLUMN public_username VARCHAR(255) UNIQUE, -- @petya1
ADD COLUMN phone_number VARCHAR(255) UNIQUE,    -- +79991234567
ADD COLUMN email VARCHAR(255) UNIQUE;
```
Эти поля могут быть `NULL` и могут меняться. Индекс `UNIQUE` гарантирует, что два пользователя не смогут занять один и тот же `@username`.

**Новые эндпоинты в FastAPI:**

*   **`POST /api/profile/update-username`:** Позволяет пользователю установить или изменить свой `public_username`. FastAPI проверяет, не занят ли он, и обновляет запись в БД.
*   **`POST /api/profile/update-phone`:** Аналогично для телефона (с процедурой верификации).

---

### Шаг 3: Как все это работает вместе (Полный цикл)

**Сценарий 1: Пользователь А хочет найти и написать Пользователю Б**

1.  **Клиент А:** Открывает поиск, вводит `@petya1`.
2.  **Клиент А -> FastAPI:** `GET /api/users/search?q=@petya1`.
3.  **FastAPI:** Ищет в таблице `users` по полю `public_username`. Находит пользователя и видит, что его внутренний `id` — `123e4567...`.
4.  **FastAPI -> Клиент А:** Возвращает профиль Пользователя Б, включая его публичные данные и **самое главное — его внутренний ID**.
5.  **Клиент А:** Получив `id`, он **"на лету" конструирует JID** получателя: `123e4567...@your_domain.com`.
6.  **Клиент А -> `ejabberd`:** Отправляет XMPP-сообщение на этот "технический" JID.
7.  **`ejabberd`:** Получает сообщение, видит JID, находит активную сессию для этого аккаунта и доставляет сообщение Пользователю Б.

**Сценарий 2: Логин пользователя**

1.  **Клиент:** Пользователь вводит свой номер телефона.
2.  **Клиент -> FastAPI:** `POST /api/auth/request-otp` с номером телефона.
3.  **FastAPI:** Генерирует одноразовый пароль (OTP), отправляет его по SMS, сохраняет в Redis.
4.  **Клиент:** Пользователь вводит OTP.
5.  **Клиент -> FastAPI:** `POST /api/auth/login-otp` с телефоном и OTP.
6.  **FastAPI:**
    а. Проверяет OTP.
    б. Находит пользователя в БД по `phone_number`.
    в. Извлекает его **"скрытый" XMPP-username (UUID)** и **"скрытый" XMPP-пароль**.
    г. Возвращает эти "технические" креды клиенту вместе с JWT-токеном для API.
7.  **Клиент:**
    а. Сохраняет JWT-токен для будущих запросов к FastAPI.
    б. Использует полученные технические `xmpp_username` и `xmpp_password` для **подключения к `ejabberd` по WebSocket**.

### Преимущества этого подхода

*   **Идеальный UX:** Пользователь работает только с привычными идентификаторами (телефон, `@username`). Он полностью избавлен от технических деталей XMPP.
*   **Гибкость и стабильность:** Пользователь может сменить телефон, email, публичное имя — его **внутренний ID и JID остаются неизменными**. Все его чаты, контакты и история остаются с ним. Это решает главную проблему старых систем.
*   **Полный контроль:** Вся бизнес-логика (что можно менять, как часто, проверка на уникальность) находится в вашем `FastAPI`-бэкенде, где ей и место.
*   **Эффективность:** `ejabberd` продолжает делать то, что умеет лучше всего — быстро маршрутизировать сообщения по простым и неизменным идентификаторам (UUID), не отвлекаясь на сложную логику поиска или алиасов.

Эта архитектура — золотой стандарт для современных мессенджеров. Вы берете всю мощь и надежность `ejabberd`, но "оборачиваете" ее в современный, дружелюбный к пользователю и гибкий API на `FastAPI`.