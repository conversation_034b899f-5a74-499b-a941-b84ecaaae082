Вы выбрали самую гибкую и функциональную модель шифрования, которая сочетает безопасность с удобством облачных функций. Реализовать ее в вашем стеке абсолютно возможно. Это потребует четкого разделения криптографических операций (на клиенте) и управления ключами (на бэкенде).

`ejabberd` в этой схеме превращается в **"бронированный почтовый фургон"**. Он ничего не знает о содержимом, он просто надежно и быстро перевозит запечатанные, зашифрованные "контейнеры" из точки А в точку Б и складирует их в архиве (MAM).

Вот пошаговый план, где и что нужно добавить и модифицировать в каждом компоненте вашей системы.

---

### Шаг 1: Модификации на бэкенде (`FastAPI` и `PostgreSQL`)

Ваш бэкенд становится "банком" — хранилищем ключей.

#### 1. Изменение схемы БД (PostgreSQL)

В вашей таблице `users` добавьте два новых поля (не забывайте сделать миграцию alembic):

```sql
ALTER TABLE users
ADD COLUMN public_key TEXT,
ADD COLUMN encrypted_private_key TEXT;
```
*   `public_key`: Здесь будет храниться публичный ключ пользователя в текстовом формате (например, Base64). Он доступен всем.
*   `encrypted_private_key`: Здесь будет храниться приватный ключ пользователя, **зашифрованный его же паролем**. Сервер не сможет его прочитать без участия пользователя.

#### 2. Новые эндпоинты в FastAPI

Вам понадобятся новые эндпоинты для управления ключами:

*   **`POST /api/auth/keys` (после регистрации):**
    *   **Задача:** Сохранить ключи, сгенерированные клиентом.
    *   **Вход:** `{ "public_key": "...", "encrypted_private_key": "..." }`.
    *   **Действие:** Сохраняет эти ключи в соответствующие поля в таблице `users` для текущего авторизованного пользователя.

*   **`GET /api/users/{username}/key`:**
    *   **Задача:** Предоставить публичный ключ другого пользователя.
    *   **Вход:** `username` в пути.
    *   **Действие:** Находит пользователя в БД и возвращает его `public_key`. Это нужно, чтобы зашифровать для него сообщение.

*   **`GET /api/auth/me/key`:**
    *   **Задача:** Отдать текущему пользователю его собственный зашифрованный приватный ключ.
    *   **Действие:** Возвращает `encrypted_private_key` из БД для авторизованного пользователя. Это нужно клиенту после логина, чтобы расшифровать свой ключ паролем.

---

### Шаг 2: Модификации на клиенте (`React`)

Клиент становится "криптографической машиной". Вся магия шифрования и расшифровки происходит здесь.

#### 1. Необходимые библиотеки

Вам понадобятся криптографические библиотеки для браузера.
*   **`libsodium-wrappers`**: Отличный выбор для всех операций. Высокоуровневая, быстрая и безопасная.
*   **`argon2-browser`**: Для реализации растяжения ключа из пароля (Argon2).

#### 2. Процесс регистрации (добавление к существующему)

1.  Пользователь вводит логин и пароль.
2.  **Дополнительные шаги на клиенте:**
    а. **Генерация ключей:** `const keyPair = await sodium.crypto_box_keypair();`
    б. **Растяжение пароля:** `const derivedKey = await argon2.hash({ pass: password, salt: 'somesalt' });` // Соль можно генерировать
    в. **Шифрование приватного ключа:** `const encryptedPrivateKey = sodium.crypto_secretbox_easy(keyPair.privateKey, nonce, derivedKey.hash);`
3.  Клиент отправляет на `POST /api/auth/register` логин и пароль.
4.  После успешной регистрации клиент делает второй запрос на `POST /api/auth/keys`, отправляя `keyPair.publicKey` и `encryptedPrivateKey`.

#### 3. Процесс логина

1.  Клиент успешно аутентифицируется в `ejabberd` по WebSocket.
2.  Клиент делает запрос на `GET /api/auth/me/key`, чтобы получить свой `encrypted_private_key`.
3.  Клиент запрашивает у пользователя его пароль.
4.  Используя пароль и тот же `argon2`, клиент **расшифровывает свой приватный ключ** и сохраняет его **только в оперативной памяти** (например, в сторе Zustand). **Никогда не храните расшифрованный приватный ключ в `localStorage`!**

#### 4. Процесс отправки сообщения

1.  Пользователь А пишет сообщение Пользователю Б.
2.  Клиент А делает запрос на `GET /api/users/user_b/key` (этот запрос можно и нужно кэшировать в React Query).
3.  Получив публичный ключ Пользователя Б, клиент А выполняет криптографические операции:
    а. Генерирует случайный **симметричный ключ сообщения (Message Key)**.
    б. Шифрует текст сообщения этим `Message Key` (`sodium.crypto_secretbox_easy`).
    в. Шифрует `Message Key` **публичным ключом Пользователя Б** (`sodium.crypto_box_easy`).
4.  Клиент А формирует кастомную XMPP-станзу и отправляет ее в `ejabberd`.

---

### Шаг 3: Модификации в транспорте (`ejabberd`)

Как мы и говорили, `ejabberd` почти ничего не замечает.

#### 1. Новый формат сообщения

Вместо `<body>Привет</body>` вы теперь отправляете структурированный зашифрованный "контейнер".

```xml
<message to='user_b@localhost' type='chat'>
  <!-- Это поле для ejabberd и MAM, оно должно быть.
       Можно оставить пустым или написать [Зашифрованное сообщение] -->
  <body>...</body> 
  
  <!-- Наш кастомный зашифрованный контейнер -->
  <encrypted xmlns='urn:myapp:cse:1'>
    <key recipient_jid='user_b@localhost'>
      A1B2C3D4E5... (это Message Key, зашифрованный ключом user_b, в Base64)
    </key>
    <payload>
      F6G7H8I9J0... (это текст сообщения, зашифрованный Message Key, в Base64)
    </payload>
  </encrypted>
</message>
```

#### 2. Конфигурация `ejabberd`

**Никаких изменений не требуется!** `mod_mam` увидит это сообщение, включая ваш кастомный тег `<encrypted>`, и целиком сохранит его в базу данных как есть. Он выполнит свою работу идеально.

---

### Реализация поиска по истории на сервере (опционально)

Это та самая "особенность", ради которой все затевалось.

1.  **Новый эндпоинт:** `POST /api/search/history`
2.  **Процесс на клиенте:**
    *   Клиент отправляет поисковый запрос и свой **пароль** (или ключ, полученный из пароля) на этот эндпоинт. **Это и есть компромисс в безопасности, который нужно осознавать.**
3.  **Процесс на FastAPI:**
    *   Бэкенд получает запрос.
    *   Используя пароль, он расшифровывает `encrypted_private_key` пользователя, получая его приватный ключ **в памяти, на время выполнения запроса**.
    *   Бэкенд делает `SELECT` в таблицу `archive` (где `mod_mam` хранит историю), получая все зашифрованные "контейнеры" пользователя.
    *   В цикле, в памяти, он расшифровывает каждое сообщение: сначала `Message Key` своим приватным ключом, затем `payload` полученным `Message Key`.
    *   Проверяет, соответствует ли расшифрованный текст поисковому запросу.
    *   Собирает результаты и отдает их клиенту.
    *   **Сразу после завершения запроса приватный ключ и расшифрованные сообщения уничтожаются из памяти.**

Эта архитектура в точности повторяет модель "облачных чатов" Telegram, идеально вписываясь в ваш технологический стек.