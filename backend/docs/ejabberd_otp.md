Вы совершенно правы, переходя к passwordless-аутентификации. Это современный, безопасный и очень удобный для пользователя подход, который идеально вписывается в вашу архитектуру, так как пользователь и так не должен знать свой "технический" XMPP-пароль.

Реализация "magic link" или OTP-входа, который объединяет регистрацию и логин, — это элегантное решение. Давайте пошагово разберем, что для этого нужно.

---

### Шаг 1: Изменения в инфраструктуре

Вам понадобится надежный способ отправлять транзакционные email-сообщения.

1.  **SMTP-сервис (Email Delivery Service):**
    *   **Для разработки:** Вы можете использовать встроенную в Python библиотеку `smtplib` с вашим Gmail-аккаунтом (не рекомендуется для продакшена из-за лимитов и безопасности) или локальный SMTP-сервер для отладки, например `aipsmtpd`.
    *   **Для продакшена (обязательно):** Используйте специализированный сервис. Это критически важно для **доставляемости** писем (чтобы они не попадали в спам).
        *   **SendGrid**
        *   **Mailgun**
        *   **Amazon SES**
    *   **Интеграция:** У всех этих сервисов есть удобные Python-библиотеки, которые вы будете использовать в своем FastAPI-бэкенде. Вы просто добавляете их API-ключ в свои переменные окружения.

2.  **Redis (Обязательный компонент):**
    Вам нужно место для временного хранения OTP-кодов. Redis для этого идеален. Вы уже планировали его использовать для кэширования, теперь у него появится еще одна важная роль.

---

### Шаг 2: Изменения на бэкенде (FastAPI)

Вся логика OTP-аутентификации будет жить здесь.

#### 1. Изменения в БД (PostgreSQL)

В вашей таблице `users` поле `email` теперь должно быть **уникальным и обязательным**:
```sql
ALTER TABLE users ALTER COLUMN email SET NOT NULL;
ALTER TABLE users ADD CONSTRAINT users_email_unique UNIQUE (email);
```

#### 2. Новые эндпоинты API

Вам понадобятся два новых эндпоинта, которые заменят стандартные `register` и `login`.

*   **`POST /api/auth/otp/request`**
    *   **Задача:** Сгенерировать OTP и отправить его на email.
    *   **Входной JSON:** `{"email": "<EMAIL>"}`
    *   **Логика:**
        1.  Валидация email.
        2.  Генерация безопасного 6-значного кода (например, `secrets.token_hex(3)`).
        3.  Сохранение **хэша** этого кода в **Redis**. **Ключ:** `otp:<EMAIL>`, **Значение:** `{ "hash": "...", "attempts": 0 }`.
        4.  **Установка TTL (Time-To-Live) для ключа в Redis!** Например, 5-10 минут. Это критически важно для безопасности.
        5.  Вызов вашего SMTP-сервиса для отправки письма с кодом на указанный email.
        6.  Возврат ответа `{"success": true, "message": "OTP sent"}`.

*   **`POST /api/auth/otp/verify`**
    *   **Задача:** Проверить OTP и выполнить логин или регистрацию.
    *   **Входной JSON:** `{"email": "<EMAIL>", "otp": "123456"}`
    *   **Логика (самая важная часть):**
        1.  Поиск ключа `otp:<EMAIL>` в Redis. Если не найден — ошибка "OTP устарел или не запрашивался".
        2.  Проверка количества попыток. Если превышен лимит (например, 5 попыток), удалить ключ и выдать ошибку.
        3.  Сравнение присланного `otp` с хэшем из Redis. Если не совпадает — инкремент счетчика попыток, ошибка.
        4.  **Если OTP верный — НЕМЕДЛЕННО удалить ключ из Redis, чтобы предотвратить повторное использование.**
        5.  **Далее — "магический" роутинг:**
            *   **Поиск пользователя в PostgreSQL:** `SELECT * FROM users WHERE email = :email`.
            *   **Сценарий 1: Пользователь найден (Логин)**
                *   Вы уже знаете его `id (UUID)`, который является его `xmpp_username`, и его "скрытый" `xmpp_password`.
                *   Генерируете JWT-токен для FastAPI.
                *   Возвращаете клиенту: `{"jwt_token": "...", "xmpp_username": "uuid...", "xmpp_password": "..."}`.
            *   **Сценарий 2: Пользователь не найден (Регистрация)**
                *   Выполняете всю логику регистрации из предыдущего ответа:
                    а. Создаете новую запись в `users` с этим email, генерируете новый `id (UUID)`.
                    б. Формируете `xmpp_username` из этого UUID.
                    в. Генерируете случайный, сложный `xmpp_password`.
                    г. Даете команду `ejabberd` через Admin API создать этот аккаунт.
                    д. Генерируете JWT-токен.
                    е. Возвращаете клиенту те же данные: `{"jwt_token": "...", "xmpp_username": "uuid...", "xmpp_password": "..."}`.

---

### Шаг 3: Изменения на клиенте (React)

UX становится проще и состоит из двух шагов.

1.  **Новый UI-флоу:**
    *   **Экран 1:** Одно поле для ввода email и кнопка "Продолжить".
    *   **Экран 2:** Поле для ввода 6-значного OTP-кода, кнопка "Войти" и ссылка "Отправить код еще раз".

2.  **Работа с API и состоянием:**
    *   **Запрос OTP:** Используем `useMutation` из **React Query** для вызова `POST /api/auth/otp/request`. При успехе показываем второй экран.
    *   **Верификация OTP:** Используем другой `useMutation` для вызова `POST /api/auth/otp/verify`.
    *   **При успешной верификации:**
        1.  Клиент получает в ответе `jwt_token`, `xmpp_username` и `xmpp_password`.
        2.  Он сохраняет `jwt_token` в безопасное место (например, в память или `localStorage`) и будет использовать его для всех последующих запросов к API FastAPI.
        3.  Он **немедленно** вызывает функцию `connect(xmpp_username, xmpp_password)` из вашего **Zustand** стора.
        4.  Zustand инициирует WebSocket-подключение к `ejabberd`.
        5.  После успешного подключения пользователь видит свой список чатов.

### Полный цикл "Passwordless" входа

1.  **Пользователь** открывает приложение, вводит `<EMAIL>`, нажимает "Продолжить".
2.  **React** вызывает мутацию, отправляя `{"email": "<EMAIL>"}` на `POST /api/auth/otp/request`.
3.  **FastAPI** генерирует код "123456", сохраняет его хэш в **Redis** с TTL=5m и через **SendGrid** отправляет письмо.
4.  **Пользователь** получает письмо, вводит "123456" в приложении.
5.  **React** вызывает мутацию, отправляя `{"email": "<EMAIL>", "otp": "123456"}` на `POST /api/auth/otp/verify`.
6.  **FastAPI** проверяет код в Redis, удаляет его, ищет пользователя в **PostgreSQL**.
7.  **FastAPI** (допустим, это новый юзер) создает пользователя в **PostgreSQL**, командует **`ejabberd`** создать XMPP-аккаунт, генерирует JWT.
8.  **FastAPI** возвращает `jwt_token` и XMPP-креды.
9.  **React** сохраняет JWT и передает XMPP-креды в **Zustand**.
10. **Zustand** инициирует WebSocket-подключение к **`ejabberd`**.
11. **Пользователь** залогинен и видит главный экран мессенджера.

Этот подход полностью соответствует современным практикам, он безопасен, удобен для пользователя и идеально вписывается в вашу существующую архитектуру.