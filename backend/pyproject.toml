[project]
name = ""
version = "0.0.1"
dependencies = [
    "aiohttp>=3.9.1",
    "alembic>=1.12.1",
    "asyncio-mqtt>=0.16.1",
    "asyncpg>=0.29.0",
    "click>=8.1.7",
    "fastapi>=0.104.1",
    "greenlet>=3.2.4",
    "livekit-api>=1.0.5",
    "minio>=7.2.0",
    "passlib[bcrypt]>=1.7.4",
    "psycopg2-binary>=2.9.10",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "python-dotenv>=1.0.0",
    "python-jose[cryptography]>=3.3.0",
    "python-multipart>=0.0.6",
    "redis>=5.0.1",
    "rich>=13.7.0",
    "sqlalchemy>=2.0.23",
    "uvicorn[standard]>=0.24.0",
    "websockets>=15.0.1",
]

[tool.uv]
package = false
