"""
🌉 XMPP WebSocket Bridge - Мост между WebSocket клиентами и eJabberd

Обеспечивает двустороннюю связь:
- WebSocket клиенты ↔ FastAPI ↔ eJabberd XMPP
- Real-time уведомления о сообщениях
- Интеграция с eJabberd WebSocket endpoint
"""

import asyncio
import logging
import xml.etree.ElementTree as ET
from typing import Dict
from datetime import datetime

import websockets
from websockets.exceptions import ConnectionClosed

from services.websocket_client import websocket_manager

logger = logging.getLogger(__name__)


class XMPPWebSocketBridge:
    """Мост между WebSocket клиентами и eJabberd XMPP"""

    def __init__(self):
        self.ejabberd_ws_url = "ws://localhost:5443/websocket"
        self.connected_users: Dict[str, websockets.WebSocketServerProtocol] = {}
        self.user_sessions: Dict[str, str] = {}  # user_jid -> session_id
        self.running = False

    async def start_bridge(self):
        """Запуск моста"""
        self.running = True
        logger.info("🌉 Starting XMPP WebSocket Bridge")

        # Запускаем мониторинг подключений
        await self.monitor_connections()

    async def stop_bridge(self):
        """Остановка моста"""
        self.running = False
        logger.info("🌉 Stopping XMPP WebSocket Bridge")

        # Закрываем все соединения
        for ws in self.connected_users.values():
            if not ws.closed:
                await ws.close()

        self.connected_users.clear()
        self.user_sessions.clear()

    async def connect_user_to_ejabberd(self, user_jid: str, password: str) -> bool:
        """Подключение пользователя к eJabberd через WebSocket"""
        try:
            # Подключаемся к eJabberd WebSocket
            ws = await websockets.connect(self.ejabberd_ws_url)

            # Отправляем XMPP stream header
            stream_header = """<?xml version='1.0'?>
<stream:stream to='localhost' xmlns='jabber:client' 
               xmlns:stream='http://etherx.jabber.org/streams' version='1.0'>"""

            await ws.send(stream_header)

            # Ждем ответ от сервера
            response = await ws.recv()
            logger.info(f"📨 eJabberd response: {response}")

            # Выполняем аутентификацию
            auth_success = await self.authenticate_xmpp(ws, user_jid, password)

            if auth_success:
                self.connected_users[user_jid] = ws
                session_id = f"session_{user_jid}_{datetime.now().timestamp()}"
                self.user_sessions[user_jid] = session_id

                # Запускаем прослушивание сообщений от eJabberd
                asyncio.create_task(self.listen_ejabberd_messages(user_jid, ws))

                logger.info(f"✅ User {user_jid} connected to eJabberd via WebSocket")
                return True
            else:
                await ws.close()
                logger.error(f"❌ Authentication failed for {user_jid}")
                return False

        except Exception as e:
            logger.error(f"❌ Failed to connect {user_jid} to eJabberd: {e}")
            return False

    async def authenticate_xmpp(
        self, ws: websockets.WebSocketServerProtocol, user_jid: str, password: str
    ) -> bool:
        """XMPP аутентификация"""
        try:
            username = user_jid.split("@")[0]

            # SASL PLAIN аутентификация
            import base64

            auth_string = f"\0{username}\0{password}"
            auth_b64 = base64.b64encode(auth_string.encode()).decode()

            auth_stanza = f"""<auth xmlns='urn:ietf:params:xml:ns:xmpp-sasl' mechanism='PLAIN'>{auth_b64}</auth>"""

            await ws.send(auth_stanza)

            # Ждем результат аутентификации
            response = await ws.recv()

            if "<success" in response:
                # Аутентификация успешна, отправляем новый stream
                stream_restart = """<?xml version='1.0'?>
<stream:stream to='localhost' xmlns='jabber:client' 
               xmlns:stream='http://etherx.jabber.org/streams' version='1.0'>"""

                await ws.send(stream_restart)
                await ws.recv()  # Получаем stream features

                # Привязываем ресурс
                bind_stanza = """<iq type='set' id='bind_1'>
<bind xmlns='urn:ietf:params:xml:ns:xmpp-bind'>
<resource>websocket</resource>
</bind>
</iq>"""

                await ws.send(bind_stanza)
                bind_response = await ws.recv()

                if 'type="result"' in bind_response:
                    # Устанавливаем присутствие
                    presence = "<presence/>"
                    await ws.send(presence)

                    logger.info(f"✅ XMPP authentication successful for {user_jid}")
                    return True

            logger.error(f"❌ XMPP authentication failed for {user_jid}: {response}")
            return False

        except Exception as e:
            logger.error(f"❌ XMPP authentication error for {user_jid}: {e}")
            return False

    async def listen_ejabberd_messages(
        self, user_jid: str, ws: websockets.WebSocketServerProtocol
    ):
        """Прослушивание сообщений от eJabberd"""
        try:
            while self.running and not ws.closed:
                try:
                    message = await asyncio.wait_for(ws.recv(), timeout=1.0)
                    await self.process_xmpp_message(user_jid, message)
                except asyncio.TimeoutError:
                    continue
                except ConnectionClosed:
                    break

        except Exception as e:
            logger.error(f"❌ Error listening to eJabberd messages for {user_jid}: {e}")
        finally:
            # Очищаем соединение
            if user_jid in self.connected_users:
                del self.connected_users[user_jid]
            if user_jid in self.user_sessions:
                del self.user_sessions[user_jid]

            logger.info(f"🔌 eJabberd connection closed for {user_jid}")

    async def process_xmpp_message(self, user_jid: str, xmpp_data: str):
        """Обработка XMPP сообщения от eJabberd"""
        try:
            # Парсим XML
            if not xmpp_data.strip() or xmpp_data.startswith("<?xml"):
                return

            # Простая обработка сообщений
            if "<message" in xmpp_data and "type=" in xmpp_data:
                await self.handle_incoming_message(user_jid, xmpp_data)
            elif "<presence" in xmpp_data:
                await self.handle_presence_update(user_jid, xmpp_data)
            elif "<iq" in xmpp_data:
                await self.handle_iq_stanza(user_jid, xmpp_data)

        except Exception as e:
            logger.error(f"❌ Error processing XMPP message: {e}")

    async def handle_incoming_message(self, user_jid: str, xmpp_data: str):
        """Обработка входящего сообщения"""
        try:
            # Извлекаем информацию из XMPP stanza
            root = ET.fromstring(f"<root>{xmpp_data}</root>")
            message_elem = root.find(".//message")

            if message_elem is not None:
                from_jid = message_elem.get("from", "")
                to_jid = message_elem.get("to", "")
                msg_type = message_elem.get("type", "chat")

                body_elem = message_elem.find("body")
                body = body_elem.text if body_elem is not None else ""

                if body:
                    # Отправляем уведомление через WebSocket менеджер
                    await websocket_manager.notify_new_message(
                        from_jid=from_jid,
                        to_jid=to_jid,
                        message_body=body,
                        message_type=msg_type,
                    )

                    logger.info(
                        f"📨 Relayed message from {from_jid} to {to_jid}: {body[:50]}..."
                    )

        except Exception as e:
            logger.error(f"❌ Error handling incoming message: {e}")

    async def handle_presence_update(self, user_jid: str, xmpp_data: str):
        """Обработка обновления присутствия"""
        try:
            # Извлекаем информацию о присутствии
            root = ET.fromstring(f"<root>{xmpp_data}</root>")
            presence_elem = root.find(".//presence")

            if presence_elem is not None:
                from_jid = presence_elem.get("from", "")
                presence_type = presence_elem.get("type", "available")

                status = "online" if presence_type == "available" else "offline"

                # Уведомляем WebSocket клиентов об изменении статуса
                await websocket_manager.broadcast_user_status(from_jid, status)

                logger.info(f"👤 Presence update: {from_jid} is {status}")

        except Exception as e:
            logger.error(f"❌ Error handling presence update: {e}")

    async def handle_iq_stanza(self, user_jid: str, xmpp_data: str):
        """Обработка IQ stanza"""
        # Пока просто логируем
        logger.debug(f"📋 IQ stanza for {user_jid}: {xmpp_data[:100]}...")

    async def send_message_to_ejabberd(
        self, from_jid: str, to_jid: str, body: str, msg_type: str = "chat"
    ):
        """Отправка сообщения в eJabberd"""
        if from_jid not in self.connected_users:
            logger.error(f"❌ User {from_jid} not connected to eJabberd")
            return False

        try:
            ws = self.connected_users[from_jid]

            # Формируем XMPP message stanza
            message_id = f"msg_{datetime.now().timestamp()}"
            message_stanza = f"""<message to='{to_jid}' type='{msg_type}' id='{message_id}'>
<body>{body}</body>
</message>"""

            await ws.send(message_stanza)
            logger.info(f"📤 Sent message from {from_jid} to {to_jid} via eJabberd")
            return True

        except Exception as e:
            logger.error(f"❌ Error sending message to eJabberd: {e}")
            return False

    async def monitor_connections(self):
        """Мониторинг соединений"""
        while self.running:
            try:
                # Проверяем активные соединения
                dead_connections = []
                for user_jid, ws in self.connected_users.items():
                    if ws.closed:
                        dead_connections.append(user_jid)

                # Удаляем мертвые соединения
                for user_jid in dead_connections:
                    del self.connected_users[user_jid]
                    if user_jid in self.user_sessions:
                        del self.user_sessions[user_jid]
                    logger.info(f"🧹 Cleaned up dead connection for {user_jid}")

                await asyncio.sleep(30)  # Проверяем каждые 30 секунд

            except Exception as e:
                logger.error(f"❌ Error in connection monitoring: {e}")
                await asyncio.sleep(5)


# Глобальный экземпляр моста
xmpp_bridge = XMPPWebSocketBridge()
