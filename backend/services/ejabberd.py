"""
Сервис для работы с eJabberd API
"""

from typing import Dict, Any, Optional
import aiohttp
import asyncio
from fastapi import HTTPException

from core.config import settings
from .ejabberd_db import EjabberdDBService


class EjabberdService:
    """Сервис для взаимодействия с eJabberd"""

    def __init__(self):
        self.api_url = settings.ejabberd_api_url
        self.admin_user = settings.ejabberd_admin_user
        self.admin_password = settings.ejabberd_admin_password
        self.domain = settings.ejabberd_domain
        self.db_service = EjabberdDBService()

    async def call_ejabberdctl(self, command: str, args: list = None) -> Dict[str, Any]:
        """Вызов ejabberdctl через docker exec"""
        try:
            cmd = [
                "docker",
                "exec",
                "ejabberd_fastapi1-ejabberd-1",  # Исправленное имя контейнера
                "ejabberdctl",
                command,
            ]
            if args:
                cmd.extend(args)

            print(f"🔄 Executing ejabberdctl command: {' '.join(cmd)}")

            process = await asyncio.create_subprocess_exec(
                *cmd, stdout=asyncio.subprocess.PIPE, stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            stdout_text = stdout.decode().strip()
            stderr_text = stderr.decode().strip()

            print(f"📊 ejabberdctl result - return code: {process.returncode}")
            print(f"📊 ejabberdctl stdout: {stdout_text}")
            if stderr_text:
                print(f"📊 ejabberdctl stderr: {stderr_text}")

            if process.returncode == 0:
                return {"success": True, "output": stdout_text, "error": stderr_text}
            else:
                return {"success": False, "error": stderr_text, "output": stdout_text}
        except Exception as e:
            error_msg = f"ejabberdctl execution error: {str(e)}"
            print(f"❌ {error_msg}")
            return {"success": False, "error": error_msg}

    async def call_api(
        self, command: str, data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Вызов API eJabberd"""
        url = f"{self.api_url}/{command}"
        auth = aiohttp.BasicAuth(self.admin_user, self.admin_password)

        print(f"🔄 Calling eJabberd API: {url}")
        print(f"🔄 Auth: {self.admin_user}:***")
        print(f"🔄 Data: {data}")

        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=data or {}, auth=auth) as resp:
                print(f"📊 eJabberd API response status: {resp.status}")
                if resp.status == 200:
                    result = await resp.json()
                    print(f"📊 eJabberd API response: {result}")
                    return result
                else:
                    text = await resp.text()
                    error_msg = f"eJabberd API error (status {resp.status}): {text}"
                    print(f"❌ {error_msg}")
                    raise HTTPException(status_code=resp.status, detail=error_msg)

    async def register_user(self, username: str, password: str) -> bool:
        """Регистрация пользователя в eJabberd"""
        try:
            print(f"🔄 Registering user in eJabberd: {username}@{self.domain}")
            # Регистрируем пользователя через ejabberdctl
            result = await self.call_ejabberdctl(
                "register", [username, self.domain, password]
            )

            success = result.get("success", False)
            if success:
                print(f"✅ User registered in eJabberd: {username}@{self.domain}")
            else:
                error = result.get("error", "Unknown error")
                print(
                    f"❌ Failed to register user in eJabberd: {username}@{self.domain}, error: {error}"
                )

            return success
        except Exception as e:
            error_msg = (
                f"Error registering user in eJabberd: {username}@{self.domain}: {e}"
            )
            print(f"❌ {error_msg}")
            return False

    async def check_password(self, username: str, password: str) -> bool:
        """Проверка пароля пользователя"""
        try:
            print(f"🔄 Checking password for user: {username}@{self.domain}")

            # Используем проверку через базу данных
            return await self.db_service.check_password(username, password)

        except Exception as e:
            error_msg = f"Error checking password for {username}@{self.domain}: {e}"
            print(f"❌ {error_msg}")
            return False

    async def send_message(
        self, from_jid: str, to_jid: str, body: str, message_type: str = "chat"
    ) -> bool:
        """Отправка сообщения"""
        # Сохраняем сообщение в архив через DB сервис
        success = await self.db_service.send_message_to_archive(
            from_jid, to_jid, body, message_type
        )
        if success:
            print(f"Message from {from_jid} to {to_jid}: {body}")
        return success

    async def get_message_history(
        self, user_jid: str, with_jid: str, limit: int = 50, offset: int = 0
    ) -> Dict[str, Any]:
        """Получение истории сообщений"""
        # Получаем историю из базы данных через DB сервис
        return await self.db_service.get_message_history(
            user_jid, with_jid, limit, offset
        )

    async def create_room(self, room_name: str, title: str) -> bool:
        """Создание MUC комнаты"""
        # Пока что просто логируем создание комнаты и возвращаем True
        print(f"Creating room: {room_name} with title: {title}")
        return True
