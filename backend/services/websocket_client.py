"""
🔌 WebSocket Client Service - Real-time связь с фронтендом

Обеспечивает real-time связь между бекендом и фронтенд клиентами:
- WebSocket соединения с клиентами
- Уведомления о новых сообщениях
- Статусы пользователей в реальном времени
- Интеграция с eJabberd WebSocket
"""

import json
import logging
from typing import Dict, Set, Optional
from datetime import datetime

from fastapi import WebSocket, WebSocketDisconnect
from sqlalchemy import select

from core.security import decode_access_token
from database.connection import AsyncSessionLocal
from models.user import UserExtended
from services.redis import RedisService

# Настройка логирования
logger = logging.getLogger(__name__)


class WebSocketManager:
    """Менеджер WebSocket соединений"""

    def __init__(self):
        # Активные соединения: user_jid -> WebSocket
        self.active_connections: Dict[str, WebSocket] = {}
        # Комнаты: room_id -> Set[user_jid]
        self.rooms: Dict[str, Set[str]] = {}
        # Redis для синхронизации
        self.redis_service = RedisService()

    async def connect(self, websocket: WebSocket, user_jid: str):
        """Подключение нового клиента"""
        await websocket.accept()
        self.active_connections[user_jid] = websocket

        # Обновляем статус в Redis
        await self.redis_service.set_user_status(user_jid, "online")

        logger.info(f"✅ WebSocket connected: {user_jid}")

        # Уведомляем других пользователей о подключении
        await self.broadcast_user_status(user_jid, "online")

    async def disconnect(self, user_jid: str):
        """Отключение клиента"""
        if user_jid in self.active_connections:
            del self.active_connections[user_jid]

        # Обновляем статус в Redis
        await self.redis_service.set_user_status(user_jid, "offline")

        # Удаляем из всех комнат
        for room_id in list(self.rooms.keys()):
            if user_jid in self.rooms[room_id]:
                self.rooms[room_id].discard(user_jid)
                if not self.rooms[room_id]:
                    del self.rooms[room_id]

        logger.info(f"❌ WebSocket disconnected: {user_jid}")

        # Уведомляем других пользователей об отключении
        await self.broadcast_user_status(user_jid, "offline")

    async def send_personal_message(self, user_jid: str, message: dict):
        """Отправка личного сообщения пользователю"""
        if user_jid in self.active_connections:
            try:
                websocket = self.active_connections[user_jid]
                await websocket.send_text(json.dumps(message))
                logger.info(
                    f"📤 Message sent to {user_jid}: {message.get('type', 'unknown')}"
                )
                return True
            except Exception as e:
                logger.error(f"❌ Failed to send message to {user_jid}: {e}")
                # Удаляем неактивное соединение
                await self.disconnect(user_jid)
                return False
        return False

    async def broadcast_to_room(
        self, room_id: str, message: dict, exclude_user: str = None
    ):
        """Отправка сообщения всем участникам комнаты"""
        if room_id not in self.rooms:
            return

        sent_count = 0
        for user_jid in self.rooms[room_id].copy():
            if exclude_user and user_jid == exclude_user:
                continue

            if await self.send_personal_message(user_jid, message):
                sent_count += 1

        logger.info(f"📢 Broadcast to room {room_id}: {sent_count} recipients")

    async def broadcast_user_status(self, user_jid: str, status: str):
        """Уведомление о смене статуса пользователя"""
        message = {
            "type": "user_status",
            "user_jid": user_jid,
            "status": status,
            "timestamp": datetime.utcnow().isoformat(),
        }

        # Отправляем всем подключенным пользователям
        for connected_user in list(self.active_connections.keys()):
            if connected_user != user_jid:
                await self.send_personal_message(connected_user, message)

    async def join_room(self, user_jid: str, room_id: str):
        """Присоединение к комнате"""
        if room_id not in self.rooms:
            self.rooms[room_id] = set()

        self.rooms[room_id].add(user_jid)
        logger.info(f"🏠 User {user_jid} joined room {room_id}")

    async def leave_room(self, user_jid: str, room_id: str):
        """Выход из комнаты"""
        if room_id in self.rooms and user_jid in self.rooms[room_id]:
            self.rooms[room_id].discard(user_jid)
            if not self.rooms[room_id]:
                del self.rooms[room_id]
            logger.info(f"🚪 User {user_jid} left room {room_id}")

    async def notify_new_message(
        self,
        from_jid: str,
        to_jid: str,
        message_body: str,
        message_type: str = "chat",
        room_id: str = None,
    ):
        """Уведомление о новом сообщении"""
        message = {
            "type": "new_message",
            "from_jid": from_jid,
            "to_jid": to_jid,
            "body": message_body,
            "message_type": message_type,
            "room_id": room_id,
            "timestamp": datetime.utcnow().isoformat(),
            "message_id": f"msg_{datetime.utcnow().timestamp()}",
        }

        if message_type == "chat":
            # Личное сообщение
            await self.send_personal_message(to_jid, message)
            logger.info(f"📨 Notified {to_jid} about message from {from_jid}")
        elif message_type == "groupchat" and room_id:
            # Групповое сообщение
            await self.broadcast_to_room(room_id, message, exclude_user=from_jid)
            logger.info(f"📢 Broadcasted group message to room {room_id}")

    async def notify_typing(self, from_jid: str, to_jid: str, is_typing: bool):
        """Уведомление о печати"""
        message = {
            "type": "typing",
            "from_jid": from_jid,
            "is_typing": is_typing,
            "timestamp": datetime.utcnow().isoformat(),
        }

        await self.send_personal_message(to_jid, message)

    async def get_online_users(self) -> list:
        """Получение списка онлайн пользователей"""
        return list(self.active_connections.keys())

    async def get_room_participants(self, room_id: str) -> list:
        """Получение участников комнаты"""
        return list(self.rooms.get(room_id, set()))


# Глобальный экземпляр менеджера
websocket_manager = WebSocketManager()


async def authenticate_websocket(token: str) -> Optional[str]:
    """Аутентификация WebSocket соединения"""
    try:
        payload = decode_access_token(token)
        user_jid = payload.get("sub")

        if not user_jid:
            return None

        # Проверяем что пользователь существует в базе
        async with AsyncSessionLocal() as db:
            username = user_jid.split("@")[0]
            result = await db.execute(
                select(UserExtended).where(UserExtended.username == username)
            )
            user = result.scalar_one_or_none()

            if user:
                return user_jid

    except Exception as e:
        logger.error(f"❌ WebSocket authentication failed: {e}")

    return None


async def handle_websocket_message(user_jid: str, message_data: dict):
    """Обработка сообщения от WebSocket клиента"""
    message_type = message_data.get("type")

    if message_type == "ping":
        # Ответ на ping
        return {"type": "pong", "timestamp": datetime.utcnow().isoformat()}

    elif message_type == "join_room":
        # Присоединение к комнате
        room_id = message_data.get("room_id")
        if room_id:
            await websocket_manager.join_room(user_jid, room_id)
            return {"type": "room_joined", "room_id": room_id}

    elif message_type == "leave_room":
        # Выход из комнаты
        room_id = message_data.get("room_id")
        if room_id:
            await websocket_manager.leave_room(user_jid, room_id)
            return {"type": "room_left", "room_id": room_id}

    elif message_type == "typing":
        # Индикатор печати
        to_jid = message_data.get("to_jid")
        is_typing = message_data.get("is_typing", False)
        if to_jid:
            await websocket_manager.notify_typing(user_jid, to_jid, is_typing)
            return {"type": "typing_sent"}

    elif message_type == "get_online_users":
        # Запрос онлайн пользователей
        online_users = await websocket_manager.get_online_users()
        return {"type": "online_users", "users": online_users}

    elif message_type == "incoming_call":
        # Обработка уведомления о входящем звонке (ретрансляция)
        to_jid = message_data.get("to_jid")
        if to_jid:
            # Ретранслируем сообщение получателю
            await websocket_manager.send_personal_message(to_jid, message_data)
            return {"type": "call_notification_sent"}

    elif message_type == "call_answer":
        # Обработка ответа на звонок
        call_id = message_data.get("call_id")
        if call_id:
            # Уведомляем всех участников звонка
            # TODO: Получить список участников из базы данных
            return {"type": "call_answer_sent"}

    elif message_type == "call_reject":
        # Обработка отклонения звонка
        call_id = message_data.get("call_id")
        if call_id:
            # Уведомляем всех участников звонка
            # TODO: Получить список участников из базы данных
            return {"type": "call_reject_sent"}

    elif message_type == "call_end":
        # Обработка завершения звонка
        call_id = message_data.get("call_id")
        if call_id:
            # Уведомляем всех участников звонка
            # TODO: Получить список участников из базы данных
            return {"type": "call_end_sent"}

    logger.warning(f"Unknown WebSocket message type: {message_type} from {user_jid}")
    return {"type": "error", "message": f"Unknown message type: {message_type}"}


async def websocket_endpoint(websocket: WebSocket, token: str):
    """WebSocket endpoint для real-time связи"""
    user_jid = await authenticate_websocket(token)

    if not user_jid:
        await websocket.close(code=4001, reason="Authentication failed")
        return

    await websocket_manager.connect(websocket, user_jid)

    try:
        while True:
            # Получаем сообщение от клиента
            data = await websocket.receive_text()

            try:
                message_data = json.loads(data)
                response = await handle_websocket_message(user_jid, message_data)

                if response:
                    await websocket.send_text(json.dumps(response))

            except json.JSONDecodeError:
                await websocket.send_text(
                    json.dumps({"type": "error", "message": "Invalid JSON format"})
                )
            except Exception as e:
                logger.error(f"❌ Error handling WebSocket message: {e}")
                await websocket.send_text(
                    json.dumps({"type": "error", "message": str(e)})
                )

    except WebSocketDisconnect:
        pass
    except Exception as e:
        logger.error(f"❌ WebSocket error for {user_jid}: {e}")
    finally:
        await websocket_manager.disconnect(user_jid)
