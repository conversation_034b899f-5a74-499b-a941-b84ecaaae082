"""
Сервис для работы с Redis
"""

from typing import Optional, Dict, Any
import json
import redis.asyncio as redis

from core.config import settings


class RedisService:
    """Сервис для работы с Redis"""

    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not hasattr(self, "redis"):
            self.redis: Optional[redis.Redis] = None

    async def connect(self):
        """Подключение к Redis"""
        try:
            self.redis = redis.from_url(settings.redis_url)
            # Проверяем подключение
            await self.redis.ping()
            print(f"✅ Connected to Redis at {settings.redis_url}")
        except Exception as e:
            print(f"❌ Failed to connect to Redis: {e}")
            self.redis = None

    async def disconnect(self):
        """Отключение от Redis"""
        if self.redis:
            await self.redis.close()

    async def set_user_status(self, user_jid: str, status: str):
        """Установка статуса пользователя"""
        if self.redis:
            try:
                await self.redis.hset(f"user:{user_jid}", "status", status)
                print(f"✅ Set status for {user_jid}: {status}")
            except Exception as e:
                print(f"❌ Failed to set status for {user_jid}: {e}")
        else:
            print(f"❌ Redis not connected, cannot set status for {user_jid}")

    async def get_user_status(self, user_jid: str) -> Optional[str]:
        """Получение статуса пользователя"""
        if self.redis:
            status = await self.redis.hget(f"user:{user_jid}", "status")
            return status.decode() if status else None
        return None

    async def get_online_users(self) -> Dict[str, str]:
        """Получение списка онлайн пользователей"""
        online_users = {}
        if self.redis:
            async for key in self.redis.scan_iter(match="user:*"):
                user_data = await self.redis.hgetall(key)
                if user_data.get(b"status") == b"online":
                    user_jid = key.decode().replace("user:", "")
                    online_users[user_jid] = "online"
        return online_users

    async def set_cache(self, key: str, value: Any, expire: int = 3600):
        """Установка значения в кэш"""
        if self.redis:
            await self.redis.setex(key, expire, json.dumps(value))

    async def get_cache(self, key: str) -> Optional[Any]:
        """Получение значения из кэша"""
        if self.redis:
            value = await self.redis.get(key)
            if value:
                return json.loads(value)
        return None

    async def delete_cache(self, key: str):
        """Удаление значения из кэша"""
        if self.redis:
            await self.redis.delete(key)

    # OTP методы
    async def store_otp(self, email: str, otp_hash: str, ttl: int = 300):
        """Сохранение OTP кода в Redis"""
        if self.redis:
            try:
                otp_data = {
                    "hash": otp_hash,
                    "attempts": 0,
                    "email": email
                }
                await self.redis.setex(f"otp:{email}", ttl, json.dumps(otp_data))
                print(f"✅ OTP stored for {email} with TTL {ttl}s")
            except Exception as e:
                print(f"❌ Failed to store OTP for {email}: {e}")

    async def get_otp_data(self, email: str) -> Optional[Dict[str, Any]]:
        """Получение данных OTP из Redis"""
        if self.redis:
            try:
                data = await self.redis.get(f"otp:{email}")
                if data:
                    return json.loads(data.decode() if isinstance(data, bytes) else data)
            except Exception as e:
                print(f"❌ Failed to get OTP data for {email}: {e}")
        return None

    async def update_otp_attempts(self, email: str, attempts: int):
        """Обновление количества попыток OTP"""
        if self.redis:
            try:
                otp_data = await self.get_otp_data(email)
                if otp_data:
                    otp_data["attempts"] = attempts
                    ttl = await self.redis.ttl(f"otp:{email}")
                    if ttl > 0:
                        await self.redis.setex(f"otp:{email}", ttl, json.dumps(otp_data))
            except Exception as e:
                print(f"❌ Failed to update OTP attempts for {email}: {e}")

    async def delete_otp(self, email: str):
        """Удаление OTP кода из Redis"""
        if self.redis:
            try:
                await self.redis.delete(f"otp:{email}")
                print(f"✅ OTP deleted for {email}")
            except Exception as e:
                print(f"❌ Failed to delete OTP for {email}: {e}")
