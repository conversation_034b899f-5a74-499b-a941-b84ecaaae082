"""
👁️ eJabberd Monitor - Мониторинг сообщений и событий eJabberd

Отслеживает события в eJabberd и уведомляет WebSocket клиентов:
- Новые сообщения
- Изменения статусов пользователей
- Подключения/отключения
"""

import asyncio
import logging
from typing import Set, Optional
from datetime import datetime, timedelta

import aiohttp

from services.websocket_client import websocket_manager
from services.redis import RedisService
from core.config import settings

logger = logging.getLogger(__name__)


class EjabberdMonitor:
    """Монитор событий eJabberd"""

    def __init__(self):
        self.running = False
        self.last_check = datetime.utcnow()
        self.known_online_users: Set[str] = set()
        self.redis_service = RedisService()

    async def start_monitoring(self):
        """Запуск мониторинга"""
        self.running = True
        logger.info("👁️ Starting eJabberd monitoring")

        # Запускаем задачи мониторинга
        tasks = [
            asyncio.create_task(self.monitor_user_sessions()),
            asyncio.create_task(self.monitor_message_archive()),
            asyncio.create_task(self.sync_online_users()),
        ]

        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            logger.error(f"❌ Monitoring error: {e}")
        finally:
            self.running = False

    async def stop_monitoring(self):
        """Остановка мониторинга"""
        self.running = False
        logger.info("👁️ Stopping eJabberd monitoring")

    async def monitor_user_sessions(self):
        """Мониторинг пользовательских сессий"""
        while self.running:
            try:
                # Получаем список онлайн пользователей из eJabberd
                online_users = await self.get_online_users_from_ejabberd()

                if online_users is not None:
                    current_online = set(online_users)

                    # Находим новых пользователей
                    new_online = current_online - self.known_online_users
                    for user_jid in new_online:
                        await websocket_manager.broadcast_user_status(
                            user_jid, "online"
                        )
                        logger.info(f"👤 User came online: {user_jid}")

                    # Находим отключившихся пользователей
                    went_offline = self.known_online_users - current_online
                    for user_jid in went_offline:
                        await websocket_manager.broadcast_user_status(
                            user_jid, "offline"
                        )
                        logger.info(f"👤 User went offline: {user_jid}")

                    self.known_online_users = current_online

                await asyncio.sleep(30)  # Проверяем каждые 30 секунд

            except Exception as e:
                logger.error(f"❌ Error monitoring user sessions: {e}")
                await asyncio.sleep(10)

    async def monitor_message_archive(self):
        """Мониторинг архива сообщений (MAM)"""
        while self.running:
            try:
                # Проверяем новые сообщения в архиве
                # Это упрощенная версия - в реальности нужно использовать MAM API
                await asyncio.sleep(60)  # Проверяем каждую минуту

            except Exception as e:
                logger.error(f"❌ Error monitoring message archive: {e}")
                await asyncio.sleep(30)

    async def sync_online_users(self):
        """Синхронизация онлайн пользователей с Redis"""
        while self.running:
            try:
                # Получаем пользователей подключенных к WebSocket
                ws_online_users = await websocket_manager.get_online_users()

                # Обновляем статусы в Redis
                for user_jid in ws_online_users:
                    await self.redis_service.set_user_status(user_jid, "online")

                # Очищаем старые статусы
                await self.cleanup_old_statuses()

                await asyncio.sleep(120)  # Синхронизируем каждые 2 минуты

            except Exception as e:
                logger.error(f"❌ Error syncing online users: {e}")
                await asyncio.sleep(60)

    async def get_online_users_from_ejabberd(self) -> Optional[list]:
        """Получение списка онлайн пользователей из eJabberd"""
        try:
            # Используем eJabberd REST API
            url = f"{settings.ejabberd_api_url}/connected_users_info"
            auth = aiohttp.BasicAuth(
                settings.ejabberd_admin_user, settings.ejabberd_admin_password
            )

            async with aiohttp.ClientSession() as session:
                async with session.post(url, auth=auth, json={}) as response:
                    if response.status == 200:
                        data = await response.json()
                        # Извлекаем JID пользователей
                        users = []
                        for user_info in data:
                            if isinstance(user_info, dict) and "jid" in user_info:
                                users.append(user_info["jid"])
                        return users
                    else:
                        logger.warning(f"⚠️ eJabberd API returned {response.status}")
                        return None

        except Exception as e:
            logger.error(f"❌ Error getting online users from eJabberd: {e}")
            return None

    async def cleanup_old_statuses(self):
        """Очистка старых статусов"""
        try:
            # Удаляем статусы старше 5 минут
            cutoff_time = datetime.utcnow() - timedelta(minutes=5)

            # Это упрощенная версия - в реальности нужно хранить timestamp в Redis
            # и очищать по нему

        except Exception as e:
            logger.error(f"❌ Error cleaning up old statuses: {e}")

    async def handle_ejabberd_webhook(self, event_data: dict):
        """Обработка webhook от eJabberd"""
        try:
            event_type = event_data.get("event")

            if event_type == "user_session_opened":
                user_jid = event_data.get("user_jid")
                if user_jid:
                    await websocket_manager.broadcast_user_status(user_jid, "online")
                    logger.info(f"👤 Webhook: User {user_jid} came online")

            elif event_type == "user_session_closed":
                user_jid = event_data.get("user_jid")
                if user_jid:
                    await websocket_manager.broadcast_user_status(user_jid, "offline")
                    logger.info(f"👤 Webhook: User {user_jid} went offline")

            elif event_type == "message_sent":
                from_jid = event_data.get("from_jid")
                to_jid = event_data.get("to_jid")
                body = event_data.get("body")
                msg_type = event_data.get("type", "chat")

                if from_jid and to_jid and body:
                    await websocket_manager.notify_new_message(
                        from_jid=from_jid,
                        to_jid=to_jid,
                        message_body=body,
                        message_type=msg_type,
                    )
                    logger.info(f"📨 Webhook: Message from {from_jid} to {to_jid}")

        except Exception as e:
            logger.error(f"❌ Error handling eJabberd webhook: {e}")

    async def get_user_roster(self, user_jid: str) -> list:
        """Получение ростера пользователя"""
        try:
            username = user_jid.split("@")[0]
            url = f"{settings.ejabberd_api_url}/get_roster"
            auth = aiohttp.BasicAuth(
                settings.ejabberd_admin_user, settings.ejabberd_admin_password
            )

            data = {"user": username, "server": settings.ejabberd_domain}

            async with aiohttp.ClientSession() as session:
                async with session.post(url, auth=auth, json=data) as response:
                    if response.status == 200:
                        roster_data = await response.json()
                        return roster_data
                    else:
                        logger.warning(f"⚠️ Failed to get roster for {user_jid}")
                        return []

        except Exception as e:
            logger.error(f"❌ Error getting user roster: {e}")
            return []

    async def send_presence_probe(self, from_jid: str, to_jid: str):
        """Отправка presence probe для проверки статуса"""
        try:
            from_user = from_jid.split("@")[0]
            to_user = to_jid.split("@")[0]

            url = f"{settings.ejabberd_api_url}/send_stanza"
            auth = aiohttp.BasicAuth(
                settings.ejabberd_admin_user, settings.ejabberd_admin_password
            )

            stanza = f'<presence from="{from_jid}" to="{to_jid}" type="probe"/>'

            data = {
                "from": from_user,
                "server": settings.ejabberd_domain,
                "stanza": stanza,
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(url, auth=auth, json=data) as response:
                    if response.status == 200:
                        logger.info(
                            f"📡 Sent presence probe from {from_jid} to {to_jid}"
                        )
                    else:
                        logger.warning("⚠️ Failed to send presence probe")

        except Exception as e:
            logger.error(f"❌ Error sending presence probe: {e}")


# Глобальный экземпляр монитора
ejabberd_monitor = EjabberdMonitor()


async def start_ejabberd_monitoring():
    """Запуск мониторинга eJabberd"""
    await ejabberd_monitor.start_monitoring()


async def stop_ejabberd_monitoring():
    """Остановка мониторинга eJabberd"""
    await ejabberd_monitor.stop_monitoring()
