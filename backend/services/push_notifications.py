"""
Сервис для отправки push уведомлений
"""

import asyncio
from typing import Dict, Any
from sqlalchemy import select

from database.connection import AsyncSessionLocal
from models.notification import Notification
from models.user import UserExtended
from services.redis import RedisService


class PushNotificationService:
    """Сервис для отправки push уведомлений"""

    def __init__(self):
        self.redis_service = RedisService()

    async def create_notification(
        self,
        user_jid: str,
        notification_type: str,
        title: str,
        body: str,
        data: Dict[str, Any] = None,
    ) -> str:
        """Создание уведомления в базе данных"""
        async with AsyncSessionLocal() as db:
            notification = Notification(
                user_jid=user_jid,
                type=notification_type,
                title=title,
                body=body,
                data=data or {},
            )

            db.add(notification)
            await db.commit()
            await db.refresh(notification)

            return str(notification.id)

    async def send_message_notification(
        self, from_jid: str, to_jid: str, message_body: str, message_type: str = "chat"
    ):
        """Отправка уведомления о новом сообщении"""
        try:
            # Проверяем, онлайн ли получатель
            recipient_status = await self.redis_service.get_user_status(to_jid)
            if recipient_status == "online":
                # Пользователь онлайн, не отправляем push уведомление
                return

            # Получаем информацию об отправителе
            async with AsyncSessionLocal() as db:
                from_username = from_jid.split("@")[0]
                result = await db.execute(
                    select(UserExtended).where(UserExtended.username == from_username)
                )
                sender = result.scalar_one_or_none()

                sender_name = (
                    sender.display_name
                    if sender and sender.display_name
                    else from_username
                )

            # Создаем уведомление
            title = f"Новое сообщение от {sender_name}"
            body = (
                message_body[:100] + "..." if len(message_body) > 100 else message_body
            )

            notification_id = await self.create_notification(
                user_jid=to_jid,
                notification_type="message",
                title=title,
                body=body,
                data={
                    "from_jid": from_jid,
                    "message_type": message_type,
                    "timestamp": str(asyncio.get_event_loop().time()),
                },
            )

            # Здесь можно добавить отправку реального push уведомления
            # через Firebase, Apple Push Notification Service и т.д.
            print(f"📱 Push notification sent to {to_jid}: {title}")

            return notification_id

        except Exception as e:
            print(f"Error sending message notification: {e}")
            return None

    async def send_group_invite_notification(
        self, inviter_jid: str, invitee_jid: str, group_name: str, group_id: str
    ):
        """Отправка уведомления о приглашении в группу"""
        try:
            # Получаем информацию о пригласившем
            async with AsyncSessionLocal() as db:
                inviter_username = inviter_jid.split("@")[0]
                result = await db.execute(
                    select(UserExtended).where(
                        UserExtended.username == inviter_username
                    )
                )
                inviter = result.scalar_one_or_none()

                inviter_name = (
                    inviter.display_name
                    if inviter and inviter.display_name
                    else inviter_username
                )

            # Создаем уведомление
            title = "Приглашение в группу"
            body = f"{inviter_name} пригласил вас в группу '{group_name}'"

            notification_id = await self.create_notification(
                user_jid=invitee_jid,
                notification_type="group_invite",
                title=title,
                body=body,
                data={
                    "inviter_jid": inviter_jid,
                    "group_id": group_id,
                    "group_name": group_name,
                },
            )

            print(f"📱 Group invite notification sent to {invitee_jid}: {title}")

            return notification_id

        except Exception as e:
            print(f"Error sending group invite notification: {e}")
            return None

    async def send_call_notification(
        self, caller_jid: str, callee_jid: str, call_type: str, room_name: str
    ):
        """Отправка уведомления о входящем звонке"""
        try:
            # Получаем информацию о звонящем
            async with AsyncSessionLocal() as db:
                caller_username = caller_jid.split("@")[0]
                result = await db.execute(
                    select(UserExtended).where(UserExtended.username == caller_username)
                )
                caller = result.scalar_one_or_none()

                caller_name = (
                    caller.display_name
                    if caller and caller.display_name
                    else caller_username
                )

            # Создаем уведомление
            call_type_text = "Видеозвонок" if call_type == "video" else "Аудиозвонок"
            title = f"Входящий {call_type_text.lower()}"
            body = f"{caller_name} звонит вам"

            notification_id = await self.create_notification(
                user_jid=callee_jid,
                notification_type="call",
                title=title,
                body=body,
                data={
                    "caller_jid": caller_jid,
                    "call_type": call_type,
                    "room_name": room_name,
                },
            )

            print(f"📱 Call notification sent to {callee_jid}: {title}")

            return notification_id

        except Exception as e:
            print(f"Error sending call notification: {e}")
            return None

    async def get_unread_count(self, user_jid: str) -> int:
        """Получение количества непрочитанных уведомлений"""
        try:
            async with AsyncSessionLocal() as db:
                result = await db.execute(
                    select(Notification).where(
                        Notification.user_jid == user_jid, Notification.is_read == False
                    )
                )
                notifications = result.scalars().all()
                return len(notifications)
        except Exception as e:
            print(f"Error getting unread count: {e}")
            return 0


# Глобальный экземпляр сервиса
push_service = PushNotificationService()
