"""
Сервис для работы с OTP кодами
"""

import secrets
import hashlib
from typing import Optional, Dict, Any
from datetime import datetime, timedelta

from services.redis import RedisService
from core.config import settings


class OTPService:
    """Сервис для генерации и проверки OTP кодов"""
    
    def __init__(self):
        self.redis_service = None
        self.otp_length = 6
        self.otp_ttl = 300  # 5 минут в секундах
        self.max_attempts = 5
    
    def _get_redis_service(self) -> RedisService:
        """Получение Redis сервиса"""
        if not self.redis_service:
            from app import get_redis_service
            self.redis_service = get_redis_service()
        return self.redis_service
    
    def generate_otp(self) -> str:
        """Генерация 6-значного OTP кода"""
        # Генерируем случайное число от 100000 до 999999
        return str(secrets.randbelow(900000) + 100000)
    
    def hash_otp(self, otp: str) -> str:
        """Хеширование OTP кода для безопасного хранения"""
        return hashlib.sha256(f"{otp}{settings.secret_key}".encode()).hexdigest()
    
    async def send_otp(self, email: str) -> bool:
        """
        Генерация и отправка OTP кода
        В режиме разработки выводит код в консоль
        """
        try:
            redis_service = self._get_redis_service()
            if not redis_service or not redis_service.redis:
                print("❌ Redis service not available")
                return False

            # Генерируем OTP код
            otp_code = self.generate_otp()
            otp_hash = self.hash_otp(otp_code)

            # Сохраняем в Redis с TTL
            await redis_service.store_otp(email, otp_hash, self.otp_ttl)

            # В режиме разработки выводим код в консоль
            print(f"\n🔐 OTP CODE FOR {email}: {otp_code}")
            print(f"⏰ Code expires in {self.otp_ttl // 60} minutes")
            print(f"🔑 Redis key: otp:{email}")
            print("-" * 50)

            return True

        except Exception as e:
            print(f"❌ Error sending OTP for {email}: {str(e)}")
            return False
    
    async def verify_otp(self, email: str, otp: str) -> Dict[str, Any]:
        """
        Проверка OTP кода
        Возвращает словарь с результатом проверки
        """
        try:
            redis_service = self._get_redis_service()
            if not redis_service or not redis_service.redis:
                return {
                    "success": False,
                    "error": "Redis service not available"
                }

            # Получаем данные из Redis
            otp_data = await redis_service.get_otp_data(email)
            if not otp_data:
                return {
                    "success": False,
                    "error": "OTP code expired or not found"
                }

            stored_hash = otp_data.get("hash")
            attempts = otp_data.get("attempts", 0)

            # Проверяем количество попыток
            if attempts >= self.max_attempts:
                await redis_service.delete_otp(email)
                return {
                    "success": False,
                    "error": f"Too many attempts. Maximum {self.max_attempts} attempts allowed."
                }

            # Проверяем OTP код
            provided_hash = self.hash_otp(otp)

            if provided_hash != stored_hash:
                # Увеличиваем счетчик попыток
                new_attempts = attempts + 1
                await redis_service.update_otp_attempts(email, new_attempts)

                return {
                    "success": False,
                    "error": f"Invalid OTP code. {self.max_attempts - new_attempts} attempts remaining."
                }

            # OTP код верный - удаляем из Redis
            await redis_service.delete_otp(email)

            print(f"✅ OTP verified successfully for {email}")

            return {
                "success": True,
                "message": "OTP verified successfully"
            }

        except Exception as e:
            print(f"❌ Error verifying OTP for {email}: {str(e)}")
            return {
                "success": False,
                "error": f"Internal error: {str(e)}"
            }
    
    async def cleanup_expired_otps(self):
        """Очистка истекших OTP кодов (вызывается периодически)"""
        try:
            redis_service = self._get_redis_service()
            if not redis_service or not redis_service.redis:
                return
            
            # Redis автоматически удаляет ключи с истекшим TTL
            # Эта функция может быть расширена для дополнительной очистки
            print("🧹 OTP cleanup completed")
            
        except Exception as e:
            print(f"❌ Error during OTP cleanup: {str(e)}")
