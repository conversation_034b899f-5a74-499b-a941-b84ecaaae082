"""
Модель контактов пользователя
"""

from sqlalchemy import Column, String, DateTime, Boolean, Text, Integer, Index
from sqlalchemy.dialects.postgresql import UUID
from datetime import datetime
import uuid

from database.connection import Base


class Contact(Base):
    """Контакт пользователя"""

    __tablename__ = "contacts"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Владелец контакта
    owner_jid = Column(String(255), nullable=False, index=True)

    # Контакт
    contact_jid = Column(String(255), nullable=False, index=True)
    contact_username = Column(String(100), nullable=False)
    contact_display_name = Column(String(255), nullable=False)

    # Метаданные
    added_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    last_message_at = Column(DateTime, nullable=True)
    last_message_preview = Column(Text, nullable=True)
    unread_count = Column(Integer, default=0, nullable=False)

    # Настройки
    is_pinned = Column(Boolean, default=False, nullable=False)
    is_muted = Column(Boolean, default=False, nullable=False)
    is_blocked = Column(Boolean, default=False, nullable=False)

    # Индексы для быстрого поиска
    __table_args__ = (
        Index("idx_contacts_owner_contact", "owner_jid", "contact_jid"),
        Index("idx_contacts_owner_last_message", "owner_jid", "last_message_at"),
    )

    def __repr__(self):
        return f"<Contact {self.owner_jid} -> {self.contact_jid}>"


class ChatHistory(Base):
    """История чатов для быстрого доступа"""

    __tablename__ = "chat_history"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Участники чата
    user_jid = Column(String(255), nullable=False, index=True)
    chat_jid = Column(
        String(255), nullable=False, index=True
    )  # Может быть пользователь или группа
    chat_type = Column(String(20), nullable=False, default="direct")  # direct, group

    # Информация о чате
    chat_name = Column(String(255), nullable=False)
    chat_avatar = Column(String(500), nullable=True)

    # Последнее сообщение
    last_message_id = Column(String(255), nullable=True)
    last_message_body = Column(Text, nullable=True)
    last_message_at = Column(DateTime, nullable=True)
    last_message_from = Column(String(255), nullable=True)

    # Счетчики
    unread_count = Column(Integer, default=0, nullable=False)
    total_messages = Column(Integer, default=0, nullable=False)

    # Настройки
    is_pinned = Column(Boolean, default=False, nullable=False)
    is_muted = Column(Boolean, default=False, nullable=False)
    is_archived = Column(Boolean, default=False, nullable=False)

    # Метаданные
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )

    # Индексы
    __table_args__ = (
        Index("idx_chat_history_user_updated", "user_jid", "updated_at"),
        Index("idx_chat_history_user_chat", "user_jid", "chat_jid"),
    )

    def __repr__(self):
        return f"<ChatHistory {self.user_jid} -> {self.chat_jid}>"
