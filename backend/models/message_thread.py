"""
Модель для ответов на сообщения (threads/replies)
"""

from sqlalchemy import Column, String, DateTime, Text, Boolean
from sqlalchemy.dialects.postgresql import UUID
from datetime import datetime
import uuid

from database.connection import Base


class MessageThread(Base):
    """Ответы на сообщения и треды"""

    __tablename__ = "message_threads"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    message_id = Column(String, nullable=False, index=True)  # ID сообщения
    reply_to_message_id = Column(
        String, nullable=True, index=True
    )  # ID сообщения, на которое отвечаем
    thread_id = Column(String, nullable=True, index=True)  # ID треда (для группировки)
    user_jid = Column(String, nullable=False, index=True)  # Автор сообщения
    body = Column(Text, nullable=False)  # Текст сообщения
    message_type = Column(String, default="chat")  # Тип сообщения
    is_forwarded = Column(Boolean, default=False)  # Пересланное сообщение
    forwarded_from_jid = Column(String, nullable=True)  # От кого переслано
    forwarded_from_message_id = Column(
        String, nullable=True
    )  # ID оригинального сообщения
    is_edited = Column(Boolean, default=False)  # Отредактированное сообщение
    edited_at = Column(DateTime, nullable=True)  # Время редактирования
    created_at = Column(DateTime, default=datetime.utcnow)

    def to_dict(self):
        return {
            "id": str(self.id),
            "message_id": self.message_id,
            "reply_to_message_id": self.reply_to_message_id,
            "thread_id": self.thread_id,
            "user_jid": self.user_jid,
            "body": self.body,
            "message_type": self.message_type,
            "is_forwarded": self.is_forwarded,
            "forwarded_from_jid": self.forwarded_from_jid,
            "forwarded_from_message_id": self.forwarded_from_message_id,
            "is_edited": self.is_edited,
            "edited_at": self.edited_at.isoformat() if self.edited_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }
