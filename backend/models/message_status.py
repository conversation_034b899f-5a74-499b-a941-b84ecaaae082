"""
Модель для статуса прочтения сообщений
"""

from sqlalchemy import Column, String, DateTime, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from datetime import datetime
import uuid

from database.connection import Base


class MessageStatus(Base):
    """Статус прочтения сообщений"""

    __tablename__ = "message_statuses"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    message_id = Column(String, nullable=False, index=True)  # ID сообщения
    user_jid = Column(String, nullable=False, index=True)  # JID пользователя
    status = Column(
        String, nullable=False, default="sent"
    )  # Статус: sent, delivered, read
    timestamp = Column(DateTime, default=datetime.utcnow)  # Время статуса

    # Уникальность: один статус на сообщение для каждого пользователя
    __table_args__ = (
        UniqueConstraint("message_id", "user_jid", name="unique_message_user_status"),
    )

    def to_dict(self):
        return {
            "id": str(self.id),
            "message_id": self.message_id,
            "user_jid": self.user_jid,
            "status": self.status,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None,
        }
