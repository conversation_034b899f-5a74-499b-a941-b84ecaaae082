"""
Модели групповых чатов
"""

from sqlalchemy import Column, String, Text, Boolean, Integer, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from database.connection import Base


class GroupChat(Base):
    """Групповые чаты (дополнительная информация к MUC)"""

    __tablename__ = "group_chats"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    jid = Column(String(255), nullable=False, unique=True)  # <EMAIL>
    name = Column(String(255), nullable=False)
    description = Column(Text)
    avatar_url = Column(Text)
    created_by = Column(String(255), nullable=False)  # JID создателя
    max_members = Column(Integer, default=500)
    is_public = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now()
    )

    # Связь с участниками
    members = relationship(
        "GroupMember", back_populates="group", cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<GroupChat(name='{self.name}', jid='{self.jid}')>"


class GroupMember(Base):
    """Участники групповых чатов"""

    __tablename__ = "group_members"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    group_id = Column(
        UUID(as_uuid=True),
        ForeignKey("group_chats.id", ondelete="CASCADE"),
        nullable=False,
    )
    user_jid = Column(String(255), nullable=False)
    role = Column(String(20), default="member")  # admin, moderator, member
    joined_at = Column(DateTime(timezone=True), server_default=func.now())

    # Связь с группой
    group = relationship("GroupChat", back_populates="members")

    __table_args__ = ({"extend_existing": True},)

    def __repr__(self):
        return f"<GroupMember(user_jid='{self.user_jid}', role='{self.role}')>"
