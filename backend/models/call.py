"""
Модель звонков
"""

from sqlalchemy import Column, String, Integer, DateTime, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
import uuid

from database.connection import Base


class Call(Base):
    """Звонки через LiveKit"""

    __tablename__ = "calls"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    livekit_room_id = Column(String(255), nullable=False)
    call_type = Column(String(20), nullable=False)  # audio, video
    initiator_jid = Column(String(255), nullable=False)  # JID инициатора
    participants = Column(JSON, default=list)  # список JID участников
    status = Column(
        String(20), default="initiated"
    )  # initiated, ongoing, ended, declined
    started_at = Column(DateTime(timezone=True), server_default=func.now())
    ended_at = Column(DateTime(timezone=True))
    duration_seconds = Column(Integer, default=0)

    def __repr__(self):
        return (
            f"<Call(livekit_room_id='{self.livekit_room_id}', status='{self.status}')>"
        )
