"""
Модель реакций на сообщения
"""

from sqlalchemy import Column, String, DateTime, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from datetime import datetime
import uuid

from database.connection import Base


class MessageReaction(Base):
    """Реакции на сообщения"""

    __tablename__ = "message_reactions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    message_id = Column(String, nullable=False, index=True)  # ID сообщения из ejabberd
    user_jid = Column(String, nullable=False, index=True)  # JID пользователя
    emoji = Column(String(10), nullable=False)  # Эмодзи реакции
    created_at = Column(DateTime, default=datetime.utcnow)

    # Уникальность: один пользователь может поставить только одну реакцию на сообщение
    __table_args__ = (
        UniqueConstraint("message_id", "user_jid", name="unique_user_message_reaction"),
    )

    def to_dict(self):
        return {
            "id": str(self.id),
            "message_id": self.message_id,
            "user_jid": self.user_jid,
            "emoji": self.emoji,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }
