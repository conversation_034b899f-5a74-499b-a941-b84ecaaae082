"""
API роутеры - только для бизнес-логики, не для real-time функций
"""

from fastapi import APIRouter
from .auth import router as auth_router
from .users import router as users_router
from .calls import router as calls_router
from .files import router as files_router
from .contacts import router as contacts_router

# Главный роутер API
api_router = APIRouter(prefix="/api")

# Подключаем только необходимые роутеры
# Real-time функции (сообщения, присутствие) обрабатываются ejabberd
api_router.include_router(auth_router, prefix="/auth", tags=["Authentication"])
api_router.include_router(users_router, prefix="/users", tags=["Users"])
api_router.include_router(calls_router, prefix="/calls", tags=["Calls"])
api_router.include_router(files_router, prefix="/files", tags=["Files"])
api_router.include_router(contacts_router, prefix="/contacts", tags=["Contacts"])


# Health check endpoint
@api_router.get("/health")
async def health_check():
    """Проверка состояния API"""
    return {"status": "healthy", "message": "Messenger API is running"}
