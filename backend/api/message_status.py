"""
API роутер для статусов сообщений (доставлено, прочитано)
"""

from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from datetime import datetime

from database.connection import get_db
from models.message_status import MessageStatus
from schemas.message_status import (
    MessageStatusUpdate,
    MessageStatusResponse,
    MessageStatusBatch,
    ConversationStatusResponse,
)
from core.security import get_current_user_jid
from services.ejabberd import EjabberdService
from services.redis import RedisService

router = APIRouter(prefix="/message-status", tags=["message-status"])

ejabberd_service = EjabberdService()
redis_service = RedisService()


@router.post(
    "/update",
    response_model=MessageStatusResponse,
    summary="Обновление статуса сообщения",
    description="""
    Обновляет статус доставки/прочтения сообщения.

    **Поддерживаемые статусы:**
    - `sent` - сообщение отправлено
    - `delivered` - сообщение доставлено получателю
    - `read` - сообщение прочитано получателем

    **Автоматическое обновление:**
    - Статус `delivered` устанавливается при получении сообщения XMPP клиентом
    - Статус `read` устанавливается при открытии чата пользователем
    - Уведомления о прочтении отправляются отправителю

    **Применение:**
    - Отображение галочек в мессенджере (✓ доставлено, ✓✓ прочитано)
    - Аналитика доставляемости сообщений
    - Подтверждения для важных сообщений
    """,
    responses={
        200: {
            "description": "Статус успешно обновлен",
            "content": {
                "application/json": {
                    "example": {
                        "message_id": "msg-123",
                        "user_jid": "john_doe@localhost",
                        "status": "read",
                        "timestamp": "2024-01-01T12:00:00Z",
                    }
                }
            },
        },
        401: {"description": "Требуется авторизация"},
        404: {"description": "Сообщение не найдено"},
        422: {"description": "Неверный статус"},
        500: {"description": "Ошибка обновления статуса"},
    },
)
async def update_message_status(
    status_data: MessageStatusUpdate,
    current_user: str = Depends(get_current_user_jid),
    db: AsyncSession = Depends(get_db),
):
    """Обновить статус сообщения"""
    try:
        # Проверяем, существует ли уже статус для этого сообщения и пользователя
        existing_status = await db.execute(
            select(MessageStatus).where(
                MessageStatus.message_id == status_data.message_id,
                MessageStatus.user_jid == current_user,
            )
        )
        status = existing_status.scalar_one_or_none()

        if status:
            # Обновляем существующий статус (только если новый статус "выше")
            status_hierarchy = {"sent": 1, "delivered": 2, "read": 3}
            current_level = status_hierarchy.get(status.status, 0)
            new_level = status_hierarchy.get(status_data.status, 0)

            if new_level > current_level:
                status.status = status_data.status
                status.timestamp = datetime.utcnow()
        else:
            # Создаем новый статус
            status = MessageStatus(
                message_id=status_data.message_id,
                user_jid=current_user,
                status=status_data.status,
            )
            db.add(status)

        await db.commit()
        await db.refresh(status)

        # Инвалидируем кэш статусов для этого сообщения
        cache_key = f"message_status:{status_data.message_id}"
        await redis_service.delete_cache(cache_key)

        # Отправляем уведомление отправителю сообщения (опционально)
        # Можно отправить специальное сообщение со статусом

        return MessageStatusResponse(
            id=status.id,
            message_id=status.message_id,
            user_jid=status.user_jid,
            status=status.status,
            timestamp=status.timestamp,
        )

    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=500, detail=f"Failed to update message status: {str(e)}"
        )


@router.post("/batch-update")
async def batch_update_message_status(
    batch_data: MessageStatusBatch,
    current_user: str = Depends(get_current_user_jid),
    db: AsyncSession = Depends(get_db),
):
    """Массовое обновление статусов сообщений"""
    try:
        updated_count = 0

        for status_update in batch_data.updates:
            # Проверяем существующий статус
            existing_status = await db.execute(
                select(MessageStatus).where(
                    MessageStatus.message_id == status_update.message_id,
                    MessageStatus.user_jid == current_user,
                )
            )
            status = existing_status.scalar_one_or_none()

            if status:
                # Обновляем если новый статус "выше"
                status_hierarchy = {"sent": 1, "delivered": 2, "read": 3}
                current_level = status_hierarchy.get(status.status, 0)
                new_level = status_hierarchy.get(status_update.status, 0)

                if new_level > current_level:
                    status.status = status_update.status
                    status.timestamp = datetime.utcnow()
                    updated_count += 1
            else:
                # Создаем новый статус
                status = MessageStatus(
                    message_id=status_update.message_id,
                    user_jid=current_user,
                    status=status_update.status,
                )
                db.add(status)
                updated_count += 1

        await db.commit()

        # Инвалидируем кэш для всех обновленных сообщений
        for status_update in batch_data.updates:
            cache_key = f"message_status:{status_update.message_id}"
            await redis_service.delete_cache(cache_key)

        return {
            "success": True,
            "updated_count": updated_count,
            "total_messages": len(batch_data.updates),
        }

    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=500, detail=f"Failed to batch update message status: {str(e)}"
        )


@router.get("/message/{message_id}")
async def get_message_status(
    message_id: str,
    current_user: str = Depends(get_current_user_jid),
    db: AsyncSession = Depends(get_db),
):
    """Получить статусы сообщения"""
    try:
        # Проверяем кэш
        cache_key = f"message_status:{message_id}"
        cached_status = await redis_service.get_cache(cache_key)

        if cached_status:
            return cached_status

        # Получаем статусы из базы данных
        result = await db.execute(
            select(MessageStatus).where(MessageStatus.message_id == message_id)
        )
        statuses = result.scalars().all()

        # Группируем статусы по типу
        status_groups = {"delivered": [], "read": []}

        for status in statuses:
            if status.status in status_groups:
                status_groups[status.status].append(
                    {"user_jid": status.user_jid, "timestamp": status.timestamp}
                )

        response = {
            "message_id": message_id,
            "delivered_to": status_groups["delivered"],
            "read_by": status_groups["read"],
            "delivery_count": len(status_groups["delivered"]),
            "read_count": len(status_groups["read"]),
        }

        # Кэшируем на 1 минуту
        await redis_service.set_cache(cache_key, response, expire=60)

        return response

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get message status: {str(e)}"
        )


@router.get("/conversation/{peer_jid}", response_model=ConversationStatusResponse)
async def get_conversation_status(
    peer_jid: str,
    limit: int = 50,
    current_user: str = Depends(get_current_user_jid),
    db: AsyncSession = Depends(get_db),
):
    """Получить статусы сообщений в разговоре"""
    try:
        # Получаем последние сообщения из архива eJabberd
        messages = await ejabberd_service.get_message_history(
            current_user, peer_jid, limit=limit
        )

        if not messages:
            return ConversationStatusResponse(
                peer_jid=peer_jid, messages=[], unread_count=0
            )

        # Получаем статусы для всех сообщений
        message_ids = [msg["id"] for msg in messages if "id" in msg]

        if message_ids:
            result = await db.execute(
                select(MessageStatus).where(
                    MessageStatus.message_id.in_(message_ids),
                    MessageStatus.user_jid == current_user,
                )
            )
            statuses = result.scalars().all()

            # Создаем словарь статусов
            status_dict = {status.message_id: status for status in statuses}
        else:
            status_dict = {}

        # Формируем ответ
        message_statuses = []
        unread_count = 0

        for msg in messages:
            msg_id = msg.get("id", "")
            status = status_dict.get(msg_id)

            message_status = {
                "message_id": msg_id,
                "from_jid": msg.get("from", ""),
                "timestamp": msg.get("timestamp", ""),
                "status": status.status if status else "sent",
                "is_read": status.status == "read" if status else False,
            }

            # Считаем непрочитанные (сообщения от собеседника, которые не прочитаны)
            if msg.get("from") == peer_jid and (not status or status.status != "read"):
                unread_count += 1

            message_statuses.append(message_status)

        return ConversationStatusResponse(
            peer_jid=peer_jid, messages=message_statuses, unread_count=unread_count
        )

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get conversation status: {str(e)}"
        )


@router.post("/mark-read/{peer_jid}")
async def mark_conversation_read(
    peer_jid: str,
    current_user: str = Depends(get_current_user_jid),
    db: AsyncSession = Depends(get_db),
):
    """Отметить все сообщения в разговоре как прочитанные"""
    try:
        # Получаем последние сообщения от собеседника
        messages = await ejabberd_service.get_message_history(
            current_user, peer_jid, limit=100
        )

        # Фильтруем сообщения от собеседника
        peer_messages = [msg for msg in messages if msg.get("from") == peer_jid]
        message_ids = [msg["id"] for msg in peer_messages if "id" in msg]

        if not message_ids:
            return {"success": True, "marked_count": 0}

        # Обновляем статусы всех сообщений от собеседника на "read"
        updated_count = 0

        for message_id in message_ids:
            # Проверяем существующий статус
            existing_status = await db.execute(
                select(MessageStatus).where(
                    MessageStatus.message_id == message_id,
                    MessageStatus.user_jid == current_user,
                )
            )
            status = existing_status.scalar_one_or_none()

            if status:
                if status.status != "read":
                    status.status = "read"
                    status.timestamp = datetime.utcnow()
                    updated_count += 1
            else:
                # Создаем новый статус "read"
                status = MessageStatus(
                    message_id=message_id, user_jid=current_user, status="read"
                )
                db.add(status)
                updated_count += 1

        await db.commit()

        # Инвалидируем кэш
        for message_id in message_ids:
            cache_key = f"message_status:{message_id}"
            await redis_service.delete_cache(cache_key)

        return {"success": True, "marked_count": updated_count, "peer_jid": peer_jid}

    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=500, detail=f"Failed to mark conversation as read: {str(e)}"
        )


@router.get("/unread-count")
async def get_unread_count(
    current_user: str = Depends(get_current_user_jid),
    db: AsyncSession = Depends(get_db),
):
    """Получить общее количество непрочитанных сообщений"""
    try:
        # Это упрощенная версия - в реальности нужно анализировать архив сообщений
        # и сравнивать со статусами в базе данных

        # Получаем все статусы пользователя
        result = await db.execute(
            select(MessageStatus).where(
                MessageStatus.user_jid == current_user, MessageStatus.status != "read"
            )
        )
        unread_statuses = result.scalars().all()

        return {"unread_count": len(unread_statuses), "user_jid": current_user}

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get unread count: {str(e)}"
        )
