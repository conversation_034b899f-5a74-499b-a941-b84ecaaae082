"""
API роутер для звонков
"""

import uuid
from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from database.connection import get_db
from models.call import Call
from schemas.call import CallCreate, CallInfo, CallResponse
from services.livekit import LiveKitService
from services.push_notifications import push_service
from core.security import get_current_user_jid

router = APIRouter()

# Инициализируем сервисы
livekit_service = LiveKitService()


@router.post(
    "/create",
    response_model=CallResponse,
    summary="Создание звонка",
    description="""
    Создает новый аудио или видео звонок через LiveKit.

    **Типы звонков:**
    - `audio` - только аудио звонок
    - `video` - видео звонок с аудио

    **Процесс создания:**
    1. Создается комната в LiveKit
    2. Генерируются JWT токены для всех участников
    3. Сохраняется информация о звонке в БД
    4. Отправляются push уведомления участникам

    **Участники:**
    - Список JID пользователей для приглашения
    - Инициатор автоматически добавляется
    - Максимум 100 участников в групповом звонке

    **Возвращаемые данные:**
    - ID звонка для отслеживания
    - Название LiveKit комнаты
    - URL LiveKit сервера
    - JWT токены для каждого участника
    """,
    responses={
        200: {
            "description": "Звонок успешно создан",
            "content": {
                "application/json": {
                    "example": {
                        "call_id": "123e4567-e89b-12d3-a456-************",
                        "room_name": "call-room-uuid",
                        "livekit_url": "ws://localhost:7880",
                        "tokens": {
                            "john_doe@localhost": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                            "jane_doe@localhost": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                        },
                    }
                }
            },
        },
        401: {"description": "Требуется авторизация"},
        422: {"description": "Ошибка валидации участников"},
        500: {"description": "Ошибка создания комнаты в LiveKit"},
    },
)
async def create_call(
    call_data: CallCreate,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid),
):
    """Создание звонка через LiveKit"""
    try:
        room_name = f"call-{uuid.uuid4()}"

        # Записываем в БД
        call = Call(
            livekit_room_id=room_name,
            call_type=call_data.call_type,
            initiator_jid=current_user_jid,
            participants=call_data.participants,
        )

        db.add(call)
        await db.commit()
        await db.refresh(call)

        # Генерируем токены для участников
        all_participants = [current_user_jid] + call_data.participants
        tokens = livekit_service.generate_tokens_for_participants(
            room_name, all_participants
        )

        # Отправляем push уведомления участникам звонка
        for participant_jid in call_data.participants:
            await push_service.send_call_notification(
                caller_jid=current_user_jid,
                callee_jid=participant_jid,
                call_type=call_data.call_type,
                room_name=room_name,
            )

        # Отправляем WebSocket уведомления для real-time
        from services.websocket_client import websocket_manager

        for participant_jid in call_data.participants:
            await websocket_manager.send_personal_message(
                participant_jid,
                {
                    "type": "incoming_call",
                    "call_id": str(call.id),
                    "call_type": call_data.call_type,
                    "room_name": room_name,
                    "from_jid": current_user_jid,
                    "participants": [
                        {
                            "userJid": jid,
                            "username": jid.split("@")[0],
                            "displayName": jid.split("@")[0],
                            "isAudioEnabled": True,
                            "isVideoEnabled": call_data.call_type == "video",
                            "isSpeaking": False,
                        }
                        for jid in call_data.participants
                    ],
                },
            )

        return CallResponse(
            call_id=str(call.id),
            room_name=room_name,
            livekit_url="ws://localhost:7880",  # Внешний адрес для браузера
            tokens=tokens,
        )

    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{call_id}", response_model=CallInfo)
async def get_call_info(
    call_id: str,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid),
):
    """Получение информации о звонке"""
    result = await db.execute(select(Call).where(Call.id == call_id))
    call = result.scalar_one_or_none()

    if not call:
        raise HTTPException(status_code=404, detail="Call not found")

    return CallInfo.model_validate(call)


@router.get("/{call_id}/token")
async def get_call_token(
    call_id: str,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid),
):
    """Получение токена для подключения к звонку"""
    result = await db.execute(select(Call).where(Call.id == call_id))
    call = result.scalar_one_or_none()

    if not call:
        raise HTTPException(status_code=404, detail="Call not found")

    # Проверяем, что пользователь является участником звонка
    all_participants = [call.initiator_jid] + call.participants
    if current_user_jid not in all_participants:
        raise HTTPException(status_code=403, detail="Not a participant of this call")

    # Генерируем токен для текущего пользователя
    username = current_user_jid.split("@")[0]
    token = livekit_service.generate_access_token(
        call.livekit_room_id, username, username
    )

    return {
        "token": token,
        "livekit_url": "ws://localhost:7880",
        "room_name": call.livekit_room_id,
    }


@router.post("/{call_id}/answer")
async def answer_call(
    call_id: str,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid),
):
    """Ответ на звонок"""
    result = await db.execute(select(Call).where(Call.id == call_id))
    call = result.scalar_one_or_none()

    if not call:
        raise HTTPException(status_code=404, detail="Call not found")

    # Проверяем, что пользователь является участником звонка
    all_participants = [call.initiator_jid] + call.participants
    if current_user_jid not in all_participants:
        raise HTTPException(status_code=403, detail="Not a participant of this call")

    # Обновляем статус звонка
    call.status = "connected"
    await db.commit()

    # Уведомляем других участников
    from services.websocket_client import websocket_manager

    for participant_jid in all_participants:
        if participant_jid != current_user_jid:
            await websocket_manager.send_personal_message(
                participant_jid,
                {
                    "type": "call_answered",
                    "call_id": call_id,
                    "answered_by": current_user_jid,
                },
            )

    return {"success": True, "message": "Call answered"}


@router.post("/{call_id}/reject")
async def reject_call(
    call_id: str,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid),
):
    """Отклонение звонка"""
    result = await db.execute(select(Call).where(Call.id == call_id))
    call = result.scalar_one_or_none()

    if not call:
        raise HTTPException(status_code=404, detail="Call not found")

    # Проверяем, что пользователь является участником звонка
    all_participants = [call.initiator_jid] + call.participants
    if current_user_jid not in all_participants:
        raise HTTPException(status_code=403, detail="Not a participant of this call")

    # Обновляем статус звонка
    call.status = "rejected"
    await db.commit()

    # Уведомляем других участников
    from services.websocket_client import websocket_manager

    for participant_jid in all_participants:
        if participant_jid != current_user_jid:
            await websocket_manager.send_personal_message(
                participant_jid,
                {
                    "type": "call_rejected",
                    "call_id": call_id,
                    "rejected_by": current_user_jid,
                },
            )

    return {"success": True, "message": "Call rejected"}


@router.post("/{call_id}/end")
async def end_call(
    call_id: str,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid),
):
    """Завершение звонка"""
    result = await db.execute(select(Call).where(Call.id == call_id))
    call = result.scalar_one_or_none()

    if not call:
        raise HTTPException(status_code=404, detail="Call not found")

    # Обновляем статус звонка
    call.status = "ended"
    # TODO: Вычислить duration_seconds на основе started_at и ended_at

    await db.commit()

    # Уведомляем всех участников
    from services.websocket_client import websocket_manager

    all_participants = [call.initiator_jid] + call.participants
    for participant_jid in all_participants:
        await websocket_manager.send_personal_message(
            participant_jid,
            {
                "type": "call_ended",
                "call_id": call_id,
                "ended_by": current_user_jid,
            },
        )

    return {"success": True, "message": "Call ended"}
