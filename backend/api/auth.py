"""
API роутер для аутентификации
"""

import secrets
import uuid
from datetime import timedelta
from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from database.connection import get_db
from models.user import UserExtended
from schemas.user import UserCreate, UserLogin
from schemas.auth import OTPRequest, OTPVerify, OTPResponse, AuthResponse
from services.ejabberd import EjabberdService
from services.redis import RedisService
from services.otp import OTPService
from core.security import create_access_token
from core.config import settings

router = APIRouter()

# Инициализируем сервисы
ejabberd_service = EjabberdService()
otp_service = OTPService()


def get_redis_service() -> RedisService:
    from app import get_redis_service as get_global_redis

    return get_global_redis()


def generate_secure_password() -> str:
    """Генерация безопасного пароля для ejabberd"""
    return secrets.token_urlsafe(32)


@router.post(
    "/register",
    summary="Регистрация пользователя",
    description="""
    Регистрирует нового пользователя в системе.

    Создает учетную запись в eJabberd XMPP сервере и сохраняет
    дополнительную информацию в PostgreSQL.

    **Что происходит:**
    1. Проверяется уникальность username
    2. Создается пользователь в eJabberd
    3. Сохраняется профиль в нашей БД

    **Требования к паролю:**
    - Минимум 6 символов
    - Рекомендуется использовать буквы, цифры и спецсимволы
    """,
    responses={
        200: {"description": "Пользователь успешно зарегистрирован"},
        400: {"description": "Ошибка валидации или пользователь уже существует"},
        500: {"description": "Внутренняя ошибка сервера"},
    },
)
async def register_user(user_data: UserCreate, db: AsyncSession = Depends(get_db)):
    """Регистрация нового пользователя"""
    try:
        print(f"🔄 Starting registration for user: {user_data.username}")

        # Создаем пользователя в eJabberd
        print(f"🔄 Calling eJabberd service to register user: {user_data.username}")
        success = await ejabberd_service.register_user(
            user_data.username, user_data.password
        )

        print(f"📊 eJabberd registration result: {success}")
        if not success:
            error_msg = "Failed to register user in eJabberd"
            print(f"❌ {error_msg}")
            raise HTTPException(status_code=400, detail=error_msg)

        # Сохраняем дополнительную информацию в нашей БД
        print(f"🔄 Saving user data to database: {user_data.username}")
        user = UserExtended(
            username=user_data.username,
            display_name=user_data.display_name,
            email=user_data.email,
            phone=user_data.phone,
        )

        db.add(user)
        await db.commit()

        print(f"✅ User registered successfully: {user_data.username}")
        return {"success": True, "message": "User registered successfully"}

    except HTTPException:
        # Перебрасываем HTTP исключения как есть
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        error_msg = f"Registration error for {user_data.username}: {str(e)}"
        print(f"❌ {error_msg}")
        raise HTTPException(status_code=400, detail=error_msg)


@router.post(
    "/login",
    summary="Авторизация пользователя",
    description="""
    Авторизует пользователя и возвращает JWT токен с полной информацией о пользователе.

    **Процесс авторизации:**
    1. Проверяется пароль через eJabberd API
    2. Загружается профиль пользователя из БД
    3. Генерируется JWT токен с JID пользователя
    4. Обновляется статус "онлайн" в Redis

    **Полученный токен используйте в заголовке:**
    ```
    Authorization: Bearer <access_token>
    ```

    **Время жизни токена:** 24 часа (по умолчанию)
    """,
    responses={
        200: {
            "description": "Успешная авторизация",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                        "token_type": "bearer",
                        "user_id": "123",
                        "username": "john_doe",
                        "display_name": "John Doe",
                        "user_jid": "john_doe@localhost",
                        "avatar": None,
                        "status": "online",
                    }
                }
            },
        },
        401: {"description": "Неверные учетные данные"},
        400: {"description": "Ошибка валидации данных"},
    },
)
async def login_user(user_data: UserLogin, db: AsyncSession = Depends(get_db)):
    """Авторизация пользователя"""
    try:
        # Проверяем пароль через eJabberd
        success = await ejabberd_service.check_password(
            user_data.username, user_data.password
        )

        if not success:
            raise HTTPException(status_code=401, detail="Invalid credentials")

        # Получаем информацию о пользователе из БД
        result = await db.execute(
            select(UserExtended).where(UserExtended.username == user_data.username)
        )
        user = result.scalar_one_or_none()

        if not user:
            raise HTTPException(status_code=404, detail="User profile not found")

        # Создаем JWT токен
        user_jid = f"{user_data.username}@{settings.ejabberd_domain}"
        access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
        access_token = create_access_token(
            data={"sub": user_jid}, expires_delta=access_token_expires
        )

        # Обновляем статус в Redis
        redis_svc = RedisService()
        await redis_svc.set_user_status(user_jid, "online")

        return {
            "success": True,
            "access_token": access_token,
            "token_type": "bearer",
            "user_id": str(user.id),
            "username": user.username,
            "display_name": user.display_name,
            "user_jid": user_jid,
            "avatar": user.avatar_url,
            "status": "online",
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post(
    "/otp/request",
    response_model=OTPResponse,
    summary="Запрос OTP кода",
    description="""
    Отправляет OTP код на указанный email адрес.

    **Современная passwordless аутентификация:**
    - Не требует создания пароля
    - Безопасная одноразовая аутентификация
    - Объединяет регистрацию и вход в один процесс

    **Процесс:**
    1. Пользователь вводит email
    2. Система генерирует 6-значный OTP код
    3. В режиме разработки код выводится в консоль бекенда
    4. Код действителен 5 минут
    5. Максимум 5 попыток ввода

    **Безопасность:**
    - OTP коды хешируются перед сохранением в Redis
    - Автоматическое истечение через TTL
    - Защита от брутфорса через лимит попыток
    """,
    responses={
        200: {"description": "OTP код успешно отправлен"},
        400: {"description": "Ошибка валидации email"},
        500: {"description": "Внутренняя ошибка сервера"},
    },
)
async def request_otp(otp_request: OTPRequest):
    """Запрос OTP кода для аутентификации"""
    try:
        print(f"🔄 OTP request for email: {otp_request.email}")

        # Отправляем OTP код
        success = await otp_service.send_otp(otp_request.email)

        if not success:
            raise HTTPException(
                status_code=500,
                detail="Failed to send OTP code. Please try again."
            )

        return OTPResponse(
            success=True,
            message="OTP code sent to your email. Check the backend console in development mode."
        )

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Error in OTP request: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post(
    "/otp/verify",
    response_model=AuthResponse,
    summary="Верификация OTP и аутентификация",
    description="""
    Проверяет OTP код и выполняет аутентификацию пользователя.

    **Магический роутинг:**
    - Если пользователь с таким email существует → **Логин**
    - Если пользователя нет → **Автоматическая регистрация**

    **При регистрации (новый пользователь):**
    1. Создается запись в PostgreSQL с UUID как ID
    2. UUID используется как username для ejabberd
    3. Генерируется безопасный пароль для ejabberd
    4. Создается аккаунт в ejabberd через Admin API
    5. Возвращается JWT токен и XMPP креды

    **При логине (существующий пользователь):**
    1. Загружается профиль из PostgreSQL
    2. Извлекаются сохраненные XMPP креды
    3. Возвращается JWT токен и XMPP креды

    **Возвращаемые данные:**
    - JWT токен для API аутентификации
    - XMPP username и password для подключения к ejabberd
    - Профиль пользователя
    - Флаг is_new_user для определения регистрации/логина
    """,
    responses={
        200: {"description": "Успешная аутентификация"},
        400: {"description": "Неверный OTP код или ошибка валидации"},
        500: {"description": "Внутренняя ошибка сервера"},
    },
)
async def verify_otp(otp_verify: OTPVerify, db: AsyncSession = Depends(get_db)):
    """Верификация OTP кода и аутентификация"""
    try:
        print(f"🔄 OTP verification for email: {otp_verify.email}")

        # Проверяем OTP код
        verification_result = await otp_service.verify_otp(otp_verify.email, otp_verify.otp)

        if not verification_result["success"]:
            raise HTTPException(
                status_code=400,
                detail=verification_result["error"]
            )

        print(f"✅ OTP verified for {otp_verify.email}")

        # Ищем пользователя в базе данных
        result = await db.execute(
            select(UserExtended).where(UserExtended.email == otp_verify.email)
        )
        user = result.scalar_one_or_none()

        is_new_user = False

        if not user:
            # Новый пользователь - регистрация
            print(f"🔄 Creating new user for email: {otp_verify.email}")
            is_new_user = True

            # Генерируем UUID для username
            user_uuid = str(uuid.uuid4())
            xmpp_password = generate_secure_password()

            # Создаем пользователя в ejabberd
            ejabberd_success = await ejabberd_service.register_user(user_uuid, xmpp_password)

            if not ejabberd_success:
                raise HTTPException(
                    status_code=500,
                    detail="Failed to create XMPP account"
                )

            # Создаем запись в нашей БД
            user = UserExtended(
                username=user_uuid,
                email=otp_verify.email,
                xmpp_password=xmpp_password,
                display_name=otp_verify.email.split('@')[0],  # Используем часть email как display_name
                server=settings.ejabberd_domain
            )

            db.add(user)
            await db.commit()
            await db.refresh(user)

            print(f"✅ New user created: {user.email} -> {user.username}@{user.server}")

        else:
            # Существующий пользователь - логин
            print(f"✅ Existing user login: {user.email} -> {user.username}@{user.server}")

        # Создаем JWT токен
        user_jid = f"{user.username}@{user.server}"
        access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
        access_token = create_access_token(
            data={"sub": user_jid, "email": user.email},
            expires_delta=access_token_expires
        )

        # Обновляем статус в Redis
        redis_svc = get_redis_service()
        if redis_svc:
            await redis_svc.set_user_status(user_jid, "online")

        return AuthResponse(
            success=True,
            access_token=access_token,
            token_type="bearer",
            user_id=str(user.id),
            email=user.email,
            display_name=user.display_name,
            user_jid=user_jid,
            xmpp_username=user.username,
            xmpp_password=user.xmpp_password,
            avatar=user.avatar_url,
            status="online",
            is_new_user=is_new_user
        )

    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        print(f"❌ Error in OTP verification: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
