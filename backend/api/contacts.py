"""
API роутер для контактов
"""

from typing import List
from datetime import datetime
from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc

from database.connection import get_db
from models.contact import Contact, ChatHistory
from models.user import UserExtended
from schemas.contact import ContactInfo, ChatInfo, ContactAdd
from core.security import get_current_user_jid

router = APIRouter()


@router.get("/list", response_model=List[ContactInfo])
async def get_contacts(
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid),
):
    """Получение списка контактов пользователя"""
    try:
        result = await db.execute(
            select(Contact)
            .where(Contact.owner_jid == current_user_jid)
            .where(Contact.is_blocked == False)
            .order_by(desc(Contact.last_message_at))
        )
        contacts = result.scalars().all()

        return [ContactInfo.model_validate(contact) for contact in contacts]

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/add", response_model=dict)
async def add_contact(
    contact_data: ContactAdd,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid),
):
    """Добавление контакта"""
    try:
        # Проверяем существование пользователя
        result = await db.execute(
            select(UserExtended).where(UserExtended.username == contact_data.username)
        )
        user = result.scalar_one_or_none()

        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        contact_jid = f"{contact_data.username}@localhost"

        # Проверяем, что пользователь не добавляет сам себя
        if contact_jid == current_user_jid:
            raise HTTPException(
                status_code=400, detail="Cannot add yourself as contact"
            )

        # Проверяем, что контакт еще не добавлен
        existing = await db.execute(
            select(Contact).where(
                and_(
                    Contact.owner_jid == current_user_jid,
                    Contact.contact_jid == contact_jid,
                )
            )
        )
        if existing.scalar_one_or_none():
            raise HTTPException(status_code=409, detail="Contact already exists")

        # Добавляем контакт
        contact = Contact(
            owner_jid=current_user_jid,
            contact_jid=contact_jid,
            contact_username=contact_data.username,
            contact_display_name=user.display_name or contact_data.username,
        )

        db.add(contact)
        await db.commit()

        return {"success": True, "message": "Contact added successfully"}

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/chats", response_model=List[ChatInfo])
async def get_chats(
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid),
):
    """Получение списка чатов пользователя"""
    try:
        result = await db.execute(
            select(ChatHistory)
            .where(ChatHistory.user_jid == current_user_jid)
            .where(ChatHistory.is_archived == False)
            .order_by(desc(ChatHistory.updated_at))
        )
        chats = result.scalars().all()

        return [ChatInfo.model_validate(chat) for chat in chats]

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/chats/update")
async def update_chat(
    chat_jid: str,
    last_message_body: str = None,
    last_message_from: str = None,
    increment_unread: bool = False,
    mark_as_read: bool = False,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid),
):
    """Обновление информации о чате"""
    try:
        # Ищем существующий чат
        result = await db.execute(
            select(ChatHistory).where(
                and_(
                    ChatHistory.user_jid == current_user_jid,
                    ChatHistory.chat_jid == chat_jid,
                )
            )
        )
        chat = result.scalar_one_or_none()

        if not chat:
            # Создаем новый чат
            # Определяем имя чата
            chat_name = chat_jid.split("@")[0]
            if "@conference." in chat_jid:
                # Групповой чат
                chat_type = "group"
            else:
                # Личный чат - получаем имя пользователя
                chat_type = "direct"
                username = chat_jid.split("@")[0]
                user_result = await db.execute(
                    select(UserExtended).where(UserExtended.username == username)
                )
                user = user_result.scalar_one_or_none()
                if user:
                    chat_name = user.display_name or username

            chat = ChatHistory(
                user_jid=current_user_jid,
                chat_jid=chat_jid,
                chat_type=chat_type,
                chat_name=chat_name,
                unread_count=1 if increment_unread else 0,
                total_messages=1,
            )
            db.add(chat)
        else:
            # Обновляем существующий чат
            if increment_unread:
                chat.unread_count += 1
            if mark_as_read:
                chat.unread_count = 0

            chat.total_messages += 1

        # Обновляем информацию о последнем сообщении
        if last_message_body:
            chat.last_message_body = last_message_body
            chat.last_message_at = datetime.utcnow()
        if last_message_from:
            chat.last_message_from = last_message_from

        await db.commit()

        return {"success": True, "message": "Chat updated"}

    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/chats/{chat_jid}/mark-read")
async def mark_chat_as_read(
    chat_jid: str,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid),
):
    """Отметить чат как прочитанный"""
    try:
        result = await db.execute(
            select(ChatHistory).where(
                and_(
                    ChatHistory.user_jid == current_user_jid,
                    ChatHistory.chat_jid == chat_jid,
                )
            )
        )
        chat = result.scalar_one_or_none()

        if chat:
            chat.unread_count = 0
            await db.commit()

        return {"success": True, "message": "Chat marked as read"}

    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/contacts/{contact_jid}")
async def remove_contact(
    contact_jid: str,
    db: AsyncSession = Depends(get_db),
    current_user_jid: str = Depends(get_current_user_jid),
):
    """Удаление контакта"""
    try:
        result = await db.execute(
            select(Contact).where(
                and_(
                    Contact.owner_jid == current_user_jid,
                    Contact.contact_jid == contact_jid,
                )
            )
        )
        contact = result.scalar_one_or_none()

        if not contact:
            raise HTTPException(status_code=404, detail="Contact not found")

        await db.delete(contact)
        await db.commit()

        return {"success": True, "message": "Contact removed"}

    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))
