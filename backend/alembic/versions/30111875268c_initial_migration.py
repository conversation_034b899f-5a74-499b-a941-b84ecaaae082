"""Initial migration

Revision ID: 30111875268c
Revises:
Create Date: 2025-09-04 19:11:45.285168

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "30111875268c"
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Создаем расширение UUID
    op.execute('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"')

    # Таблица расширенной информации о пользователях
    op.create_table(
        "users_extended",
        sa.Column(
            "id",
            postgresql.UUID(as_uuid=True),
            primary_key=True,
            server_default=sa.text("uuid_generate_v4()"),
        ),
        sa.Column("username", sa.String(255), nullable=False),
        sa.Column("server", sa.String(255), nullable=False, server_default="localhost"),
        sa.Column("display_name", sa.String(255)),
        sa.Column("avatar_url", sa.Text()),
        sa.Column("phone", sa.String(20)),
        sa.Column("email", sa.String(255)),
        sa.Column("status", sa.String(50), server_default="offline"),
        sa.Column(
            "last_seen", sa.DateTime(timezone=True), server_default=sa.func.now()
        ),
        sa.Column(
            "created_at", sa.DateTime(timezone=True), server_default=sa.func.now()
        ),
        sa.Column(
            "updated_at", sa.DateTime(timezone=True), server_default=sa.func.now()
        ),
        sa.UniqueConstraint("username", "server", name="uq_username_server"),
    )

    # Таблица файлов
    op.create_table(
        "files",
        sa.Column(
            "id",
            postgresql.UUID(as_uuid=True),
            primary_key=True,
            server_default=sa.text("uuid_generate_v4()"),
        ),
        sa.Column("filename", sa.String(255), nullable=False),
        sa.Column("original_name", sa.String(255), nullable=False),
        sa.Column("content_type", sa.String(100)),
        sa.Column("size_bytes", sa.BigInteger()),
        sa.Column("s3_key", sa.String(500), nullable=False),
        sa.Column("s3_bucket", sa.String(100), server_default="messenger-files"),
        sa.Column("uploaded_by", sa.String(255), nullable=False),
        sa.Column(
            "created_at", sa.DateTime(timezone=True), server_default=sa.func.now()
        ),
    )

    # Таблица групповых чатов
    op.create_table(
        "group_chats",
        sa.Column(
            "id",
            postgresql.UUID(as_uuid=True),
            primary_key=True,
            server_default=sa.text("uuid_generate_v4()"),
        ),
        sa.Column("jid", sa.String(255), nullable=False, unique=True),
        sa.Column("name", sa.String(255), nullable=False),
        sa.Column("description", sa.Text()),
        sa.Column("avatar_url", sa.Text()),
        sa.Column("created_by", sa.String(255), nullable=False),
        sa.Column("max_members", sa.Integer(), server_default="500"),
        sa.Column("is_public", sa.Boolean(), server_default="true"),
        sa.Column(
            "created_at", sa.DateTime(timezone=True), server_default=sa.func.now()
        ),
        sa.Column(
            "updated_at", sa.DateTime(timezone=True), server_default=sa.func.now()
        ),
    )

    # Таблица участников групп
    op.create_table(
        "group_members",
        sa.Column(
            "id",
            postgresql.UUID(as_uuid=True),
            primary_key=True,
            server_default=sa.text("uuid_generate_v4()"),
        ),
        sa.Column(
            "group_id",
            postgresql.UUID(as_uuid=True),
            sa.ForeignKey("group_chats.id", ondelete="CASCADE"),
        ),
        sa.Column("user_jid", sa.String(255), nullable=False),
        sa.Column("role", sa.String(20), server_default="member"),
        sa.Column(
            "joined_at", sa.DateTime(timezone=True), server_default=sa.func.now()
        ),
        sa.UniqueConstraint("group_id", "user_jid"),
    )

    # Таблица звонков
    op.create_table(
        "calls",
        sa.Column(
            "id",
            postgresql.UUID(as_uuid=True),
            primary_key=True,
            server_default=sa.text("uuid_generate_v4()"),
        ),
        sa.Column("livekit_room_id", sa.String(255), nullable=False),
        sa.Column("call_type", sa.String(20), nullable=False),
        sa.Column("initiator_jid", sa.String(255), nullable=False),
        sa.Column("participants", postgresql.JSONB(), server_default="'[]'"),
        sa.Column("status", sa.String(20), server_default="initiated"),
        sa.Column(
            "started_at", sa.DateTime(timezone=True), server_default=sa.func.now()
        ),
        sa.Column("ended_at", sa.DateTime(timezone=True)),
        sa.Column("duration_seconds", sa.Integer(), server_default="0"),
    )

    # Таблица уведомлений
    op.create_table(
        "notifications",
        sa.Column(
            "id",
            postgresql.UUID(as_uuid=True),
            primary_key=True,
            server_default=sa.text("uuid_generate_v4()"),
        ),
        sa.Column("user_jid", sa.String(255), nullable=False),
        sa.Column("type", sa.String(50), nullable=False),
        sa.Column("title", sa.String(255)),
        sa.Column("body", sa.Text()),
        sa.Column("data", postgresql.JSONB(), server_default="'{}'"),
        sa.Column("is_read", sa.Boolean(), server_default="false"),
        sa.Column(
            "created_at", sa.DateTime(timezone=True), server_default=sa.func.now()
        ),
    )

    # Таблица реакций на сообщения
    op.create_table(
        "message_reactions",
        sa.Column(
            "id",
            postgresql.UUID(as_uuid=True),
            primary_key=True,
            server_default=sa.text("uuid_generate_v4()"),
        ),
        sa.Column("message_id", sa.String(255), nullable=False),
        sa.Column("user_jid", sa.String(255), nullable=False),
        sa.Column("reaction", sa.String(50), nullable=False),
        sa.Column(
            "created_at", sa.DateTime(timezone=True), server_default=sa.func.now()
        ),
        sa.UniqueConstraint("message_id", "user_jid", "reaction"),
    )

    # Таблица тредов сообщений
    op.create_table(
        "message_threads",
        sa.Column(
            "id",
            postgresql.UUID(as_uuid=True),
            primary_key=True,
            server_default=sa.text("uuid_generate_v4()"),
        ),
        sa.Column("parent_message_id", sa.String(255), nullable=False),
        sa.Column("thread_message_id", sa.String(255), nullable=False),
        sa.Column(
            "created_at", sa.DateTime(timezone=True), server_default=sa.func.now()
        ),
        sa.UniqueConstraint("parent_message_id", "thread_message_id"),
    )

    # Таблица статусов сообщений
    op.create_table(
        "message_statuses",
        sa.Column(
            "id",
            postgresql.UUID(as_uuid=True),
            primary_key=True,
            server_default=sa.text("uuid_generate_v4()"),
        ),
        sa.Column("message_id", sa.String(255), nullable=False),
        sa.Column("user_jid", sa.String(255), nullable=False),
        sa.Column("status", sa.String(20), nullable=False),
        sa.Column(
            "timestamp", sa.DateTime(timezone=True), server_default=sa.func.now()
        ),
        sa.UniqueConstraint("message_id", "user_jid"),
    )

    # Таблица каналов
    op.create_table(
        "channels",
        sa.Column(
            "id",
            postgresql.UUID(as_uuid=True),
            primary_key=True,
            server_default=sa.text("uuid_generate_v4()"),
        ),
        sa.Column("jid", sa.String(255), nullable=False, unique=True),
        sa.Column("name", sa.String(255), nullable=False),
        sa.Column("description", sa.Text()),
        sa.Column("avatar_url", sa.Text()),
        sa.Column("created_by", sa.String(255), nullable=False),
        sa.Column("is_public", sa.Boolean(), server_default="true"),
        sa.Column("subscriber_count", sa.Integer(), server_default="0"),
        sa.Column(
            "created_at", sa.DateTime(timezone=True), server_default=sa.func.now()
        ),
        sa.Column(
            "updated_at", sa.DateTime(timezone=True), server_default=sa.func.now()
        ),
    )

    # Таблица подписчиков каналов
    op.create_table(
        "channel_subscribers",
        sa.Column(
            "id",
            postgresql.UUID(as_uuid=True),
            primary_key=True,
            server_default=sa.text("uuid_generate_v4()"),
        ),
        sa.Column(
            "channel_id",
            postgresql.UUID(as_uuid=True),
            sa.ForeignKey("channels.id", ondelete="CASCADE"),
        ),
        sa.Column("user_jid", sa.String(255), nullable=False),
        sa.Column(
            "subscribed_at", sa.DateTime(timezone=True), server_default=sa.func.now()
        ),
        sa.UniqueConstraint("channel_id", "user_jid"),
    )

    # Создаем индексы для производительности
    op.create_index(
        "idx_users_extended_username_server", "users_extended", ["username", "server"]
    )
    op.create_index("idx_files_uploaded_by", "files", ["uploaded_by"])
    op.create_index("idx_group_members_user_jid", "group_members", ["user_jid"])
    op.create_index("idx_calls_initiator_jid", "calls", ["initiator_jid"])
    op.create_index("idx_notifications_user_jid", "notifications", ["user_jid"])
    op.create_index("idx_notifications_is_read", "notifications", ["is_read"])
    op.create_index(
        "idx_message_reactions_message_id", "message_reactions", ["message_id"]
    )
    op.create_index(
        "idx_message_statuses_message_id", "message_statuses", ["message_id"]
    )
    op.create_index(
        "idx_channel_subscribers_user_jid", "channel_subscribers", ["user_jid"]
    )


def downgrade() -> None:
    # Удаляем индексы
    op.drop_index("idx_channel_subscribers_user_jid")
    op.drop_index("idx_message_statuses_message_id")
    op.drop_index("idx_message_reactions_message_id")
    op.drop_index("idx_notifications_is_read")
    op.drop_index("idx_notifications_user_jid")
    op.drop_index("idx_calls_initiator_jid")
    op.drop_index("idx_group_members_user_jid")
    op.drop_index("idx_files_uploaded_by")
    op.drop_index("idx_users_extended_username_server")

    # Удаляем таблицы в обратном порядке
    op.drop_table("channel_subscribers")
    op.drop_table("channels")
    op.drop_table("message_statuses")
    op.drop_table("message_threads")
    op.drop_table("message_reactions")
    op.drop_table("notifications")
    op.drop_table("calls")
    op.drop_table("group_members")
    op.drop_table("group_chats")
    op.drop_table("files")
    op.drop_table("users_extended")
