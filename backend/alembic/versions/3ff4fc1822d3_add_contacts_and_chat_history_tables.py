"""Add contacts and chat_history tables

Revision ID: 3ff4fc1822d3
Revises: d02db7e614a5
Create Date: 2025-09-08 18:05:39.274792

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = "3ff4fc1822d3"
down_revision: Union[str, None] = "d02db7e614a5"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create contacts table
    op.create_table(
        "contacts",
        sa.Column(
            "id",
            sa.UUID(),
            server_default=sa.text("uuid_generate_v4()"),
            nullable=False,
        ),
        sa.Column("owner_jid", sa.String(length=255), nullable=False),
        sa.Column("contact_jid", sa.String(length=255), nullable=False),
        sa.Column("contact_username", sa.String(length=100), nullable=False),
        sa.Column("contact_display_name", sa.String(length=255), nullable=False),
        sa.Column("added_at", sa.DateTime(), nullable=False),
        sa.Column("last_message_at", sa.DateTime(), nullable=True),
        sa.Column("last_message_preview", sa.Text(), nullable=True),
        sa.Column("unread_count", sa.Integer(), nullable=False, server_default="0"),
        sa.Column("is_pinned", sa.Boolean(), nullable=False, server_default="false"),
        sa.Column("is_muted", sa.Boolean(), nullable=False, server_default="false"),
        sa.Column("is_blocked", sa.Boolean(), nullable=False, server_default="false"),
        sa.PrimaryKeyConstraint("id"),
    )

    # Create indexes for contacts
    op.create_index("ix_contacts_owner_jid", "contacts", ["owner_jid"])
    op.create_index("ix_contacts_contact_jid", "contacts", ["contact_jid"])
    op.create_index(
        "idx_contacts_owner_contact", "contacts", ["owner_jid", "contact_jid"]
    )
    op.create_index(
        "idx_contacts_owner_last_message", "contacts", ["owner_jid", "last_message_at"]
    )

    # Create chat_history table
    op.create_table(
        "chat_history",
        sa.Column(
            "id",
            sa.UUID(),
            server_default=sa.text("uuid_generate_v4()"),
            nullable=False,
        ),
        sa.Column("user_jid", sa.String(length=255), nullable=False),
        sa.Column("chat_jid", sa.String(length=255), nullable=False),
        sa.Column(
            "chat_type", sa.String(length=20), nullable=False, server_default="direct"
        ),
        sa.Column("chat_name", sa.String(length=255), nullable=False),
        sa.Column("chat_avatar", sa.String(length=500), nullable=True),
        sa.Column("last_message_id", sa.String(length=255), nullable=True),
        sa.Column("last_message_body", sa.Text(), nullable=True),
        sa.Column("last_message_at", sa.DateTime(), nullable=True),
        sa.Column("last_message_from", sa.String(length=255), nullable=True),
        sa.Column("unread_count", sa.Integer(), nullable=False, server_default="0"),
        sa.Column("total_messages", sa.Integer(), nullable=False, server_default="0"),
        sa.Column("is_pinned", sa.Boolean(), nullable=False, server_default="false"),
        sa.Column("is_muted", sa.Boolean(), nullable=False, server_default="false"),
        sa.Column("is_archived", sa.Boolean(), nullable=False, server_default="false"),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )

    # Create indexes for chat_history
    op.create_index("ix_chat_history_user_jid", "chat_history", ["user_jid"])
    op.create_index("ix_chat_history_chat_jid", "chat_history", ["chat_jid"])
    op.create_index(
        "idx_chat_history_user_chat", "chat_history", ["user_jid", "chat_jid"]
    )
    op.create_index(
        "idx_chat_history_user_updated", "chat_history", ["user_jid", "updated_at"]
    )


def downgrade() -> None:
    # Drop chat_history table and indexes
    op.drop_index("idx_chat_history_user_updated", table_name="chat_history")
    op.drop_index("idx_chat_history_user_chat", table_name="chat_history")
    op.drop_index("ix_chat_history_chat_jid", table_name="chat_history")
    op.drop_index("ix_chat_history_user_jid", table_name="chat_history")
    op.drop_table("chat_history")

    # Drop contacts table and indexes
    op.drop_index("idx_contacts_owner_last_message", table_name="contacts")
    op.drop_index("idx_contacts_owner_contact", table_name="contacts")
    op.drop_index("ix_contacts_contact_jid", table_name="contacts")
    op.drop_index("ix_contacts_owner_jid", table_name="contacts")
    op.drop_table("contacts")
