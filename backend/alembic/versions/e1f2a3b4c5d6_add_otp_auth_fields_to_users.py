"""Add OTP auth fields to users

Revision ID: e1f2a3b4c5d6
Revises: d02db7e614a5
Create Date: 2025-09-09 12:00:00.000000

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = "e1f2a3b4c5d6"
down_revision: Union[str, None] = "d02db7e614a5"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Добавляем новые поля для OTP аутентификации
    
    # 1. Делаем email обязательным и уникальным
    op.alter_column('users_extended', 'email',
                   existing_type=sa.String(255),
                   nullable=False)
    
    op.create_unique_constraint('uq_users_extended_email', 'users_extended', ['email'])
    
    # 2. Добавляем поле для хранения XMPP пароля
    op.add_column('users_extended', 
                  sa.Column('xmpp_password', sa.String(255), nullable=False, 
                           server_default='temp_password'))
    
    # 3. Добавляем поле для публичного username (@username)
    op.add_column('users_extended', 
                  sa.Column('public_username', sa.String(255), nullable=True))
    
    op.create_unique_constraint('uq_users_extended_public_username', 'users_extended', ['public_username'])
    
    # 4. Создаем индексы для производительности
    op.create_index('idx_users_extended_email', 'users_extended', ['email'])
    op.create_index('idx_users_extended_public_username', 'users_extended', ['public_username'])
    
    # 5. Удаляем временное значение по умолчанию
    op.alter_column('users_extended', 'xmpp_password', server_default=None)


def downgrade() -> None:
    # Удаляем индексы
    op.drop_index('idx_users_extended_public_username')
    op.drop_index('idx_users_extended_email')
    
    # Удаляем ограничения уникальности
    op.drop_constraint('uq_users_extended_public_username', 'users_extended', type_='unique')
    op.drop_constraint('uq_users_extended_email', 'users_extended', type_='unique')
    
    # Удаляем новые колонки
    op.drop_column('users_extended', 'public_username')
    op.drop_column('users_extended', 'xmpp_password')
    
    # Возвращаем email как nullable
    op.alter_column('users_extended', 'email',
                   existing_type=sa.String(255),
                   nullable=True)
