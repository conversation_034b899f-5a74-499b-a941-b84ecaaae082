"""
Главный файл FastAPI приложения
"""

import asyncio
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware

from core.config import settings
from database.connection import init_db, close_db
from services.redis import RedisService
from services.ejabberd_monitor import (
    start_ejabberd_monitoring,
    stop_ejabberd_monitoring,
)
from api import api_router
# Импортируем новые модели для создания таблиц

# Глобальные сервисы
redis_service = RedisService()

# Глобальная переменная для доступа к Redis сервису
_global_redis_service = None


# Dependency для получения Redis сервиса
def get_redis_service():
    global _global_redis_service
    return _global_redis_service


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Управление жизненным циклом приложения"""
    global _global_redis_service

    # Startup
    # Инициализируем базу данных
    await init_db()

    # Подключаемся к Redis
    await redis_service.connect()
    _global_redis_service = redis_service

    # Проверяем подключение к Redis
    if redis_service.redis:
        print("✅ Redis service initialized successfully")
    else:
        print("❌ Redis service failed to initialize")

    # Запускаем мониторинг eJabberd в фоне
    monitoring_task = asyncio.create_task(start_ejabberd_monitoring())
    print("👁️ eJabberd monitoring started")

    yield

    # Останавливаем мониторинг
    monitoring_task.cancel()
    await stop_ejabberd_monitoring()
    print("👁️ eJabberd monitoring stopped")

    # Shutdown
    # Закрываем соединение с базой данных
    await close_db()

    # Отключаемся от Redis
    await redis_service.disconnect()


# Создаем приложение
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    debug=settings.debug,
    lifespan=lifespan,
    description="""
    🚀 **Messenger Backend API**

    Полнофункциональный бекенд мессенджера на базе eJabberd (XMPP) + LiveKit (звонки) + FastAPI.

    ## Основные возможности:
    - 🔐 Регистрация и авторизация (JWT)
    - 💬 Отправка сообщений через XMPP
    - 👥 Групповые чаты и каналы
    - 📞 Аудио/видео звонки через LiveKit
    - 📁 Загрузка файлов в S3
    - 🔔 Push уведомления
    - 📊 Статусы сообщений и реакции
    - 🔍 Поиск пользователей

    ## Аутентификация:
    Используйте Bearer токен в заголовке Authorization: `Bearer <your_jwt_token>`

    ## Полезные ссылки:
    - [GitHub Repository](https://github.com/your-repo)
    - [eJabberd Admin](http://localhost:5280/admin) (admin/admin123)
    - [MinIO Console](http://localhost:9001) (minioadmin/minioadmin123)
    """,
    contact={
        "name": "Messenger API Support",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT",
        "url": "https://opensource.org/licenses/MIT",
    },
    servers=[{"url": "http://localhost:8000", "description": "Development server"}],
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Подключаем API роутеры
app.include_router(api_router)


# Дополнительные эндпойнты
@app.get(
    "/api/health",
    summary="Проверка состояния API",
    description="""
    Проверяет работоспособность API и подключенных сервисов.

    **Проверяемые компоненты:**
    - PostgreSQL база данных
    - Redis кэш
    - MinIO S3 хранилище

    Возвращает статус каждого сервиса и общее состояние системы.
    """,
    tags=["System"],
    responses={
        200: {
            "description": "Все сервисы работают нормально",
            "content": {
                "application/json": {
                    "example": {
                        "status": "healthy",
                        "services": {"database": "ok", "redis": "ok", "s3": "ok"},
                    }
                }
            },
        },
        503: {"description": "Один или несколько сервисов недоступны"},
    },
)
async def health_check():
    """Проверка состояния сервиса"""
    try:
        # TODO: Добавить проверки подключений к сервисам
        return {
            "status": "healthy",
            "services": {"database": "ok", "redis": "ok", "s3": "ok"},
        }
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Service unhealthy: {str(e)}")


@app.get(
    "/api/stats",
    summary="Общая статистика системы",
    description="""
    Возвращает общую статистику использования мессенджера.

    **Включает:**
    - Общее количество пользователей
    - Количество групповых чатов
    - Загруженные файлы
    - Проведенные звонки

    Полезно для мониторинга и аналитики.
    """,
    tags=["System"],
    responses={
        200: {
            "description": "Статистика системы",
            "content": {
                "application/json": {
                    "example": {
                        "global": {
                            "total_users": 1000,
                            "total_groups": 50,
                            "total_files": 2500,
                            "total_calls": 150,
                        }
                    }
                }
            },
        }
    },
)
async def get_stats():
    """Получение общей статистики"""
    # TODO: Реализовать получение статистики
    return {
        "global": {
            "total_users": 0,
            "total_groups": 0,
            "total_files": 0,
            "total_calls": 0,
        }
    }


# Webhook для LiveKit
@app.post("/webhooks/livekit")
async def livekit_webhook(request_data: dict):
    """Webhook для обработки событий LiveKit"""
    try:
        from sqlalchemy import select
        from database.connection import AsyncSessionLocal
        from models.call import Call
        from datetime import datetime

        event_type = request_data.get("event")
        room_name = request_data.get("room", {}).get("name")

        if not room_name:
            return {"success": True}

        print(f"LiveKit webhook: {event_type} for room {room_name}")

        async with AsyncSessionLocal() as db:
            # Находим звонок по room_name
            result = await db.execute(
                select(Call).where(Call.livekit_room_id == room_name)
            )
            call = result.scalar_one_or_none()

            if not call:
                print(f"Call not found for room: {room_name}")
                return {"success": True}

            # Обрабатываем различные события
            if event_type == "room_started":
                call.status = "active"
                print(f"Call {call.id} started")

            elif event_type == "room_finished":
                call.status = "ended"
                call.ended_at = datetime.utcnow()

                # Вычисляем длительность
                if call.started_at and call.ended_at:
                    duration = call.ended_at - call.started_at
                    call.duration_seconds = int(duration.total_seconds())

                print(f"Call {call.id} ended, duration: {call.duration_seconds}s")

            elif event_type == "participant_joined":
                participant = request_data.get("participant", {})
                print(
                    f"Participant {participant.get('identity')} joined call {call.id}"
                )

            elif event_type == "participant_left":
                participant = request_data.get("participant", {})
                print(f"Participant {participant.get('identity')} left call {call.id}")

            await db.commit()

        return {"success": True}
    except Exception as e:
        print(f"LiveKit webhook error: {e}")
        return {"success": False, "error": str(e)}


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8000)
