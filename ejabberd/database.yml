# Конфигурация базы данных для eJabberd
# Этот файл подключается к основному ejabberd.yml

# Настройки подключения к PostgreSQL
sql_type: pgsql
sql_server: postgres
sql_database: messenger
sql_username: postgres
sql_password: postgres123
sql_port: 5432

# Пул соединений
sql_pool_size: 10
sql_keepalive_interval: 60

# Настройки для новых пользователей
new_sql_schema: true

# Кэширование
use_cache: true
cache_size: 1000
cache_missed: true
cache_life_time: 3600